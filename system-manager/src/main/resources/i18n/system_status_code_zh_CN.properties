0=操作成功
1001=参数错误
1002=参数错误
1003=键重复
1004=不支持的操作
1005=数据库操作异常
1006=无效的参数
1007=RPC调用异常
1008=文件格式错误
400=操作失败
404=HTTP 404 Not Found
405=Request-method不支持
500=系统错误
601=请求体格式错误
602=网络错误
900=登录令牌已失效，请重新登录
902=暂无操作权限
1050=认证请求头为空或无效
1051=uac验证code失败
1052=uac参数校验失败
1053=uac账户或密码错误
1054=uac token校验失败
1055=uac授权码获取token信息失败
1056=获取uac数据失败
1057=请求uac失败
1059=请重新登录uac

1701=父级部门未找到
1702=部门不存在
1703=组织/科室名称已存在
1704=当前部门下存在子部门，不可删除
1705=当前部门下存在部门成员信息，不可删除
1706=导入数据大于了500条
1707=部门/科室/团队已被其他业务关联，不允许删除
1708=用户已存在其他部门



1750=评价功能点的functionid不能为空
1751=评价内容不能超过200字
1752=评价操作类型不能为空


1790=页面访问类型不能为空
1791=页面访问资源id不能为空
1792=页面访问产品小类id不能为空