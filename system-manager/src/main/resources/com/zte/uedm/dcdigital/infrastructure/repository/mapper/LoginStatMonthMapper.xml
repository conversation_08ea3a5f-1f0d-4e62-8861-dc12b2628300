<?xml version="1.0" encoding="UTF-8"?>
<!-- Started by AICoder, pid:r48e6t952ewf7d3143a9096c106ed14c3f78a586 -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.LoginStatMonthMapper">

    <resultMap id="LoginStatMonthResult" type="com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatMonthPo">
        <id property="id" column="id"/>
        <result property="day" column="day"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="num" column="num"/>
    </resultMap>

    <sql id="selectLoginStatMonthVo">
        SELECT id, day, dept_id, user_id, num
        FROM login_stat_month
    </sql>

    <insert id="insertLoginStatMonth" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatMonthPo">
        INSERT INTO login_stat_month (id, day, dept_id, user_id, num)
        VALUES (#{id}, #{day}, #{deptId}, #{userId}, #{num})
    </insert>

    <insert id="batchInsertLoginStatMonth">
        INSERT INTO login_stat_month (id, day, dept_id, user_id, num)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.day}, #{item.deptId}, #{item.userId}, #{item.num})
        </foreach>
    </insert>

    <update id="batchUpdateLoginStatMonth">
        UPDATE login_stat_month
        SET
        num = CASE id
        <foreach collection="list" item="item" separator=" ">
            WHEN #{item.id} THEN #{item.num}
        </foreach>
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="selectLoginStatMonthList"
            parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatMonthPo"
            resultMap="LoginStatMonthResult">
        <include refid="selectLoginStatMonthVo"/>
        <where>
            <if test="day != null">AND day = #{day}</if>
            <if test="deptId != null and deptId != ''">AND dept_id = #{deptId}</if>
            <if test="userId != null and userId != ''">AND user_id = #{userId}</if>
            <if test="beginTime != null and endTime != null">
                AND day BETWEEN #{beginTime} AND #{endTime}
            </if>
        </where>
    </select>

    <delete id="deleteLoginStatMonthByIds" parameterType="String">
        DELETE FROM login_stat_month
        WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByMonthNumber">
        DELETE FROM login_stat_month
        WHERE day = #{monthNumber}
    </delete>

    <select id="selectLoginStatMonthById"
            parameterType="String"
            resultMap="LoginStatMonthResult">
        <include refid="selectLoginStatMonthVo"/>
        WHERE id = #{id}
    </select>
</mapper>

        <!-- Ended by AICoder, pid:r48e6t952ewf7d3143a9096c106ed14c3f78a586 -->