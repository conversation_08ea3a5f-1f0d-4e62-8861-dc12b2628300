<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.DocAssetWeekMapper">

    <select id="getDocNumData"
            resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetEntity">
        SELECT day, SUM(all_num) AS all_num, SUM(change_num) AS change_num, SUM(update_num) AS update_num
        FROM asset_document_week
        <where>
            <if test="beginTime != null and endTime != null">
                AND day BETWEEN #{beginTime} AND #{endTime}
            </if>
            <if test="ids != null and ids.size() > 0">
                AND product_category_id IN
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY day
    </select>

    <select id="getDataByTime"
            resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetEntity">
        SELECT id, day, product_category_id, all_num, change_num, update_num, create_time
        FROM asset_document_week where day = #{day}
    </select>

    <select id="getBeforeWeekEntity" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetEntity">
        SELECT id, day, product_category_id, all_num, change_num, update_num, create_time FROM asset_document_week
        WHERE day = #{beforeWeek}
        <if test="id != null">
            and product_category_id = #{id}
        </if>
        <if test="id == null">
            and product_category_id IS NULL
        </if>
    </select>

    <insert id="batchInsert">
        INSERT INTO asset_document_week (id, day, product_category_id, all_num, change_num, update_num, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.day}, #{item.productCategoryId}, #{item.allNum}, #{item.changeNum}, #{item.updateNum}, #{item.createTime})
        </foreach>
    </insert>

    <update id="batchUpdate">
        UPDATE asset_document_week
        SET
        all_num = CASE
        <foreach collection="list" item="item" separator=" ">
            WHEN id = #{item.id} THEN #{item.allNum}
        </foreach>
        ELSE all_num
        END,
        change_num = CASE
        <foreach collection="list" item="item" separator=" ">
            WHEN id = #{item.id} THEN #{item.changeNum}
        </foreach>
        ELSE change_num
        END,
        update_num = CASE
        <foreach collection="list" item="item" separator=" ">
            WHEN id = #{item.id} THEN #{item.updateNum}
        </foreach>
        ELSE update_num
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
    </update>

    <select id="getDataByDayAndIds" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetEntity">
        SELECT id, day, product_category_id, all_num, change_num, update_num, create_time FROM asset_document_week
        WHERE day = #{day}
        <if test="ids != null and ids.size() > 0">
            AND product_category_id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetEntity">
        SELECT day, SUM(all_num) AS all_num, SUM(change_num) AS change_num, SUM(update_num) AS update_num FROM asset_document_week
        WHERE day BETWEEN #{startTime} AND #{endTime}
        GROUP BY day
        ORDER BY day DESC
    </select>

</mapper>
