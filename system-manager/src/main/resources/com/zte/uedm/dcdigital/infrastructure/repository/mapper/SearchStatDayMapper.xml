<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.SearchStatDayMapper">

    <!-- 根据用户ID、日期和产品小类ID查询统计记录 -->
    <select id="selectByUserAndDayAndCategory" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.SearchStatDayPo">
        SELECT id, day, dept_id, user_id, product_category_id, material_num, file_num, faq_num, create_time
        FROM search_stat_day
        WHERE user_id = #{userId}
          AND day = #{day}
          AND product_category_id = #{productCategoryId}
        LIMIT 1
    </select>

    <!-- 更新物料搜索次数 -->
    <update id="updateMaterialNum">
        UPDATE search_stat_day
        SET material_num = CAST(material_num AS INTEGER) + 1
        WHERE user_id = #{userId}
          AND day = #{day}
          AND product_category_id = #{productCategoryId}
    </update>

    <!-- 更新文档搜索次数 -->
    <update id="updateFileNum">
        UPDATE search_stat_day
        SET file_num = CAST(file_num AS INTEGER) + 1
        WHERE user_id = #{userId}
          AND day = #{day}
          AND product_category_id = #{productCategoryId}
    </update>

    <!-- 更新FAQ搜索次数 -->
    <update id="updateFaqNum">
        UPDATE search_stat_day
        SET faq_num = CAST(faq_num AS INTEGER) + 1
        WHERE user_id = #{userId}
          AND day = #{day}
          AND product_category_id = #{productCategoryId}
    </update>

    <!-- 根据日期范围汇聚统计数据 -->
    <select id="aggregateByDateRange" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.SearchStatDayPo">
        SELECT
            dept_id,
            user_id,
            product_category_id,
            SUM(CAST(material_num AS INTEGER)) as material_num,
            SUM(CAST(file_num AS INTEGER)) as file_num,
            SUM(CAST(faq_num AS INTEGER)) as faq_num
        FROM search_stat_day
        WHERE day >= #{startDay} AND day &lt;= #{endDay}
        GROUP BY dept_id, user_id, product_category_id
    </select>

    <!-- 根据用户ID列表和时间范围查询统计数据 -->
    <select id="selectByUserListAndTimeRange" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.SearchStatDayPo">
        SELECT id, day, dept_id, user_id, product_category_id, material_num, file_num, faq_num, create_time
        FROM search_stat_day
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        <if test="type != null and type != 0 and type != 1">
            <choose>
                <when test="type == 2">
                    AND CAST(material_num AS INTEGER) > 0
                </when>
                <when test="type == 3">
                    AND CAST(file_num AS INTEGER) > 0
                </when>
                <when test="type == 4">
                    AND CAST(faq_num AS INTEGER) > 0
                </when>
            </choose>
        </if>
    </select>

    <!-- 根据用户ID列表和时间范围查询汇总统计数据 -->
    <select id="selectSummaryByUserListAndTimeRange" resultType="map">
        SELECT
            SUM(CAST(material_num AS INTEGER)) as totalMaterial,
            SUM(CAST(file_num AS INTEGER)) as totalFile,
            SUM(CAST(faq_num AS INTEGER)) as totalFaq,
            SUM(CAST(material_num AS INTEGER) + CAST(file_num AS INTEGER) + CAST(faq_num AS INTEGER)) as total,
            COUNT(DISTINCT user_id) as userCount
        FROM search_stat_day
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        <if test="type != null and type != 0 and type != 1">
            <choose>
                <when test="type == 2">
                    AND CAST(material_num AS INTEGER) > 0
                </when>
                <when test="type == 3">
                    AND CAST(file_num AS INTEGER) > 0
                </when>
                <when test="type == 4">
                    AND CAST(faq_num AS INTEGER) > 0
                </when>
            </choose>
        </if>
    </select>

    <!-- 根据用户ID列表和时间范围查询用户统计数据 -->
    <select id="selectUserStatByUserListAndTimeRange" resultType="map">
        SELECT
            user_id as userId,
            SUM(CAST(material_num AS INTEGER) + CAST(file_num AS INTEGER) + CAST(faq_num AS INTEGER)) as num,
            day
        FROM search_stat_day
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        <if test="type != null and type != 0 and type != 1">
            <choose>
                <when test="type == 2">
                    AND CAST(material_num AS INTEGER) > 0
                </when>
                <when test="type == 3">
                    AND CAST(file_num AS INTEGER) > 0
                </when>
                <when test="type == 4">
                    AND CAST(faq_num AS INTEGER) > 0
                </when>
            </choose>
        </if>
        GROUP BY user_id, day
        ORDER BY num DESC
    </select>

    <!-- 根据用户ID列表和时间范围查询分类TOP10数据 -->
    <select id="selectCategoryTopByUserListAndTimeRange" resultType="map">
        SELECT
            product_category_id as categoryId,
            <choose>
                <when test="categoryType == 2">
                    SUM(CAST(material_num AS INTEGER)) as num
                </when>
                <when test="categoryType == 3">
                    SUM(CAST(file_num AS INTEGER)) as num
                </when>
                <when test="categoryType == 4">
                    SUM(CAST(faq_num AS INTEGER)) as num
                </when>
                <otherwise>
                    SUM(CAST(material_num AS INTEGER) + CAST(file_num AS INTEGER) + CAST(faq_num AS INTEGER)) as num
                </otherwise>
            </choose>,
            day
        FROM search_stat_day
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        <if test="type != null and type != 0 and type != 1">
            <choose>
                <when test="type == 2">
                    AND CAST(material_num AS INTEGER) > 0
                </when>
                <when test="type == 3">
                    AND CAST(file_num AS INTEGER) > 0
                </when>
                <when test="type == 4">
                    AND CAST(faq_num AS INTEGER) > 0
                </when>
            </choose>
        </if>
        GROUP BY product_category_id, day
        ORDER BY num DESC
        LIMIT 10
    </select>

    <!-- 根据用户ID列表和时间范围查询时间序列数据 -->
    <select id="selectTimeSeriesDataByUserListAndTimeRange" resultType="map">
        SELECT
            day,
            SUM(CAST(material_num AS INTEGER) + CAST(file_num AS INTEGER) + CAST(faq_num AS INTEGER)) as total,
            COUNT(DISTINCT user_id) as userCount
        FROM search_stat_day
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        <if test="type != null and type != 0 and type != 1">
            <choose>
                <when test="type == 2">
                    AND CAST(material_num AS INTEGER) > 0
                </when>
                <when test="type == 3">
                    AND CAST(file_num AS INTEGER) > 0
                </when>
                <when test="type == 4">
                    AND CAST(faq_num AS INTEGER) > 0
                </when>
            </choose>
        </if>
        GROUP BY day
        ORDER BY day
    </select>

    <!-- 根据用户ID列表和时间范围查询产品小类统计数据 -->
    <select id="selectCategoryStatByUserListAndTimeRange" resultType="map">
        SELECT
            product_category_id as categoryId,
            <choose>
                <when test="type == 2">
                    SUM(CAST(material_num AS INTEGER)) as num
                </when>
                <when test="type == 3">
                    SUM(CAST(file_num AS INTEGER)) as num
                </when>
                <when test="type == 4">
                    SUM(CAST(faq_num AS INTEGER)) as num
                </when>
                <otherwise>
                    SUM(CAST(material_num AS INTEGER) + CAST(file_num AS INTEGER) + CAST(faq_num AS INTEGER)) as num
                </otherwise>
            </choose>,
            day
        FROM search_stat_day
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        <if test="type != null and type != 0 and type != 1">
            <choose>
                <when test="type == 2">
                    AND CAST(material_num AS INTEGER) > 0
                </when>
                <when test="type == 3">
                    AND CAST(file_num AS INTEGER) > 0
                </when>
                <when test="type == 4">
                    AND CAST(faq_num AS INTEGER) > 0
                </when>
            </choose>
        </if>
        GROUP BY product_category_id, day
        ORDER BY num DESC, day
    </select>

    <!-- 统计指定用户列表在统计表中出现的所有用户数（不限时间） -->
    <select id="countAllUsersInStatTable" resultType="Long">
        SELECT COUNT(DISTINCT user_id)
        FROM search_stat_day
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

</mapper>
