<?xml version="1.0" encoding="UTF-8"?>
<!-- Started by AICoder, pid:ge3730ee469183b140d10b7a20a20f46add8bd33 -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.DocDownStatDayMapper">

    <resultMap id="DocDownStatDayResult" type="com.zte.uedm.dcdigital.infrastructure.repository.po.DocDownStatDayPo">
        <id property="id" column="id"/>
        <result property="day" column="day"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="resourceId" column="resource_id"/>
        <result property="downloadNum" column="download_num"/>
        <result property="previewNum" column="preview_num"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <resultMap id="DocDownStatDtoResult" type="com.zte.uedm.dcdigital.interfaces.web.dto.DocDownStatDto">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="resourceId" column="resource_id"/>

        <result property="dayDownNum" column="day_down_num"/>
        <result property="dayViewNum" column="day_view_num"/>
        <result property="weekId" column="week_id"/>
        <result property="weekDownNum" column="week_down_num"/>
        <result property="weekViewNum" column="week_view_num"/>
        <result property="monthId" column="month_id"/>
        <result property="monthDownNum" column="month_down_num"/>
        <result property="monthViewNum" column="month_view_num"/>
        <result property="yearId" column="year_id"/>
        <result property="yearDownNum" column="year_down_num"/>
        <result property="yearViewNum" column="year_view_num"/>
    </resultMap>

    <sql id="selectDocDownStatDayVo">
        SELECT id, day, dept_id, user_id, resource_id, download_num, preview_num, create_time
        FROM file_stat_day
    </sql>

    <select id="getDocDownStatWithDataList" resultMap="DocDownStatDtoResult">
        select
        d.id,
        d.user_id,
        d.dept_id,
        d.resource_id,
        d.download_num as day_down_num,
        d.preview_num as day_view_num,

        w.id as week_id,
        w.download_num as week_down_num,
        w.preview_num as week_view_num,

        m.id as month_id,
        m.download_num as month_down_num,
        m.preview_num as month_view_num,

        y.id as year_id,
        y.download_num as year_down_num,
        y.preview_num as year_view_num
        from
        file_stat_day d
        left join file_stat_week w
        on
        d.user_id = w.user_id
        and d.dept_id = w.dept_id
        and d.resource_id = w.resource_id
        and w.day = #{previousWeekNumber}
        left join file_stat_month m

        on
        d.user_id = m.user_id
        and d.dept_id = m.dept_id
        and d.resource_id = m.resource_id
        and m.day = #{previousMonthNumber}
        left join file_stat_year y
        on
        d.user_id = y.user_id
        and d.dept_id = y.dept_id
        and d.resource_id = y.resource_id
        and y.day = #{previousYearNumber}
        where
        d.day = #{previousDayNumber};
    </select>

    <select id="selectByUserIdAndResourceIdAndDay"
            resultMap="DocDownStatDayResult">
        <include refid="selectDocDownStatDayVo"/>
        WHERE user_id = #{userId} AND resource_id = #{resourceId} AND day = #{day}
    </select>

    <insert id="addRecordStatDay" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.DocDownStatDayPo">
        INSERT INTO file_stat_day (id, day, dept_id, user_id, resource_id, download_num, preview_num, create_time)
        VALUES (#{id}, #{day}, #{deptId}, #{userId}, #{resourceId}, #{downloadNum}, #{previewNum}, #{createTime})
    </insert>

    <update id="updateRecordStatDay" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.DocDownStatDayPo">
        UPDATE file_stat_day SET download_num = #{downloadNum}, preview_num = #{previewNum} WHERE id = #{id}
    </update>

    <select id="getDownAndViewNum"
            resultMap="DocDownStatDayResult">
        SELECT day, SUM(download_num) AS download_num, SUM(preview_num) AS preview_num
        FROM file_stat_day
        <where>
            <if test="item.beginTime != null and item.endTime != null">
                AND day BETWEEN #{item.beginTime} AND #{item.endTime}
            </if>
            <if test="list != null and list.size() > 0">
                AND user_id IN
                <foreach item="userId" collection="list" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
        GROUP BY day
        ORDER BY day
    </select>

    <select id="getPreviewTop50Data"
            resultMap="DocDownStatDayResult">
        SELECT resource_id, SUM(preview_num) AS preview_num
        FROM file_stat_day
        <where>
            <if test="item.beginTime != null and item.endTime != null">
                AND day BETWEEN #{item.beginTime} AND #{item.endTime}
            </if>
            <if test="list != null and list.size() > 0">
                AND user_id IN
                <foreach item="userId" collection="list" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
        GROUP BY resource_id
        ORDER BY preview_num DESC
        LIMIT 50
    </select>

    <select id="getDownloadTop50Data"
            resultMap="DocDownStatDayResult">
        SELECT resource_id, SUM(download_num) AS download_num
        FROM file_stat_day
        <where>
            <if test="item.beginTime != null and item.endTime != null">
                AND day BETWEEN #{item.beginTime} AND #{item.endTime}
            </if>
            <if test="list != null and list.size() > 0">
                AND user_id IN
                <foreach item="userId" collection="list" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
        GROUP BY resource_id
        ORDER BY download_num DESC
        LIMIT 50
    </select>

    <select id="getPreviewDetailsData"
            resultMap="DocDownStatDayResult">
        SELECT resource_id, day, SUM(preview_num) AS preview_num
        FROM file_stat_day
        <where>
            <if test="item.beginTime != null and item.endTime != null">
                AND day BETWEEN #{item.beginTime} AND #{item.endTime}
            </if>
            <if test="list != null and list.size() > 0">
                AND user_id IN
                <foreach item="userId" collection="list" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
        GROUP BY resource_id, day
    </select>

    <select id="getDownloadDetailsData"
            resultMap="DocDownStatDayResult">
        SELECT resource_id, day, SUM(download_num) AS download_num
        FROM file_stat_day
        <where>
            <if test="item.beginTime != null and item.endTime != null">
                AND day BETWEEN #{item.beginTime} AND #{item.endTime}
            </if>
            <if test="list != null and list.size() > 0">
                AND user_id IN
                <foreach item="userId" collection="list" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
        GROUP BY resource_id, day
    </select>

    <select id="getUserStatisticFileDate"
            resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocDownStatEntity">
        SELECT user_id, day, SUM(download_num) AS download_num, SUM(preview_num) AS preview_num
        FROM file_stat_day
        <where>
            <if test="item.beginTime != null and item.endTime != null">
                AND day BETWEEN #{item.beginTime} AND #{item.endTime}
            </if>
            <if test="list != null and list.size() > 0">
                AND user_id IN
                <foreach item="userId" collection="list" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
        GROUP BY user_id, day
    </select>
    <select id="selectByTimeRange"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.DocDownStatDayPo">
        select * from file_stat_day where day between #{startTime} and #{endTime}
    </select>
</mapper>

        <!-- Ended by AICoder, pid:ge3730ee469183b140d10b7a20a20f46add8bd33 -->