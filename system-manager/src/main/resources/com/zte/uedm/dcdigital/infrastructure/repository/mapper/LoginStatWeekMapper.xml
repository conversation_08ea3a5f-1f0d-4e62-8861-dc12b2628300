<?xml version="1.0" encoding="UTF-8"?>
<!-- Started by AICoder, pid:p45441e789ofd9a14862083b00c997452ce8687a -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.LoginStatWeekMapper">

    <resultMap id="LoginStatWeekResult" type="com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatWeekPo">
        <id property="id" column="id"/>
        <result property="day" column="day"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="num" column="num"/>
    </resultMap>

    <sql id="selectLoginStatWeekVo">
        SELECT id, day, dept_id, user_id, num
        FROM login_stat_week
    </sql>

    <insert id="insertLoginStatWeek" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatWeekPo">
        INSERT INTO login_stat_week (id, day, dept_id, user_id, num)
        VALUES (#{id}, #{day}, #{deptId}, #{userId}, #{num})
    </insert>

    <insert id="batchInsertLoginStatWeek">
        INSERT INTO login_stat_week (id, day, dept_id, user_id, num)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.day}, #{item.deptId}, #{item.userId}, #{item.num})
        </foreach>
    </insert>

    <update id="batchUpdateLoginStatWeek">
        UPDATE login_stat_week
        SET
        num = CASE id
        <foreach collection="list" item="item" separator=" ">
            WHEN #{item.id} THEN #{item.num}
        </foreach>
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="selectLoginStatWeekList"
            parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatWeekPo"
            resultMap="LoginStatWeekResult">
        <include refid="selectLoginStatWeekVo"/>
        <where>
            <if test="day != null">AND day = #{day}</if>
            <if test="deptId != null and deptId != ''">AND dept_id = #{deptId}</if>
            <if test="userId != null and userId != ''">AND user_id = #{userId}</if>
            <if test="beginTime != null and endTime != null">
                AND day BETWEEN #{beginTime} AND #{endTime}
            </if>
        </where>
    </select>

    <delete id="deleteLoginStatWeekByIds" parameterType="String">
        DELETE FROM login_stat_week
        WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByWeekNumber">
        DELETE FROM login_stat_week
        WHERE day = #{weekNumber}
    </delete>

    <select id="selectLoginStatWeekById"
            parameterType="String"
            resultMap="LoginStatWeekResult">
        <include refid="selectLoginStatWeekVo"/>
        WHERE id = #{id}
    </select>
</mapper>

        <!-- Ended by AICoder, pid:p45441e789ofd9a14862083b00c997452ce8687a -->