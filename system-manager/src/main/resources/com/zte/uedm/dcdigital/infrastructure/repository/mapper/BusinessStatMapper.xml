<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- Started by AICoder, pid:l2dddu3474rd08914e3d0a1e61daff84f6753bc5 -->
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.BusinessStatMapper">

    <!-- 定义业务统计视图对象的结果映射 -->
    <resultMap id="businessStatisticVo" type="com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticVo">
        <result column="day" jdbcType="VARCHAR" property="day"/>
        <result column="allTaskNum" jdbcType="VARCHAR" property="allTaskNum"/>
        <result column="normalTaskNum" jdbcType="VARCHAR" property="normalTaskNum"/>
        <result column="extensionTaskNum" jdbcType="INTEGER" property="extensionTaskNum"/>
        <result column="allLectotypeNum" jdbcType="INTEGER" property="allLectotypeNum"/>
        <result column="bidLectotypeNum" jdbcType="TIMESTAMP" property="bidLectotypeNum"/>
        <result column="appointLectotypeNum" jdbcType="TIMESTAMP" property="appointLectotypeNum"/>
    </resultMap>

    <!-- 按天统计业务数据 -->
    <select id="selectBusinessStatisticByDay" resultMap="businessStatisticVo"
            parameterType="com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticQueryDto">
        <!-- 查询按天统计的业务数据 -->
        select a.day, COALESCE(b.allTaskNum,0) as allTaskNum, COALESCE(b.normalTaskNum,0)as normalTaskNum,
        COALESCE(b.extensionTaskNum,0) as extensionTaskNum,
        COALESCE(b.allLectotypeNum,0) as allLectotypeNum, COALESCE(b.bidLectotypeNum,0) as bidLectotypeNum,
        COALESCE(b.appointLectotypeNum,0) as appointLectotypeNum from
        (SELECT column1 AS day
        FROM (VALUES
        <foreach collection="completeTimeRange" item="day" open="" separator="," close="">
            ( #{day})
        </foreach>
        ) AS temp_table(column1)
        ) a
        left join

        ( select
        day,SUM(all_task_num) as allTaskNum,SUM(normal_task_num) as normalTaskNum,
        SUM(extension_task_num) as extensionTaskNum,SUM(all_lectotype_num) as allLectotypeNum,
        SUM(bid_lectotype_num) as bidLectotypeNum,SUM(appoint_lectotype_num) as appointLectotypeNum
        FROM business_stat_day
        WHERE
        day in
        <foreach collection="completeTimeRange" item="day" open="(" separator="," close=")">
            #{day}
        </foreach>
        and product_category_id in
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>

        group by day ) b on a.day=b.day
    </select>

    <!-- 按周统计业务数据 -->
    <select id="selectBusinessStatisticByWeek" resultMap="businessStatisticVo"
            parameterType="com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticQueryDto">
        <!-- 查询按周统计的业务数据 -->
        select a.day, COALESCE(b.allTaskNum,0) as allTaskNum, COALESCE(b.normalTaskNum,0)as normalTaskNum,
        COALESCE(b.extensionTaskNum,0) as extensionTaskNum,
        COALESCE(b.allLectotypeNum,0) as allLectotypeNum, COALESCE(b.bidLectotypeNum,0) as bidLectotypeNum,
        COALESCE(b.appointLectotypeNum,0) as appointLectotypeNum from
        (SELECT column1 AS day
        FROM (VALUES
        <foreach collection="completeTimeRange" item="day" open="" separator="," close="">
            ( #{day})
        </foreach>
        ) AS temp_table(column1)
        ) a
        left join

        ( select
        day,SUM(all_task_num) as allTaskNum,SUM(normal_task_num) as normalTaskNum,
        SUM(extension_task_num) as extensionTaskNum,SUM(all_lectotype_num) as allLectotypeNum,
        SUM(bid_lectotype_num) as bidLectotypeNum,SUM(appoint_lectotype_num) as appointLectotypeNum
        FROM business_stat_week
        WHERE
        day in
        <foreach collection="completeTimeRange" item="day" open="(" separator="," close=")">
            #{day}
        </foreach>
        and product_category_id in
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>

        group by day ) b on a.day=b.day
    </select>

    <!-- 按月统计业务数据 -->
    <select id="selectBusinessStatisticByMonth" resultMap="businessStatisticVo"
            parameterType="com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticQueryDto">
        <!-- 查询按月统计的业务数据 -->
        select a.day, COALESCE(b.allTaskNum,0) as allTaskNum, COALESCE(b.normalTaskNum,0)as normalTaskNum,
        COALESCE(b.extensionTaskNum,0) as extensionTaskNum,
        COALESCE(b.allLectotypeNum,0) as allLectotypeNum, COALESCE(b.bidLectotypeNum,0) as bidLectotypeNum,
        COALESCE(b.appointLectotypeNum,0) as appointLectotypeNum from
        (SELECT column1 AS day
        FROM (VALUES
        <foreach collection="completeTimeRange" item="day" open="" separator="," close="">
            ( #{day})
        </foreach>
        ) AS temp_table(column1)
        ) a
        left join

        ( select
        day,SUM(all_task_num) as allTaskNum,SUM(normal_task_num) as normalTaskNum,
        SUM(extension_task_num) as extensionTaskNum,SUM(all_lectotype_num) as allLectotypeNum,
        SUM(bid_lectotype_num) as bidLectotypeNum,SUM(appoint_lectotype_num) as appointLectotypeNum
        FROM business_stat_month
        WHERE
        day in
        <foreach collection="completeTimeRange" item="day" open="(" separator="," close=")">
            #{day}
        </foreach>
        and product_category_id in
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>

        group by day ) b on a.day=b.day
    </select>

    <!-- 按年统计业务数据 -->
    <select id="selectBusinessStatisticByYear" resultMap="businessStatisticVo"
            parameterType="com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticQueryDto">
        <!-- 查询按年统计的业务数据 -->
        select a.day, COALESCE(b.allTaskNum,0) as allTaskNum, COALESCE(b.normalTaskNum,0)as normalTaskNum,
        COALESCE(b.extensionTaskNum,0) as extensionTaskNum,
        COALESCE(b.allLectotypeNum,0) as allLectotypeNum, COALESCE(b.bidLectotypeNum,0) as bidLectotypeNum,
        COALESCE(b.appointLectotypeNum,0) as appointLectotypeNum from
        (SELECT column1 AS day
        FROM (VALUES
        <foreach collection="completeTimeRange" item="day" open="" separator="," close="">
            ( #{day})
        </foreach>
        ) AS temp_table(column1)
        ) a
        left join

        ( select
        day,SUM(all_task_num) as allTaskNum,SUM(normal_task_num) as normalTaskNum,
        SUM(extension_task_num) as extensionTaskNum,SUM(all_lectotype_num) as allLectotypeNum,
        SUM(bid_lectotype_num) as bidLectotypeNum,SUM(appoint_lectotype_num) as appointLectotypeNum
        FROM business_stat_year
        WHERE
        day in
        <foreach collection="completeTimeRange" item="day" open="(" separator="," close=")">
            #{day}
        </foreach>
        and product_category_id in
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>

        group by day ) b on a.day=b.day
    </select>

    <!-- 插入按天统计的业务数据 -->
    <insert id="addBusinessStatDay" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.BusinessStatisticPo">
        INSERT INTO business_stat_day (id, day, product_category_id, all_task_num, normal_task_num,
        extension_task_num, all_lectotype_num,bid_lectotype_num,appoint_lectotype_num, create_time)
        VALUES (#{id}, #{day}, #{productCategoryId}, #{allTaskNum}, #{normalTaskNum}, #{extensionTaskNum},
        #{allLectotypeNum}, #{bidLectotypeNum}, #{appointLectotypeNum}, #{createTime})
    </insert>

    <!-- 插入按周统计的业务数据 -->
    <insert id="addBusinessStatWeek" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.BusinessStatisticPo">
        INSERT INTO business_stat_week (id, day, product_category_id, all_task_num, normal_task_num,
        extension_task_num, all_lectotype_num,bid_lectotype_num,appoint_lectotype_num, create_time)
        VALUES (#{id}, #{day}, #{productCategoryId}, #{allTaskNum}, #{normalTaskNum}, #{extensionTaskNum},
        #{allLectotypeNum}, #{bidLectotypeNum}, #{appointLectotypeNum}, #{createTime})
    </insert>

    <!-- 插入按月统计的业务数据 -->
    <insert id="addBusinessStatMonth" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.BusinessStatisticPo">
        INSERT INTO business_stat_month (id, day, product_category_id, all_task_num, normal_task_num,
        extension_task_num, all_lectotype_num,bid_lectotype_num,appoint_lectotype_num, create_time)
        VALUES (#{id}, #{day}, #{productCategoryId}, #{allTaskNum}, #{normalTaskNum}, #{extensionTaskNum},
        #{allLectotypeNum}, #{bidLectotypeNum}, #{appointLectotypeNum}, #{createTime})
    </insert>

    <!-- 插入按年统计的业务数据 -->
    <insert id="addBusinessStatYear" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.BusinessStatisticPo">
        INSERT INTO business_stat_year (id, day, product_category_id, all_task_num, normal_task_num,
        extension_task_num, all_lectotype_num,bid_lectotype_num,appoint_lectotype_num, create_time)
        VALUES (#{id}, #{day}, #{productCategoryId}, #{allTaskNum}, #{normalTaskNum}, #{extensionTaskNum},
        #{allLectotypeNum}, #{bidLectotypeNum}, #{appointLectotypeNum}, #{createTime})
    </insert>

    <!-- Started by AICoder, pid:kdc52mb96aa4d2d14ef90935903cf022736600cb -->

        <!-- 删除按天统计的业务数据 -->
        <delete id="delBusinessStatDay">
            <!-- 根据 day 和 product_category_id 删除 business_stat_day 表中的记录 -->
            delete from business_stat_day where day = #{day} and product_category_id = #{productCategoryId}
        </delete>

        <!-- 删除按周统计的业务数据 -->
        <delete id="delBusinessStatWeek">
            <!-- 根据 day 和 product_category_id 删除 business_stat_week 表中的记录 -->
            delete from business_stat_week where day = #{day} and product_category_id = #{productCategoryId}
        </delete>

        <!-- 删除按月统计的业务数据 -->
        <delete id="delBusinessStatMonth">
            <!-- 根据 day 和 product_category_id 删除 business_stat_month 表中的记录 -->
            delete from business_stat_month where day = #{day} and product_category_id = #{productCategoryId}
        </delete>

        <!-- 删除按年统计的业务数据 -->
        <delete id="delBusinessStatYear">
            <!-- 根据 day 和 product_category_id 删除 business_stat_year 表中的记录 -->
            delete from business_stat_year where day = #{day} and product_category_id = #{productCategoryId}
        </delete>
    <!-- Ended by AICoder, pid:kdc52mb96aa4d2d14ef90935903cf022736600cb -->

    <!-- Started by AICoder, pid:hb0bdva704tb7c41415809d540033e1d01587d24 -->
    <!-- 根据表名和天查询业务数据概览 -->
    <select id="selectOverviewStatistic" resultMap="businessStatisticVo">
            <!-- 查询指定表中按天统计的业务数据概览 -->
            select
            day,
            SUM(all_task_num) as allTaskNum,
            SUM(normal_task_num) as normalTaskNum,
            SUM(extension_task_num) as extensionTaskNum,
            SUM(all_lectotype_num) as allLectotypeNum,
            SUM(bid_lectotype_num) as bidLectotypeNum,
            SUM(appoint_lectotype_num) as appointLectotypeNum
            FROM ${table}
            WHERE day >= #{startTime} and day  &lt;= #{endTime}
            group by day order by day desc
    </select>
    <!-- Ended by AICoder, pid:hb0bdva704tb7c41415809d540033e1d01587d24 -->
</mapper>
        <!-- Ended by AICoder, pid:l2dddu3474rd08914e3d0a1e61daff84f6753bc5 -->