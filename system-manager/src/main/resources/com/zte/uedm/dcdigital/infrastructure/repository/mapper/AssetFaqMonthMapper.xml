<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.AssetFaqMonthMapper">
    <!-- Started by AICoder, pid:p1e5ch1631zd567143bb0b83907043143902494a -->
    <insert id="batchInsert">
        INSERT INTO asset_faq_month
        (id, day, product_category_id, all_num, change_num, update_num, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.day}, #{item.productCategoryId}, #{item.allNum}, #{item.changeNum}, #{item.updateNum},
            #{item.createTime})
        </foreach>
    </insert>
    <update id="update">
        UPDATE asset_faq_month
        SET all_num = #{allNum}, change_num = #{changeNum}, update_num = #{updateNum}
        WHERE id = #{id}
    </update>
    <!-- Ended by AICoder, pid:p1e5ch1631zd567143bb0b83907043143902494a -->

    <select id="selectByCategoryAndTimeRange" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqMonthPo">
        SELECT id, day, product_category_id, all_num, change_num, update_num, create_time
        FROM asset_faq_month
        WHERE product_category_id = #{productCategoryId}
        AND day BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="selectByTimeRange" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqMonthPo">
        SELECT id, day, product_category_id, all_num, change_num, update_num, create_time
        FROM asset_faq_month
        WHERE day BETWEEN #{startTime} AND #{endTime}
        AND product_category_id IN
        <foreach collection="categoryIds" item="cateIds" open="(" separator="," close=")">
            #{cateIds}
        </foreach>
    </select>
    <select id="selectByTime" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqMonthPo">
        SELECT id, day, product_category_id, all_num, change_num, update_num, create_time
        FROM asset_faq_month
        WHERE day = #{monthStr}
    </select>

    <select id="selectDateByTimeRange" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqMonthPo">
        SELECT id, day, product_category_id, all_num, change_num, update_num, create_time
        FROM asset_faq_month
        WHERE day BETWEEN #{startTime} AND #{endTime}
    </select>

</mapper>