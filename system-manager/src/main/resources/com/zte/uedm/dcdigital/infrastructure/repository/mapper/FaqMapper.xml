<?xml version="1.0" encoding="UTF-8" ?>
<!-- Started by AICoder, pid:1c88a65d33h0a9b14a620a99208e191932839d0d -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.FaqMapper">

    <!-- 模糊查询 question -->
    <select id="queryByCondition" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.FaqPo">
        SELECT *
        FROM faq
        WHERE LOWER(question) LIKE CONCAT('%', LOWER(#{searchText}), '%')
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="resourceId != null and resourceId != ''">
            AND resource_id = #{resourceId}
        </if>
        <if test="sort != null">
            ORDER BY ${sort} ${order}
        </if>
    </select>
    <select id="selectCitedList" resultType="com.zte.uedm.dcdigital.common.bean.document.DocumentCitedVo">
        SELECT id, question as name FROM faq
        WHERE id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <!-- Started by AICoder, pid:gdd4e8feebr4fb1141a309ab20895c02b84974c6 -->
    <select id="selectByIds" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.FaqPo">
        SELECT * FROM faq where id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectCountByResourceId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM faq WHERE resource_id = #{resourceId}
    </select>
    <!-- Ended by AICoder, pid:gdd4e8feebr4fb1141a309ab20895c02b84974c6 -->

    <!-- Started by AICoder, pid:52f7b82313xc4e814d2a0b951000530dd817e559 -->
    <update id="update" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.FaqPo">
        UPDATE faq
        SET question = #{question},
        answer = #{answer},
        update_time = #{updateTime},
        update_by = #{updateBy}
        WHERE id = #{id}
    </update>
    <!-- Ended by AICoder, pid:52f7b82313xc4e814d2a0b951000530dd817e559 -->

</mapper>
        <!-- Ended by AICoder, pid:1c88a65d33h0a9b14a620a99208e191932839d0d -->