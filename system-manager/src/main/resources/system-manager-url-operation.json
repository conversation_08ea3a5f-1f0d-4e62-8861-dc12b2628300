[{"url": "/api/asset-manager/v1/swagger", "method": "GET", "operation": "operation.swagger"}, {"url": "/api/asset-manager/v1/swagger.json", "method": "GET", "operation": "operation.swagger"}, {"url": "/api/asset-manager/v1/swagger.yaml", "method": "GET", "operation": "operation.swagger"}, {"url": "/iui/{respath:.+}", "method": "GET", "operation": "global.operation"}, {"url": "/iui/{respath:.+}", "method": "POST", "operation": "global.operation"}, {"url": "/api/{respath:.+}", "method": "POST", "operation": "global.operation"}, {"url": "/api/{respath:.+}", "method": "GET", "operation": "global.operation"}, {"url": "/api/{respath:.+}", "method": "DELETE", "operation": "global.operation"}, {"url": "/api/{respath:.+}", "method": "PUT", "operation": "global.operation"}, {"url": "/api/asset-manager/v1/resource-capacity/detail-by-position", "method": "POST", "operation": "operation.uedm.monitor.a.manager.real.time.res.query"}, {"url": "/api/asset-manager/v1/resource-capacity/detail/get-by-resourceId", "method": "GET", "operation": "operation.uedm.monitor.a.manager.real.time.res.query"}, {"url": "/api/asset-manager/v1/asset-overview/number-statistics/assetModels", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/overview-chart/query", "method": "GET", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/overview-chart/edit-show-status", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-instance/group-by-logicGroupId", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-history/in-decrease/query-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-instance/use-status/group-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-overview/number-statistics/dimension", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-inspection/abnormal/statistics-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-inspection/risk/statistics-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-overview/export", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-inspection/abnormal-detail/query-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-inspection/risk-detail/query-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-inspection/risk-type/options/query-alls", "method": "GET", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-inspection/abnormal-detail/export", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-inspection/risk-detail/export", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-instance-manager/select-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-inventory-dimensions/select", "method": "GET", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-inventory-dimensions/update", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.view"}, {"url": "/api/asset-manager/v1/asset-instance-manager/add-asset", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.add"}, {"url": "/api/asset-manager/v1/asset-instance-manager/detail-by-id", "method": "GET", "operation": "operation.uedm.maintenance.asset.overview.update"}, {"url": "/api/asset-manager/v1/asset-instance-manager/edit-asset", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.update"}, {"url": "/api/asset-manager/v1/asset-instance/query-batch-modify-attribute", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.update"}, {"url": "/api/asset-manager/v1/asset-instance/batch-modify", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.update"}, {"url": "/api/asset-manager/v1/asset-instance/query-batch-modify-status", "method": "GET", "operation": "operation.uedm.maintenance.asset.overview.update"}, {"url": "/api/asset-manager/v1/asset-instance-manager/delete-asset", "method": "DELETE", "operation": "operation.uedm.maintenance.asset.overview.delete"}, {"url": "/api/asset-manager/v1/asset-instance-manager/recover-delete", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.recover"}, {"url": "/api/asset-manager/v1/asset-inventory-batch/import-asset", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.import"}, {"url": "/api/asset-manager/v1/asset-inventory-batch/export-error-file", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.import"}, {"url": "/api/asset-manager/v1/asset-inventory-batch/get-import-template", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.import"}, {"url": "/api/asset-manager/v1/asset-inventory-batch/used-not-rented/get-import-is-finish", "method": "GET", "operation": "operation.uedm.maintenance.asset.overview.view:operation.uedm.maintenance.asset.overview.import"}, {"url": "/api/asset-manager/v1/export/task-manage", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.export"}, {"url": "/api/asset-manager/v1/export/task-manage/list", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.export"}, {"url": "/api/asset-manager/v1/export/task-manage", "method": "DELETE", "operation": "operation.uedm.maintenance.asset.overview.export"}, {"url": "/api/asset-manager/v1/asset-instance-manager/get-assetCodes-by-assetNumbers", "method": "POST", "operation": "operation.uedm.maintenance.asset.all.activies.view"}, {"url": "/api/asset-manager/v1/asset-activity/get-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.all.activies.view:operation.uedm.maintenance.asset.my.application.view:operation.uedm.maintenance.tower.capacity.allocate.view.activity:operation.uedm.maintenance.tower.asset.lose.view.activity"}, {"url": "/api/asset-manager/v1/asset-activity/get-related-assets-by-activityCode", "method": "GET", "operation": "operation.uedm.maintenance.asset.all.activies.view:operation.uedm.maintenance.asset.my.application.view:operation.uedm.maintenance.asset.to.handle.view:operation.uedm.maintenance.asset.my.handled.view:operation.uedm.maintenance.resources.planning.reservation.view"}, {"url": "/api/asset-manager/v1/asset-activity/activity-export", "method": "POST", "operation": "operation.uedm.maintenance.asset.all.activies.view:operation.uedm.maintenance.asset.my.application.view"}, {"url": "/api/asset-manager/v1/asset-activity/process", "method": "POST", "operation": "operation.uedm.maintenance.asset.all.activities.approval:operation.uedm.maintenance.asset.to.handle.approval"}, {"url": "/api/asset-manager/v1/asset-activity/related-assets-edit", "method": "POST", "operation": "operation.uedm.maintenance.asset.all.activities.approval:operation.uedm.maintenance.asset.my.application.add:operation.uedm.maintenance.asset.my.application.edit:operation.uedm.maintenance.asset.my.application.be.deleted:operation.uedm.maintenance.asset.to.handle.approval:operation.uedm.maintenance.resources.planning.reservation.mounting"}, {"url": "/api/asset-manager/v1/asset-activity/copy-asset", "method": "POST", "operation": "operation.uedm.maintenance.asset.all.activities.approval:operation.uedm.maintenance.asset.my.application.add:operation.uedm.maintenance.asset.my.application.edit:operation.uedm.maintenance.asset.to.handle.approval:operation.uedm.maintenance.resources.planning.reservation.mounting"}, {"url": "/api/asset-manager/v1/asset-activity/related-assets-delete", "method": "DELETE", "operation": "operation.uedm.maintenance.asset.all.activities.approval:operation.uedm.maintenance.asset.my.application.add:operation.uedm.maintenance.asset.my.application.edit:operation.uedm.maintenance.asset.to.handle.approval:operation.uedm.maintenance.resources.planning.reservation.mounting"}, {"url": "/api/asset-manager/v1/asset-activity/assign-processor", "method": "POST", "operation": "operation.uedm.maintenance.asset.all.activities.assign"}, {"url": "/api/asset-manager/v1/asset-activity/addOrEdit", "method": "POST", "operation": "operation.uedm.maintenance.tower.asset.lose.to.scrap:operation.uedm.maintenance.asset.my.application.add:operation.uedm.maintenance.asset.my.application.edit:operation.uedm.maintenance.asset.my.application.warehousing:operation.uedm.maintenance.asset.my.application.rack.mounting:operation.uedm.maintenance.asset.my.application.shelf.removal:operation.uedm.maintenance.asset.my.application.allocation:operation.uedm.maintenance.asset.my.application.repair:operation.uedm.maintenance.asset.my.application.scrapping:operation.uedm.maintenance.asset.my.application.requisition:operation.uedm.maintenance.asset.my.application.retreat:operation.uedm.maintenance.asset.my.application.maintenance:operation.uedm.maintenance.resources.planning.reservation.mounting"}, {"url": "/api/asset-manager/v1/user-group-approver/process", "method": "POST", "operation": "operation.uedm.maintenance.asset.my.application.add:operation.uedm.maintenance.asset.my.application.edit:operation.uedm.maintenance.resources.planning.reservation.view"}, {"url": "/api/asset-manager/v1/asset-activity/recall", "method": "GET", "operation": "operation.uedm.maintenance.asset.my.application.withdraw"}, {"url": "/api/asset-manager/v1/asset-activity/delete", "method": "DELETE", "operation": "operation.uedm.maintenance.asset.my.application.delete:operation.uedm.maintenance.resources.planning.reservation.mounting"}, {"url": "/api/asset-manager/v1/asset-activity/get-my-approval", "method": "POST", "operation": "operation.uedm.maintenance.asset.to.handle.view"}, {"url": "/api/asset-manager/v1/asset-activity/my-approval-export", "method": "POST", "operation": "operation.uedm.maintenance.asset.to.handle.view"}, {"url": "/api/asset-manager/v1/asset-activity/get-formal-assets-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.to.handle.feedback"}, {"url": "/api/asset-manager/v1/asset-activity/maintain-feedback", "method": "POST", "operation": "operation.uedm.maintenance.asset.to.handle.feedback"}, {"url": "/api/asset-manager/v1/asset-activity/get-processed-assets-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.my.after.handled.view"}, {"url": "/api/asset-manager/v1/asset-activity/processed-assets-export", "method": "POST", "operation": "operation.uedm.maintenance.asset.my.after.handled.view"}, {"url": "/api/asset-manager/v1/asset-activity/get-my-approved", "method": "POST", "operation": "operation.uedm.maintenance.asset.my.handled.view"}, {"url": "/api/asset-manager/v1/asset-activity/my-approved-export", "method": "POST", "operation": "operation.uedm.maintenance.asset.my.handled.view"}, {"url": "/api/asset-manager/v1/monitor-object-synchronization-task-record/select-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.obj.syn"}, {"url": "/api/asset-manager/v1/monitor-object-synchronization-task-record/taskName-uniqueness", "method": "GET", "operation": "operation.uedm.maintenance.asset.obj.syn"}, {"url": "/api/asset-manager/v1/monitor-object-synchronization-task-record/insert", "method": "POST", "operation": "operation.uedm.maintenance.asset.obj.syn"}, {"url": "/api/asset-manager/v1/websocket/monitor-object-synchronization-task-record", "method": "GET", "operation": "operation.uedm.maintenance.asset.obj.syn"}, {"url": "/api/asset-manager/v1/asset-model/attribute/move-attribute", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.model.view"}, {"url": "/api/asset-manager/v1/asset-model/add", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.model.add"}, {"url": "/api/asset-manager/v1/asset-model/check-name", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.model.add:operation.uedm.maintenance.asset.config.model.edit"}, {"url": "/api/asset-manager/v1/asset-model/update", "method": "PUT", "operation": "operation.uedm.maintenance.asset.config.model.edit"}, {"url": "/api/asset-manager/v1/asset-model", "method": "DELETE", "operation": "operation.uedm.maintenance.asset.config.model.delete"}, {"url": "/api/asset-manager/v1/asset-model/attribute/check-name", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.model.attr.custom.add:operation.uedm.maintenance.asset.config.model.attr.custom.edit"}, {"url": "/api/asset-manager/v1/asset-model/attribute/insert-custom", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.model.attr.custom.add"}, {"url": "/api/asset-manager/v1/asset-model/attribute/update-custom", "method": "PUT", "operation": "operation.uedm.maintenance.asset.config.model.attr.custom.edit"}, {"url": "/api/asset-manager/v1/asset-model/attribute/check-quote", "method": "GET", "operation": "operation.uedm.maintenance.asset.config.model.attr.custom.delete"}, {"url": "/api/asset-manager/v1/asset-model/attribute/delete-custom", "method": "DELETE", "operation": "operation.uedm.maintenance.asset.config.model.attr.custom.delete"}, {"url": "/api/asset-manager/v1/asset-model/attribute/enable-attribute", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.model.attr.set"}, {"url": "/api/asset-manager/v1/device-models/is-same-name-exit", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.template.add:operation.uedm.maintenance.asset.config.template.edit"}, {"url": "/api/asset-manager/v1/device-models/add", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.template.add"}, {"url": "/api/asset-manager/v1/device-models/update-template", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.template.edit"}, {"url": "/api/asset-manager/v1/device-models/delete", "method": "DELETE", "operation": "operation.uedm.maintenance.asset.config.template.delete"}, {"url": "/api/asset-manager/v1/device-models/copy", "method": "GET", "operation": "operation.uedm.maintenance.asset.config.template.copy"}, {"url": "/api/asset-manager/v1/manufacturer-type/get-by-condition", "method": "GET", "operation": "operation.uedm.maintenance.asset.config.vendor.view"}, {"url": "/api/asset-manager/v1/manufacturer/get-descent-model-by-id", "method": "POST", "operation": "operation.uedm.maintenance.asset.overview.add:operation.uedm.maintenance.asset.overview.update:operation.uedm.maintenance.asset.all.activities.approval:operation.uedm.maintenance.asset.my.application.add:operation.uedm.maintenance.asset.my.application.edit:operation.uedm.maintenance.asset.to.handle.approval:operation.uedm.maintenance.asset.config.template.add:operation.uedm.maintenance.asset.config.template.edit:operation.uedm.maintenance.asset.config.vendor.view"}, {"url": "/api/asset-manager/v1/manufacturer/descent-model-export", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.vendor.view"}, {"url": "/api/asset-manager/v1/manufacturer/add", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.vendor.add"}, {"url": "/api/asset-manager/v1/manufacturer/check-name", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.vendor.add:operation.uedm.maintenance.asset.config.vendor.edit"}, {"url": "/api/asset-manager/v1/manufacturer/edit", "method": "PUT", "operation": "operation.uedm.maintenance.asset.config.vendor.edit"}, {"url": "/api/asset-manager/v1/manufacturer", "method": "DELETE", "operation": "operation.uedm.maintenance.asset.config.vendor.delete"}, {"url": "/api/asset-manager/v1/activity-process/type/query-all", "method": "GET", "operation": "operation.uedm.maintenance.asset.all.activies.view:operation.uedm.maintenance.asset.my.application.view:operation.uedm.maintenance.asset.my.application.add:operation.uedm.maintenance.asset.my.application.edit:operation.uedm.maintenance.asset.to.handle.view:operation.uedm.maintenance.asset.my.handled.view:operation.uedm.maintenance.asset.my.after.handled.view:operation.uedm.maintenance.asset.config.process.view:operation.uedm.maintenance.asset.config.approver.view"}, {"url": "/api/asset-manager/v1/asset-activity/require-info", "method": "PUT", "operation": "operation.uedm.maintenance.asset.config.process.view"}, {"url": "/api/asset-manager/v1/activity-process/template/check-name", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.process.add:operation.uedm.maintenance.asset.config.process.edit"}, {"url": "/api/asset-manager/v1/activity-process/template/add", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.process.add"}, {"url": "/api/asset-manager/v1/activity-process/template/edit", "method": "PUT", "operation": "operation.uedm.maintenance.asset.config.process.edit"}, {"url": "/api/asset-manager/v1/activity-process/template", "method": "DELETE", "operation": "operation.uedm.maintenance.asset.config.process.delete"}, {"url": "/api/asset-manager/v1/user-group-approver/query-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.approver.view:operation.uedm.maintenance.asset.config.process.add:operation.uedm.maintenance.asset.config.process.edit"}, {"url": "/api/asset-manager/v1/user-group-approver/candidate-user/query-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.processor.add"}, {"url": "/api/asset-manager/v1/user-group-approver/add", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.processor.add"}, {"url": "/api/asset-manager/v1/user-group-approver/delete-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.asset.config.processor.delete"}, {"url": "/api/asset-manager/v1/resource-overview/view", "method": "POST", "operation": "operation.uedm.maintenance.resources.analysis.view"}, {"url": "/api/asset-manager/v1/resource-overview/history", "method": "POST", "operation": "operation.uedm.maintenance.resources.analysis.view"}, {"url": "/api/asset-manager/v1/resource-analysis/list", "method": "POST", "operation": "operation.uedm.maintenance.resources.analysis.view"}, {"url": "/api/asset-manager/v1/resource-analysis/scatter", "method": "POST", "operation": "operation.uedm.maintenance.resources.analysis.view"}, {"url": "/api/asset-manager/v1/resource-management/detail-list", "method": "POST", "operation": "operation.uedm.maintenance.resources.analysis.view"}, {"url": "/api/asset-manager/v1/resource-overview/trend", "method": "POST", "operation": "operation.uedm.maintenance.resources.analysis.view"}, {"url": "/api/asset-manager/v1/resource-overview/export", "method": "POST", "operation": "operation.uedm.maintenance.resources.analysis.view"}, {"url": "/api/asset-manager/v1/cap-eval/select-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.resources.smr.analysis.view"}, {"url": "/api/asset-manager/v1/cap-eval/trend/select-by-area", "method": "POST", "operation": "operation.uedm.maintenance.resources.smr.analysis.view"}, {"url": "/api/asset-manager/v1/cap-eval/trend/select-by-sp", "method": "POST", "operation": "operation.uedm.maintenance.resources.smr.analysis.view"}, {"url": "/api/asset-manager/v1/cap-eval/trend/select-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.resources.smr.analysis.view"}, {"url": "/api/asset-manager/v1/cap-eval/time/select-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.resources.smr.analysis.view"}, {"url": "/api/asset-manager/v1/cap-eval/select-by-sp", "method": "POST", "operation": "operation.uedm.maintenance.resources.smr.analysis.view"}, {"url": "/api/asset-manager/v1/cap-eval/export-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.resources.smr.analysis.view"}, {"url": "/api/asset-manager/v1/cap-eval/export-by-sp", "method": "POST", "operation": "operation.uedm.maintenance.resources.smr.analysis.view"}, {"url": "/api/asset-manager/v1/cap-eval/eval-task", "method": "POST", "operation": "operation.uedm.maintenance.resources.smr.analysis.eval"}, {"url": "/api/asset-manager/v1/cap-thrd/load-rate/select", "method": "GET", "operation": "operation.uedm.maintenance.resources.smr.analysis.set"}, {"url": "/api/asset-manager/v1/cap-thrd/load-rate/update", "method": "PUT", "operation": "operation.uedm.maintenance.resources.smr.analysis.set"}, {"url": "/api/asset-manager/v1/resource-planning/capacity-search/select-by-device-model", "method": "POST", "operation": "operation.uedm.maintenance.resources.planning.search.view"}, {"url": "/api/asset-manager/v1/resource-planning/capacity-search/select-by-reserved-capacity", "method": "GET", "operation": "operation.uedm.maintenance.resources.planning.search.view"}, {"url": "/api/asset-manager/v1/resource-planning/capacity-search/select-by-rack-capacity", "method": "GET", "operation": "operation.uedm.maintenance.resources.planning.search.view"}, {"url": "/api/asset-manager/v1/resource-pre-occupy/get-by-condition", "method": "POST", "operation": "operation.uedm.maintenance.resources.planning.reservation.view"}, {"url": "/api/asset-manager/v1/resource-pre-occupy/get-related-occupy-workflow-by-activityCode", "method": "POST", "operation": "operation.uedm.maintenance.resources.planning.reservation.view"}, {"url": "/api/asset-manager/v1/resource-pre-occupy/get-related-resource-by-activityCode", "method": "POST", "operation": "operation.uedm.maintenance.resources.planning.reservation.view"}, {"url": "/api/asset-manager/v1/resource-pre-occupy/addOrEdit", "method": "POST", "operation": "operation.uedm.maintenance.resources.planning.reservation.add:operation.uedm.maintenance.resources.planning.reservation.edit"}, {"url": "/api/asset-manager/v1/resource-pre-occupy/delete", "method": "DELETE", "operation": "operation.uedm.maintenance.resources.planning.reservation.add:operation.uedm.maintenance.resources.planning.reservation.delete"}, {"url": "/api/asset-manager/v1/resource-pre-occupy/release", "method": "GET", "operation": "operation.uedm.maintenance.resources.planning.reservation.release"}, {"url": "/api/asset-manager/v1/resource-pre-occupy/process", "method": "POST", "operation": "operation.uedm.maintenance.resources.planning.reservation.approve:operation.uedm.maintenance.asset.to.handle.approval"}, {"url": "/api/asset-manager/v1/resource-pre-occupy/update-pre-occupy-resources", "method": "POST", "operation": "operation.uedm.maintenance.resources.planning.reservation.approve:operation.uedm.maintenance.resources.planning.reservation.add:operation.uedm.maintenance.resources.planning.reservation.edit:operation.uedm.maintenance.asset.to.handle.approval"}, {"url": "/api/asset-manager/v1/resource-pre-occupy/delete-pre-occupy-resources", "method": "POST", "operation": "operation.uedm.maintenance.resources.planning.reservation.approve:operation.uedm.maintenance.resources.planning.reservation.add:operation.uedm.maintenance.resources.planning.reservation.edit:operation.uedm.maintenance.asset.to.handle.approval"}, {"url": "/api/asset-manager/v1/resource-preoccupy-shelve/cancel-occupy", "method": "POST", "operation": "operation.uedm.maintenance.resources.planning.reservation.mounting:operation.uedm.maintenance.asset.to.handle.approval"}, {"url": "/api/asset-manager/v1/resource-preoccupy-shelve/automatic-allocation", "method": "POST", "operation": "operation.uedm.maintenance.resources.planning.reservation.mounting:operation.uedm.maintenance.asset.to.handle.approval"}, {"url": "/api/asset-manager/v1/resource-preoccupy-shelve/get-automatic-allocation-result", "method": "POST", "operation": "operation.uedm.maintenance.resources.planning.reservation.mounting:operation.uedm.maintenance.asset.to.handle.approval"}, {"url": "/api/asset-manager/v1/asset-activity/cancel-asset-occupy", "method": "POST", "operation": "operation.uedm.maintenance.resources.planning.reservation.mounting:operation.uedm.maintenance.asset.to.handle.approval"}, {"url": "/api/asset-manager/v1/resource-management/list", "method": "POST", "operation": "operation.uedm.maintenance.resources.management.view"}, {"url": "/api/asset-manager/v1/resource-management/condition-dimensions", "method": "GET", "operation": "operation.uedm.maintenance.resources.management.view"}, {"url": "/api/asset-manager/v1/resource-management/condition-dimensions-update", "method": "PUT", "operation": "operation.uedm.maintenance.resources.management.view"}, {"url": "/api/asset-manager/v1/resource-management/add", "method": "POST", "operation": "operation.uedm.maintenance.resources.management.add"}, {"url": "/api/asset-manager/v1/resource-management/mo", "method": "POST", "operation": "operation.uedm.maintenance.resources.management.add"}, {"url": "/api/asset-manager/v1/resource-management/batch-update-usage-mode", "method": "POST", "operation": "operation.uedm.maintenance.resources.management.edit"}, {"url": "/api/asset-manager/v1/resource-management/get-by-id", "method": "GET", "operation": "operation.uedm.maintenance.resources.management.edit"}, {"url": "/api/asset-manager/v1/resource-management/update", "method": "POST", "operation": "operation.uedm.maintenance.resources.management.edit"}, {"url": "/api/asset-manager/v1/resource-management/delete", "method": "POST", "operation": "operation.uedm.maintenance.resources.management.delete"}, {"url": "/api/asset-manager/v1/resource-rule/list", "method": "GET", "operation": "operation.uedm.maintenance.resources.regulation.view"}, {"url": "/api/asset-manager/v1/resource-rule/name", "method": "POST", "operation": "operation.uedm.maintenance.resources.regulation.Add:operation.uedm.maintenance.resources.regulation.edit"}, {"url": "/api/asset-manager/v1/resource-rule/insert", "method": "POST", "operation": "operation.uedm.maintenance.resources.regulation.Add"}, {"url": "/api/asset-manager/v1/resource-rule/attribute", "method": "GET", "operation": "operation.uedm.maintenance.resources.regulation.Add:operation.uedm.maintenance.resources.regulation.edit"}, {"url": "/api/asset-manager/v1/resource-rule/update", "method": "POST", "operation": "operation.uedm.maintenance.resources.regulation.edit"}, {"url": "/api/asset-manager/v1/resource-rule/delete", "method": "DELETE", "operation": "operation.uedm.maintenance.resources.regulation.delete"}, {"url": "/api/asset-manager/v1/resource-rule/enable", "method": "POST", "operation": "operation.uedm.maintenance.resources.regulation.enable:operation.uedm.maintenance.resources.regulation.Add:operation.uedm.maintenance.resources.regulation.edit"}, {"url": "/api/asset-manager/v1/resource-threshold", "method": "POST", "operation": "operation.uedm.maintenance.resources.threshold.view"}, {"url": "/api/asset-manager/v1/resource-threshold/update", "method": "POST", "operation": "operation.uedm.maintenance.resources.threshold.edit"}]