package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.eval.executor.EvalQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.dto.EvalDataQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.EvalChecklistNumVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.EvalDataDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * 评价管理-管理门户
 * */
@Slf4j
@Path("/aportal/evaluation")
@Api(value = "评价管理", tags = {"评价管理-管理门户接口"})
@Controller
public class EvalAportalController {
    @Autowired
    private EvalQueryService queryService;

    @POST
    @Path("/all-eval")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取系统所有功能列表", notes = "获取系统所有功能列表", httpMethod = "POST")
    public BaseResult<Object> getAllChecklist() {
        List<EvalChecklistNumVo> list = queryService.queryChecklist();
        return BaseResult.success(list);
    }

    @POST
    @Path("/page-eval")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取指定功能的点赞/踩详情", notes = "获取指定功能的点赞/踩详情", httpMethod = "POST")
    public BaseResult<Object> getEvalData(EvalDataQueryDto queryDto) {
        PageVO<EvalDataDetailVo> pageVO = queryService.queryEvalDataList(queryDto);
        return BaseResult.success(pageVO);
    }
}
