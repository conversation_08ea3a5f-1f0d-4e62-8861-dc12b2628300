package com.zte.uedm.dcdigital.interfaces.web.vo;

/* Started by AICoder, pid:s435aw37b2e60431486c097a70e357458a02729b */
import lombok.Data;

import java.time.LocalDateTime;

/**
 * MsgPageVo 类用于表示分页查询消息的结果。
 *
 * <AUTHOR>
 */
@Data
public class MsgPageVo {
    private String deliveryId;
    /**
     * logId 属性表示消息的日志ID。
     */
    private String logId;

    /**
     * msgName 属性表示消息的名称。
     */
    private String msgName;

    /**
     * msgContent 属性表示消息的内容。
     */
    private String msgContent;

    /**
     * msgTypeName 属性表示消息类型的名称。
     */
    private String msgTypeName;

    /**
     * msgType 属性表示消息的类型（例如：1 - 任务，2 - 与我有关，其他 - 其他）。
     */
    private Integer msgType;

    /**
     * createTime 属性表示消息的创建时间。
     */
    private LocalDateTime createTime;
    private Integer msgStatus;
    private String link;
}
/* Ended by AICoder, pid:s435aw37b2e60431486c097a70e357458a02729b */
