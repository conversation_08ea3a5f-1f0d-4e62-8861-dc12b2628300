package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.gateway.UacApiService;
import com.zte.uedm.dcdigital.domain.utils.AuthUtil;
import com.zte.uedm.dcdigital.interfaces.web.dto.UacUserInfoDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.UacUserInfoQueryDto;
import com.zte.uedm.dcdigital.sdk.system.AuthConstant;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Collections;
import java.util.List;

/**
 * UAC接口 管理模块调用
 */
@Path("/aportal/uac-api")
@Controller
@Slf4j
public class UacApiAportalController {
    @Autowired
    private UacApiService uacApiService;

    /**
     * 根据姓名或工号模糊查询UAC用户信息。
     *
     * @return 包含查询结果的对象，或在失败时返回错误信息
     */
    @ApiOperation("根据姓名/工号模糊查询uac用户信息")
    @POST
    @Path("/query-uac-user-info")
    @Produces(MediaType.APPLICATION_JSON)
    public BaseResult<Object> searchUser(UacUserInfoQueryDto userInfoQueryDto, @Context HttpServletRequest request) {
        String uacToken = AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_TOKEN);
        String accountId = AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_ACCOUNT_ID);
        if (StringUtils.isAnyBlank(uacToken, accountId)) {
            throw new BusinessException(StatusCode.INVALID_TOKEN);
        }
        if (StringUtils.isBlank(userInfoQueryDto.getKey())) {
            return BaseResult.success(Collections.emptyList());
        }
        // 调用服务层方法进行用户信息查询
        List<UacUserInfoDto> list = uacApiService.searchUser(Collections.singletonList(userInfoQueryDto.getKey()), uacToken, accountId);
        return BaseResult.success(list);
    }

}
