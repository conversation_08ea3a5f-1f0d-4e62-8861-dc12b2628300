package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.dcdigital.domain.aggregate.model.PermissionEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.PermissionRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.PermissionConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.PermissionMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.PermissionPo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Repository
public class PermissionRepositoryImpl extends ServiceImpl<PermissionMapper, PermissionPo> implements PermissionRepository {

    @Autowired
    private PermissionMapper permissionMapper;

    /* Started by AICoder, pid:veaa972290z6464149910ad870c84409cda12d67 */
    @Override
    public List<PermissionEntity> selectPermissionByEmployeeId(String employeeId) {
        return convertToEntities(permissionMapper.selectPermissionByEmployeeId(employeeId));
    }
    /* Ended by AICoder, pid:veaa972290z6464149910ad870c84409cda12d67 */
    @Override
    public PermissionEntity selectMenuById(String id) {
        PermissionPo permissionPo = permissionMapper.selectById(id);
        if (permissionPo == null)
        {
            return null;
        }
        return PermissionConverter.INSTANCE.permissionEntityConverterBy(permissionPo);
    }

    /* Started by AICoder, pid:aa915q9c80if2a91491a085b100d55266bd48375 */
    @Override
    public List<PermissionEntity> selectByIds(List<String> ids) {
        List<PermissionPo> permissionPos = permissionMapper.selectBatchIds(ids);
        return convertToEntities(permissionPos);
    }

    @Override
    public List<PermissionEntity> selectByUserId(String userId) {
        List<PermissionPo> permissionPos = permissionMapper.selectByUserId(userId);
        return convertToEntities(permissionPos);
    }

    @Override
    public List<PermissionEntity> selectByUserIdAndResourceId(String userId, String resourceId) {
        List<PermissionPo> permissionPos = permissionMapper.selectByUserIdAndResourceId(userId, resourceId);
        return convertToEntities(permissionPos);
    }

    private List<PermissionEntity> convertToEntities(List<PermissionPo> permissionPos) {
        if (CollectionUtils.isEmpty(permissionPos)) {
            return Collections.emptyList();
        }
        return PermissionConverter.INSTANCE.listPermissionEntityConverterBy(permissionPos);
    }
    /* Ended by AICoder, pid:aa915q9c80if2a91491a085b100d55266bd48375 */

    /* Started by AICoder, pid:d817aff9b3s0ae7148c80a75a077e00608857ac9 */
    @Override
    public List<PermissionEntity> selectPermissionByEmployeeIdAndResourceId(String employeeId, String resourceId) {
        List<PermissionPo> permissionPos = permissionMapper.selectPermissionByEmployeeIdAndResourceId(employeeId, resourceId);
        return convertToEntities(permissionPos);
    }
    /* Ended by AICoder, pid:d817aff9b3s0ae7148c80a75a077e00608857ac9 */

    /* Started by AICoder, pid:x7dddkaf5al48fe1466e0b3170ee2d2a95b3e551 */
    /**
     * 查询所有与指定资源ID相关的实体ID。
     *
     * @param resourceId 资源ID，用于查询相关的实体ID列表。
     * @return 包含查询结果的实体ID列表。如果未找到任何实体ID，返回空列表。
     */
    @Override
    public List<String> selectAllEntityId(String resourceId) {
        return permissionMapper.selectAllEntityId(resourceId);
    }

    /**
     * 根据员工工号和资源ID列表查询权限信息。
     *
     * @param workNumber 员工工号，用于筛选相关的权限信息。
     * @param ids        资源ID列表，用于进一步筛选权限信息。
     * @return 包含查询结果的权限实体列表。如果未找到任何权限信息，返回空列表。
     */
    @Override
    public List<PermissionEntity> selectPermissionByEmployeeIdAndResourceIds(String workNumber, List<String> ids) {
        List<PermissionPo> permissionPos = permissionMapper.selectPermissionByEmployeeIdAndResourceIds(workNumber, ids);
        return convertToEntities(permissionPos);
    }
    /* Ended by AICoder, pid:x7dddkaf5al48fe1466e0b3170ee2d2a95b3e551 */

    @Override
    public List<String> selectAllEntityIds(List<String> resourceIds) {
        return permissionMapper.selectAllEntityIds(resourceIds);
    }

    @Override
    public List<PermissionEntity> selectPermissionByGroupAndResourceIds(List<String> groupIds, List<String> ids) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        List<PermissionPo> permissionPos = permissionMapper.selectPermissionByGroupAndResourceIds(groupIds, ids);
        return convertToEntities(permissionPos);
    }

    @Override
    public List<PermissionEntity> selectPermissionByGroup(List<String> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        List<PermissionPo> permissionPos = permissionMapper.selectPermissionByGroup(groupIds);
        return convertToEntities(permissionPos);
    }
}
