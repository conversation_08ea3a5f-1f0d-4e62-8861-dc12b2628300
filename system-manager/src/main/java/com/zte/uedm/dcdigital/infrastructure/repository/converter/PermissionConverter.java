package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.domain.aggregate.model.PermissionEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.PermissionResourceEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.UserRoleResourceEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.PermissionPo;
import com.zte.uedm.dcdigital.interfaces.web.vo.PermissionResourceVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.PermissionVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.UserRoleResourceVo;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PermissionConverter {

    PermissionConverter INSTANCE = Mappers.getMapper(PermissionConverter.class);

    PermissionEntity permissionEntityConverterBy(PermissionPo permissionPo);

    PermissionPo permissionPoConverterBy(PermissionEntity permissionEntity);

    @Named("permissionEntityConverterBy")
    List<PermissionEntity> listPermissionEntityConverterBy(List<PermissionPo> permissionPos);

    /**
     * 将实体转换为视图对象。
     *
     * @param permissionResourceEntity 实体
     * @return 视图对象
     */
    PermissionResourceVo convertEntityToVo(PermissionResourceEntity permissionResourceEntity);
}
