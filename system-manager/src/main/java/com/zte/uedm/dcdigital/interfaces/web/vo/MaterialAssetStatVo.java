package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 物料资产统计响应VO
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class MaterialAssetStatVo {

    /**
     * 数量统计和变化统计数据列表
     */
    private List<MaterialStatData> dataList;

    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MaterialStatData {

        /**
         * 日期
         */
        private String day;

        /**
         * 物料总数（上架+未上架）
         */
        private Long allNum;

        /**
         * 已上架物料数
         */
        private Long upNum;

        /**
         * 已上架物料变化数
         */
        private Long upChangeNum;

        /**
         * 未上架物料数
         */
        private Long downNum;

        /**
         * 未上架物料变化数
         */
        private Long downChangeNum;

        /**
         * 更新物料数
         */
        private Long updateNum;
    }
}
