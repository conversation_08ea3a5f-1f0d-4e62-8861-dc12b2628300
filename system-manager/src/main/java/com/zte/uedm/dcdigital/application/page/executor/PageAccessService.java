package com.zte.uedm.dcdigital.application.page.executor;

import com.zte.uedm.dcdigital.common.bean.vo.AccessDataVo;
import com.zte.uedm.dcdigital.interfaces.web.dto.PageAccessQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.LoginStatisticsVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface PageAccessService {

    //查询top20
    List<AccessDataVo> queryTop20(PageAccessQueryDto queryDto);
    //查询单个类型统计
    List<AccessDataVo> querySingleOverview(PageAccessQueryDto queryDto);
    //查询所有类型的统计
    List<Map<String, List<AccessDataVo>>> queryAllOverview(PageAccessQueryDto queryDto);
    //查询产品小类top20
    List<AccessDataVo> queryProductCate(PageAccessQueryDto queryDto);

    Map<String, List<AccessDataVo>> queryDeptOverview(PageAccessQueryDto queryDto);
    List<LoginStatisticsVo.UserLoginDetail> queryUserOverview(PageAccessQueryDto queryDto);
    void exportUserOverview(PageAccessQueryDto queryDto, HttpServletResponse response);
    Map<String, AccessDataVo> queryTotal(PageAccessQueryDto queryDto);
    Map<String, Object> queryDeptAll(PageAccessQueryDto queryDto);
}
