package com.zte.uedm.dcdigital.application.stat.scheduler;

import com.zte.uedm.dcdigital.domain.service.BusinessAssetDomainService;
import com.zte.uedm.dcdigital.domain.service.IntelligenceStatDomainService;
import com.zte.uedm.dcdigital.domain.service.MaterialAssetDomainService;
import com.zte.uedm.dcdigital.domain.service.SearchStatDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 统计数据汇聚定时任务
 * 主要用于将日表数据统计到周表、月表、年表
 */
@Slf4j
@Component
public class StatDataAggregationScheduledTasks {

    @Autowired
    private SearchStatDomainService searchStatDomainService;

    @Autowired
    private IntelligenceStatDomainService intelligenceStatDomainService;

    @Autowired
    private MaterialAssetDomainService materialAssetDomainService;

    @Autowired
    private BusinessAssetDomainService businessAssetDomainService;

    /**
     * 每天凌晨1点执行统计数据汇聚任务
     * 将前一天的数据汇聚到周表、月表、年表
     */
    //@Scheduled(cron = "0 0 1 * * ?")
    @Scheduled(cron = "0 0/2 * * * ?")
    public void aggregateStatData() {
        log.info("Statistics data aggregation scheduled task starts");

        // 执行搜索统计数据汇聚
        try {
            log.info("Starting search statistics data aggregation");
            searchStatDomainService.aggregateSearchStatData();
            log.info("Search statistics aggregation task executed successfully");
        } catch (Exception e) {
            log.error("Search statistics aggregation task failed", e);
        }

        // 执行智能体统计数据汇聚
        try {
            log.info("Starting intelligence statistics data aggregation");
            intelligenceStatDomainService.aggregateIntelligenceStatData();
            log.info("Intelligence statistics aggregation task executed successfully");
        } catch (Exception e) {
            log.error("Intelligence statistics aggregation task failed", e);
        }

        // 执行物料资产统计数据汇聚
        try {
            log.info("Starting material asset statistics data aggregation");
            materialAssetDomainService.aggregateMaterialAssetData();
            log.info("Material asset statistics aggregation task executed successfully");
        } catch (Exception e) {
            log.error("Material asset statistics aggregation task failed", e);
        }

        // 执行商机资产统计数据汇聚
        try {
            log.info("Starting business asset statistics data aggregation");
            businessAssetDomainService.aggregateBusinessAssetData();
            log.info("Material asset statistics aggregation task executed successfully");
        } catch (Exception e) {
            log.error("Business asset statistics aggregation task failed", e);
        }



        log.info("Statistics data aggregation scheduled task completed");
    }
}
