package com.zte.uedm.dcdigital.interfaces.web.controller;


import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.service.StatisticsLoginService;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.LoginStatisticsVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.StatisticOverviewVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

@Slf4j
@Path("uportal/system")
@Api(value = "系统访问统计", tags = {"系统统计接口"})
@Controller
public class SystemStatisticController {

    @Autowired
    private StatisticsLoginService statisticsLoginService;


    /**
     * 总览模块
     * @param loginQueryDto
     * @return
     */
    @POST
    @Path("/statisticOverView")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "统计总览", notes = "统计总览", httpMethod = "POST")
    public BaseResult<StatisticOverviewVo> statisticOverView(LoginQueryDto loginQueryDto) {
        StatisticOverviewVo loginStatisticsVo = statisticsLoginService.statisticOverView(loginQueryDto);
        return BaseResult.success(loginStatisticsVo);
    }

}
