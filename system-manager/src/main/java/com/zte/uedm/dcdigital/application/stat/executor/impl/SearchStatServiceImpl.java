package com.zte.uedm.dcdigital.application.stat.executor.impl;

import com.zte.uedm.dcdigital.application.stat.executor.SearchStatService;
import com.zte.uedm.dcdigital.domain.service.SearchStatDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.SearchStatisticTopPageVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.SearchStatisticVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 搜索统计服务实现类
 */
@Service
@Slf4j
public class SearchStatServiceImpl implements SearchStatService {

    @Autowired
    private SearchStatDomainService searchStatDomainService;

    @Override
    public SearchStatisticVo statisticSearch(SearchStatisticQueryDto queryDto) {
        log.info("SearchStatService.statisticSearch start, queryDto: {}", queryDto);
        
        // 参数验证
        validateSearchStatisticQuery(queryDto);
        
        // 调用搜索统计领域服务
        return searchStatDomainService.querySearchStatistic(queryDto);
    }

    @Override
    public SearchStatisticTopPageVo statisticSearchTop(SearchStatisticTopQueryDto queryDto) {
        log.info("SearchStatService.statisticSearchTop start, queryDto: {}", queryDto);

        // 参数验证
        validateSearchStatisticTopQuery(queryDto);

        // 调用搜索统计领域服务
        return searchStatDomainService.querySearchStatisticTop(queryDto);
    }

    @Override
    public void statisticSearchExport(SearchStatisticExportDto exportDto) {
        log.info("SearchStatService.statisticSearchExport start, exportDto: {}", exportDto);
        
        // 参数验证
        validateSearchStatisticExport(exportDto);
        
        // 调用搜索统计领域服务
        searchStatDomainService.exportSearchStatistic(exportDto);
    }

    @Override
    @Async
    public void searchBuryingPoint(SearchBuryingPointDto buryingPointDto) {
        log.info("SearchStatService.buryingPoint start, buryingPointDto: {}", buryingPointDto);

        try {
            // 异步处理埋点数据
            searchStatDomainService.processBuryingPoint(buryingPointDto);
        } catch (Exception e) {
            log.error("buryingPoint error", e);
            // 埋点处理失败不应该影响主流程
        }
    }

    @Override
    public void triggerSearchStatAggregation(SearchStatAggregationDto aggregationDto) {
        log.info("Manual trigger search statistics aggregation start, aggregationDto: {}", aggregationDto);

        try {
            // 参数验证
            validateAggregationRequest(aggregationDto);

            // 调用搜索统计领域服务的汇聚方法
            searchStatDomainService.aggregateSearchStatData(aggregationDto);
            log.info("Manual trigger search statistics aggregation completed successfully");
        } catch (Exception e) {
            log.error("Manual trigger search statistics aggregation failed", e);
            throw new RuntimeException("Failed to trigger search statistics aggregation", e);
        }
    }

    /**
     * 验证汇聚请求参数
     */
    private void validateAggregationRequest(SearchStatAggregationDto aggregationDto) {
        if (aggregationDto == null) {
            return; // 允许空参数，使用默认值
        }

        // 验证汇聚类型
        String aggregationType = aggregationDto.getAggregationType();
        if (aggregationType != null &&
            !aggregationType.equals("all") &&
            !aggregationType.equals("week") &&
            !aggregationType.equals("month") &&
            !aggregationType.equals("year")) {
            throw new IllegalArgumentException("Invalid aggregation type: " + aggregationType +
                ". Valid values are: all, week, month, year");
        }

        // 验证日期格式
        String targetDate = aggregationDto.getTargetDate();
        if (targetDate != null && !targetDate.matches("\\d{8}")) {
            throw new IllegalArgumentException("Invalid date format: " + targetDate +
                ". Expected format: yyyyMMdd");
        }
    }

    private void validateSearchStatisticQuery(SearchStatisticQueryDto queryDto) {
        if (queryDto == null) {
            throw new IllegalArgumentException("查询参数不能为空");
        }
    }

    /**
     * 验证搜索统计TOP查询参数
     * 重构后降低圈复杂度，提高可维护性
     */
    private void validateSearchStatisticTopQuery(SearchStatisticTopQueryDto queryDto) {
        // 基础参数验证
        validateBasicQueryParameters(queryDto.getDeptId(), queryDto.getStartTime(), queryDto.getEndTime());

        // 时间类型验证
        validateTimeType(queryDto.getTimeType());

        // 业务类型验证（TOP查询不允许全部类型）
        validateTopQueryBusinessType(queryDto.getType());
    }

    /**
     * 验证基础查询参数（公共方法）
     */
    private void validateBasicQueryParameters(String deptId, String startTime, String endTime) {
        if (StringUtils.isBlank(deptId)) {
            throw new IllegalArgumentException("Department ID cannot be empty");
        }

        if (StringUtils.isBlank(startTime)) {
            throw new IllegalArgumentException("Start time cannot be empty");
        }

        if (StringUtils.isBlank(endTime)) {
            throw new IllegalArgumentException("End time cannot be empty");
        }
    }

    /**
     * 验证TOP查询的业务类型参数
     */
    private void validateTopQueryBusinessType(Integer type) {
        if (type == null) {
            throw new IllegalArgumentException("Type cannot be null");
        }

        // TOP查询允许的类型：2-物料，3-文档，4-FAQ（不允许0-全部）
        if (type < 2 || type > 4) {
            throw new IllegalArgumentException("Type must be 2-4 (2:material, 3:document, 4:FAQ)");
        }
    }

    /**
     * 验证搜索统计导出参数
     * 重构后降低圈复杂度，提高可维护性
     */
    private void validateSearchStatisticExport(SearchStatisticExportDto exportDto) {
        // 基础参数验证
        validateBasicExportParameters(exportDto);

        // 时间和类型参数验证
        validateTimeAndTypeParameters(exportDto);
    }

    /**
     * 验证基础导出参数
     */
    private void validateBasicExportParameters(SearchStatisticExportDto exportDto) {
        if (exportDto == null) {
            throw new IllegalArgumentException("Export parameters cannot be null");
        }

        // 复用公共的基础参数验证方法
        validateBasicQueryParameters(exportDto.getDeptId(), exportDto.getStartTime(), exportDto.getEndTime());
    }

    /**
     * 验证时间类型和业务类型参数
     */
    private void validateTimeAndTypeParameters(SearchStatisticExportDto exportDto) {
        validateTimeType(exportDto.getTimeType());
        validateBusinessType(exportDto.getType());
    }

    /**
     * 验证时间类型参数
     */
    private void validateTimeType(Integer timeType) {
        if (timeType == null) {
            throw new IllegalArgumentException("Time type cannot be null");
        }

        if (timeType < 1 || timeType > 4) {
            throw new IllegalArgumentException("Time type must be 1-4 (1:day, 2:week, 3:month, 4:year)");
        }
    }

    /**
     * 验证业务类型参数
     */
    private void validateBusinessType(Integer type) {
        if (type == null) {
            throw new IllegalArgumentException("Type cannot be null");
        }

        // 允许的类型：0-全部，2-物料，3-文档，4-FAQ（不允许1）
        if (type < 0 || type > 4 || type == 1) {
            throw new IllegalArgumentException("Type must be 0,2,3,4 (0:all, 2:material, 3:document, 4:FAQ)");
        }
    }
}
