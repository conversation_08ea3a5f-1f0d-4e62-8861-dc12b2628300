package com.zte.uedm.dcdigital.interfaces.web.controller;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.service.DocAssetDomainService;
import com.zte.uedm.dcdigital.domain.service.DocDownStatsDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Collections;
import java.util.List;

//资产数量统计-文档入口
@Slf4j
@Path("/uportal/asset")
@Api(value = "文档数量统计", tags = {"文档数量统计接口"})
@Controller
public class DocUportalAssetController {
    @Autowired
    private DocAssetDomainService docAssetDomainService;

    @POST
    @Path("/test")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "文档数量统计定时任务测试",
            notes = "文档数量统计定时任务测试",
            httpMethod = "POST"
    )
    public BaseResult<String> test() {
        docAssetDomainService.synchronizeDocAssetData();
        return BaseResult.success("数据记录成功");
    }
    @POST
    @Path("/addRecord")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "文档数量统计埋点",
            notes = "文档数量统计埋点",
            httpMethod = "POST"
    )
    public BaseResult<String> addRecordDocDown(DocAssetAddDto addDto) {
        docAssetDomainService.addDocAssetRecord(addDto);
        return BaseResult.success("数据记录成功");
    }
    @POST
    @Path("/statDocument")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "用户门户-看板-系统访问统计-文档",
            notes = "用户门户-看板-系统访问统计-文档",
            httpMethod = "POST"
    )
    public BaseResult<DocAssetNumVo> getStatDocument(DocAssetQueryDto queryDto) {
        DocAssetNumVo statDocument = docAssetDomainService.getStatDocument(queryDto);
        return BaseResult.success(statDocument);
    }

    @POST
    @Path("/statJuniorStatistics")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "用户门户-看板-系统访问统计-文档-下级统计",
            notes = "用户门户-看板-系统访问统计-文档-下级统计",
            httpMethod = "POST"
    )
    public BaseResult<DocAssetJuniorNumVo> getStatJuniorStatistics(DocAssetJuniorQueryDto queryDto) {
        Integer pageNum = queryDto.getPageNum();
        Integer pageSize = queryDto.getPageSize();
        if (pageNum == null || pageSize == null) {
            // 参数无效时返回空的分页信息
            return BaseResult.success(new DocAssetJuniorNumVo());
        }
        DocAssetJuniorNumVo statJuniorStatistics = docAssetDomainService.getStatJuniorStatistics(queryDto);
        List<DocAssetJuniorNumVo.DocAssetJuniorNum> juniorNumList = pageTool(statJuniorStatistics.getJuniorList(), pageNum, pageSize);
        statJuniorStatistics.setTotal(statJuniorStatistics.getJuniorList().size());
        statJuniorStatistics.setJuniorList(juniorNumList);
        return BaseResult.success(statJuniorStatistics);
    }

    @POST
    @Path("/statisticExport")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "导出文档数量统计", notes = "导出为 Excel", httpMethod = "POST")
    public void exportStatistics(DocAssetJuniorQueryDto queryDto, @Context HttpServletResponse response) {
        log.info("exportStatistics queryDto:{}",queryDto);
        docAssetDomainService.export(queryDto,response);
    }

    private <T >List<T> pageTool(List<T> list, Integer pageNum, Integer pageSize) {
        try {
            int total = list.size();
            int fromIndex = (pageNum - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, total);

            if (fromIndex >= total) {
                return Collections.emptyList();
            }

            return list.subList(fromIndex, toIndex);
        } catch (NumberFormatException e) {
            log.error("Invalid pagination parameters for category stat, pageNum: {}, pageSize: {}", pageNum, pageSize, e);
            return list.subList(0, Math.min(10, list.size())); // 默认返回前10条
        }
    }
}
