package com.zte.uedm.dcdigital.interfaces.web.controller.stat;

import com.zte.uedm.dcdigital.application.stat.executor.FaqStatService;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

@Slf4j
@Path("/uportal/asset")
@Api(value = "faq统计", tags = {"faq统计接口"})
@Controller
public class FaqStatUportalController {

    @Autowired
    private FaqStatService faqStatService;

    @POST
    @Path("/statFaq")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "faq统计", notes = "faq统计", httpMethod = "POST")
    public BaseResult<FaqStatisticVo> statisticFaq(FaqStatisticDto daqStatisticDto) {
        log.info("statisticFaq start, FaqStatisticDto:{}", daqStatisticDto);
        FaqStatisticVo faqStatisticVo = faqStatService.statisticFaq(daqStatisticDto);
        return BaseResult.success(faqStatisticVo);
    }


    @POST
    @Path("/statFaqExport")
    @Produces({MediaType.APPLICATION_OCTET_STREAM})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "faq统计导出", notes = "faq统计", httpMethod = "POST")
    public void statisticFaqExport(FaqStatisticExportDto exportDto, @Context HttpServletResponse response) {
        log.info("statisticFaqExport start, exportDto:{}", exportDto);
        faqStatService.statisticFaqExport(exportDto, response);
    }


    @POST
    @Path("/buryingPoint/faq")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "前端埋点接口", notes = "前端埋点接口", httpMethod = "POST")
    public BaseResult<Object> buryingPoint(AssetStatBuryingPointDto buryingPointDto) {
        log.info("buryingPoint start, buryingPointDto: {}", buryingPointDto);
        faqStatService.buryingPoint(buryingPointDto);
        return BaseResult.success();
    }
}
