package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocAssetEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DocAssetWeekMapper {
    List<DocAssetEntity> getDocNumData(@Param("beginTime") int beginTime, @Param("endTime") int endTime, @Param("ids") List<String> ids);

    List<DocAssetEntity> getDataByTime(@Param("day") int day);

    DocAssetEntity getBeforeWeekEntity(@Param("beforeWeek") int beforeWeek, @Param("id") String id);

    void batchInsert(@Param("list") List<DocAssetEntity> list);

    void batchUpdate(@Param("list") List<DocAssetEntity> list);

    List<DocAssetEntity> getDataByDayAndIds(@Param("day")Integer day, @Param("ids")List<String> ids);

    List<DocAssetEntity> selectByTimeRange(@Param("startTime")Integer startTime, @Param("endTime")Integer endTime);
}
