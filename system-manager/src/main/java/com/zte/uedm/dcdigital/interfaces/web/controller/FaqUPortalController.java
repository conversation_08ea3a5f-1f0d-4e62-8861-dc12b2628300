package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.faq.executor.FaqCommandService;
import com.zte.uedm.dcdigital.application.faq.executor.FaqQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.FaqMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.FaqPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.FaqAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.FaqQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.FaqUpdateDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.FaqVo;
import com.zte.uedm.dcdigital.log.annotation.DcOperationLog;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import com.zte.uedm.dcdigital.security.annotation.DcPermission;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@Path("uportal/faq")
@Api(value = "FAQ", tags = {"FAQ接口"})
@Controller
@Slf4j
public class FaqUPortalController {

    @Autowired
    private FaqCommandService faqCommandService;

    @Autowired
    private FaqQueryService faqQueryService;

    @GET
    @Path("/{id}")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "查询FAQ详情", notes = "查询FAQ详情", httpMethod = "GET")
    public BaseResult<Object> getFaq(@Valid @NotBlank @PathParam("id") String id) {
        FaqVo faqVo = faqQueryService.getById(id);
        return BaseResult.success(faqVo);
    }

    @POST
    @Path("/query")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "搜索FAQ", notes = "搜索FAQ", httpMethod = "POST")
    public BaseResult<Object> queryFaq(FaqQueryDto faqQueryDto) {
        PageVO<FaqVo> faqVos = faqQueryService.queryByCondition(faqQueryDto);
        return BaseResult.success(faqVos);
    }

    @POST
    @Path("/add")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "新增FAQ", notes = "新增FAQ", httpMethod = "POST")
    @DcPermission(value = {"system.faq.add"}, checkResource = true)
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-system-manager",
            operation = "FaqAddDescription", targetClass = FaqAddDto.class)
    public BaseResult<Object> insertFaq(FaqAddDto faqAddDto) {
        faqCommandService.createFaq(faqAddDto);
        return BaseResult.success();
    }

    @POST
    @Path("/update")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "更新FAQ", notes = "更新FAQ", httpMethod = "POST")
    @DcPermission(value = {"system.faq.edit"})
    @DcOperationLog(method = OperationMethodEnum.UPDATE, module = "module-system-manager",mapperName = FaqMapper.class,
            operation = "FaqUpdateDescription", targetClass = FaqUpdateDto.class)
    public BaseResult<Object> updateFaq(FaqUpdateDto faqUpdateDto) {
        faqCommandService.updateFaq(faqUpdateDto);
        return BaseResult.success();
    }

    @POST
    @Path("/delete/{id}")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "删除FAQ", notes = "删除FAQ", httpMethod = "POST")
    @DcPermission(value = {"system.faq.delete"})
    @DcOperationLog(method = OperationMethodEnum.DELETE, module = "module-system-manager",mapperName = FaqMapper.class,
            operation = "FaqPoDescription", targetClass = FaqPo.class)
    public BaseResult<Object> deleteFaq(@Valid @NotBlank @PathParam("id") String id) {
        faqCommandService.deleteFaq(id);
        return BaseResult.success();
    }
}
