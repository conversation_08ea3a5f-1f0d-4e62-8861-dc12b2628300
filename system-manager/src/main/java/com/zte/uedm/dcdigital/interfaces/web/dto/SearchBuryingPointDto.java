package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 前端埋点DTO
 */
@Getter
@Setter
@ToString
public class SearchBuryingPointDto {

    /**
     * 产品小类id
     */
    private String productCategoryId;

    /**
     * 操作类型：
     * 1-页面访问-品牌，2-页面访问-物料，3-页面访问-文档，4-页面访问-FAQ，
     * 5-文档下载-预览，6-文档下载-下载，7-搜索-物料，8-搜索-文档，9-搜索-FAQ
     */
    @NotNull(message = "操作类型不能为空")
    private Integer operationType;

    @NotNull(message = "用户id不能为空")
    private String userId;
}
