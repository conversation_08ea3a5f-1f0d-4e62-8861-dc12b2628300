package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 搜索统计日表PO
 */
@Getter
@Setter
@ToString
@TableName("search_stat_day")
public class SearchStatDayPo {

    /**
     * 主表id
     */
    private String id;

    /**
     * 日期（例：20250620）
     */
    private String day;

    /**
     * 组织ID
     */
    private String deptId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 产品小类id
     */
    private String productCategoryId;

    /**
     * 物料次数
     */
    private String materialNum;

    /**
     * 文档次数
     */
    private String fileNum;

    /**
     * FAQ次数
     */
    private String faqNum;

    /**
     * 记录时间
     */
    private String createTime;
}
