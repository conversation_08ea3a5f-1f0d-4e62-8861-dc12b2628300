/* Started by AICoder, pid:obc3egb47akc092149310abe00da9a44f0294222 */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.UserEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.UserPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.UserDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UserPoConverter {

    // 使用常量来存储实例，确保线程安全和单例模式
    UserPoConverter INSTANCE = Mappers.getMapper(UserPoConverter.class);

    /**
     * 将UserDto转换为UserEntity。
     *
     * @param userDto 待转换的UserDto对象
     * @return 转换后的UserEntity对象
     */
    UserEntity convertDtoToEntity(UserDto userDto);

    /**
     * 将UserEntity转换为UserPo。
     *
     * @param userEntity 待转换的UserEntity对象
     * @return 转换后的UserPo对象
     */
    UserPo convertEntityToPo(UserEntity userEntity);

    /**
     * 将UserPo转换为UserEntity。
     *
     * @param userPo 待转换的UserPo对象
     * @return 转换后的UserEntity对象
     */
    UserEntity convertPoToEntity(UserPo userPo);

    List<UserEntity> convertPosToEntities(List<UserPo> userPos);

    /**
     * 将UserEntity转换为UserVo。
     *
     * @param userEntity 待转换的UserEntity对象
     * @return 转换后的UserVo对象
     */
    UserVo convertEntityToVo(UserEntity userEntity);
    List<UserVo> convertEntitiesToVos(List<UserEntity> userEntities);
}

/* Ended by AICoder, pid:obc3egb47akc092149310abe00da9a44f0294222 */