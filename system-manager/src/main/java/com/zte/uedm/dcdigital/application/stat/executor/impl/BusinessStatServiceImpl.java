/* Started by AICoder, pid:b6672za32ep2fb31466c0951e1b9a70084d9d162 */
package com.zte.uedm.dcdigital.application.stat.executor.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.dcdigital.application.stat.executor.BusinessStatService;
import com.zte.uedm.dcdigital.common.bean.brand.BusinessLectotypeStatisticVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.common.constant.SystemConstants;
import com.zte.uedm.dcdigital.domain.utils.StatCurrentDateUtils;
import com.zte.uedm.dcdigital.domain.utils.StatTimeUtils;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BusinessStatMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BusinessStatisticPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticVo;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class BusinessStatServiceImpl implements BusinessStatService {

    @Autowired
    private ProductService productService;

    @Autowired
    private BusinessStatMapper businessStatMapper;

    @Override
    public List<BusinessStatisticVo> statBusiness(BusinessStatisticQueryDto queryDto) {
        /**
         * 统计业务数据。
         *
         * @param queryDto 查询条件传输对象
         * @return 包含统计结果的 BusinessStatisticVo 对象列表
         */
        List<String> categoryIds = productService.selectByCategoryId(queryDto.getProductCategoryId());
        if (null == categoryIds || categoryIds.size() == 0) {
            return Collections.emptyList();
        }
        log.info("categoryIds:{}", JSON.toJSONString(categoryIds));

        List<String> completeTimeRange = StatTimeUtils.generateCompleteTimeRange(
                queryDto.getStartTime(), queryDto.getEndTime(), queryDto.getTimeType());
        log.info("completeTimeRange:{}", JSON.toJSONString(completeTimeRange));
        queryDto.setCompleteTimeRange(completeTimeRange);
        queryDto.setProductCategoryIds(categoryIds);

        List<BusinessStatisticVo> list = getBusinessStatData(queryDto);
        for (BusinessStatisticVo vo : list) {
            switch (queryDto.getTimeType()) {
                case 1: // 天
                    vo.setDay(StatTimeUtils.formatDayDisplay(vo.getDay()));
                    break;
                case 2: // 周
                    vo.setDay(StatTimeUtils.formatWeekDisplay(vo.getDay()));
                    break;
                case 3: // 月
                    vo.setDay(StatTimeUtils.formatMonthDisplay(vo.getDay()));
                    break;
                case 4: // 年
                    vo.setDay(StatTimeUtils.formatYeayDisplay(vo.getDay()));
                    break;
                default:
                    break;
            }

        }
        return list;
    }

    /* Started by AICoder, pid:p92dbvcea6o84d114f2c085d2051bc60519516f9 */
    @Override
    public void synchronizeBusinessStatData(String time) {
        /**
         * 同步业务统计数据。
         * 该方法用于定期同步业务统计数据，确保数据的一致性和准确性。
         */
        Integer previousDayNumber = StatCurrentDateUtils.getPreviousDayNumber();

        List<ProductCategoryInfoVo> list = productService.selectByNodeType(SystemConstants.STR_THREE);
        if (list.size() == 0) {
            return;
        }
        // 统计日表
        for (ProductCategoryInfoVo p : list) {
            BusinessStatisticPo po = new BusinessStatisticPo();
            po.setId(UUID.randomUUID().toString());
            po.setProductCategoryId(p.getId());
            po.setDay(String.valueOf(previousDayNumber));
            po.setCreateTime(DateTimeUtils.getCurrentTime());
            BusinessLectotypeStatisticVo vo = productService.demandLectotypeStat(p.getId(), time);
            if (null != vo) {
                po.setAllLectotypeNum(vo.getAllLectotypeNum());
                po.setBidLectotypeNum(vo.getBidLectotypeNum());
                po.setAppointLectotypeNum(vo.getAppointLectotypeNum());
            } else {
                po.setAllLectotypeNum(0L);
                po.setBidLectotypeNum(0L);
                po.setAppointLectotypeNum(0L);
            }
            // todo 任务数据
            po.setAllTaskNum(0L);
            po.setNormalTaskNum(0L);
            po.setExtensionTaskNum(0L);
            businessStatMapper.delBusinessStatDay(po);
            businessStatMapper.addBusinessStatDay(po);

            // 统计周表、月表和年表
            statOther(po);
        }
    }

    /* Started by AICoder, pid:xf113jd01dj17ea14d4d09461061d13c13932a58 */
    @Override
    public BusinessStatisticVo overviewStatBusiness(String startTime,String endTime, Integer timeType) {
        /**
         * 统计业务数据概览。
         *
         * @param endTime   结束时间（格式：YYYY-MM-DD）
         * @param timeType  时间类型：1-天，2-周，3-月，4-年
         * @return 包含统计结果的 BusinessStatisticVo 对象
         */
        List<BusinessStatisticVo> vo = new ArrayList<>();
        switch (timeType) {
            case 1: // 天
                vo = businessStatMapper.selectOverviewStatistic(startTime,endTime, "business_stat_day");
                break;
            case 2: // 周
                vo = businessStatMapper.selectOverviewStatistic(startTime,endTime, "business_stat_week");
                break;
            case 3: // 月
                vo = businessStatMapper.selectOverviewStatistic(startTime,endTime, "business_stat_month");
                break;
            case 4: // 年
                vo = businessStatMapper.selectOverviewStatistic(startTime,endTime, "business_stat_year");
                break;
            default:
                break;
        }

        if (null == vo || vo.isEmpty()) {
            return new BusinessStatisticVo();
        }

        return vo.get(0);
    }
    /* Ended by AICoder, pid:xf113jd01dj17ea14d4d09461061d13c13932a58 */

    private void statOther(BusinessStatisticPo po) {
        /**
         * 统计周表、月表和年表。
         *
         * @param po 业务统计持久化对象
         */
        Integer previousWeekNumber = StatCurrentDateUtils.getPreviousWeekOfYear();
        po.setDay(String.valueOf(previousWeekNumber));
        businessStatMapper.delBusinessStatWeek(po);
        businessStatMapper.addBusinessStatWeek(po);

        Integer previousMonthNumber = StatCurrentDateUtils.getPreviousMonthOfYear();
        po.setDay(String.valueOf(previousMonthNumber));
        businessStatMapper.delBusinessStatMonth(po);
        businessStatMapper.addBusinessStatMonth(po);

        Integer previousYearNumber = StatCurrentDateUtils.getPreviousYear();
        po.setDay(String.valueOf(previousYearNumber));
        businessStatMapper.delBusinessStatYear(po);
        businessStatMapper.addBusinessStatYear(po);
    }
    /* Ended by AICoder, pid:p92dbvcea6o84d114f2c085d2051bc60519516f9 */

    private List<BusinessStatisticVo> getBusinessStatData(BusinessStatisticQueryDto queryDto) {
        /**
         * 根据查询条件获取业务统计数据。
         *
         * @param queryDto 查询条件传输对象
         * @return 包含统计结果的 BusinessStatisticVo 对象列表
         */
        switch (queryDto.getTimeType()) {
            case 1: // 天
                return businessStatMapper.selectBusinessStatisticByDay(queryDto);
            case 2: // 周
                return businessStatMapper.selectBusinessStatisticByWeek(queryDto);
            case 3: // 月
                return businessStatMapper.selectBusinessStatisticByMonth(queryDto);
            case 4: // 年
                return businessStatMapper.selectBusinessStatisticByYear(queryDto);
            default:
                return Collections.emptyList();
        }
    }
}
/* Ended by AICoder, pid:b6672za32ep2fb31466c0951e1b9a70084d9d162 */