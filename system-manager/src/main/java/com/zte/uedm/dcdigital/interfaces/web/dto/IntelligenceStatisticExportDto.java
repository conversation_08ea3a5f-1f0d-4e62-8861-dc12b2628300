package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 智能体统计导出DTO
 */
@Getter
@Setter
@ToString
public class IntelligenceStatisticExportDto {

    /**
     * 部门id
     */
    @NotBlank(message = "部门id不能为空")
    private String deptId;

    /**
     * 开始时间
     */
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    /**
     * 结束时间
     */
    @NotBlank(message = "结束时间不能为空")
    private String endTime;

    /**
     * 时间类型：1-天，2-周，3-月，4-年
     */
    @NotNull(message = "时间类型不能为空")
    private Integer timeType;

    private String pageSize;

    private String pageNum;
}
