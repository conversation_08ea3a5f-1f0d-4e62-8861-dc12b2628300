package com.zte.uedm.dcdigital.interfaces.inner.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * UAC配置数据传输对象
 * 用于向第三方系统提供UAC配置信息
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "UAC配置信息", description = "UAC配置数据传输对象")
public class UacConfigDto implements Serializable {

    // 基础UAC认证配置
    @ApiModelProperty(value = "环境标识", notes = "dev：测试/开发，prod：生产", example = "dev")
    private String env;

    @ApiModelProperty(value = "租户ID", notes = "默认10001", example = "10001")
    private String tenantId;

    @ApiModelProperty(value = "应用ID", notes = "用于页面分享功能的iCenter通知", example = "706623187297")
    private String appId;

    @ApiModelProperty(value = "服务发行端", notes = "UAC服务发行端地址", example = "https://uactest.zte.com.cn:5555/zte-sec-uac-iportalbff/")
    private String issuer;

    @ApiModelProperty(value = "客户端ID", notes = "系统编码", example = "706623187297")
    private String clientId;

    @ApiModelProperty(value = "客户端密钥", notes = "客户端认证密钥")
    private String clientSecret;

    @ApiModelProperty(value = "访问密钥", notes = "对应iCenter通知API的appCode")
    private String accessKey;

    @ApiModelProperty(value = "访问密钥", notes = "对应iCenter通知API的secretKey")
    private String accessSecret;

    @ApiModelProperty(value = "密钥", notes = "系统密钥")
    private String secretKey;

    @ApiModelProperty(value = "加密密钥", notes = "加密X-Auth-Value所需密钥")
    private String encryptionKey;

    @ApiModelProperty(value = "业务系统基础URL", notes = "业务系统基础URL", example = "https://************")
    private String baseUrl;

    // iCenter通知API相关配置
    @ApiModelProperty(value = "iCenter基础URL", notes = "iCenter通知API基础URL", example = "https://icenter-systest.test.zte.com.cn")
    private String icenterBaseUrl;

    @ApiModelProperty(value = "发送路径", notes = "iCenter通知发送接口路径", example = "/zte-icenter-notice-message/notice/template/send")
    private String sendPath;

    // 页面分享功能模板配置
    @ApiModelProperty(value = "邮件模板ID", notes = "邮件分享模板ID", example = "Z2025062410582407294")
    private String emailTemplateId;

    @ApiModelProperty(value = "消息模板ID", notes = "消息分享模板ID", example = "W2025061916511610381")
    private String messageTemplateId;

    @ApiModelProperty(value = "默认发件人", notes = "邮件分享默认发件人", example = "<EMAIL>")
    private String defaultMailFrom;
}
