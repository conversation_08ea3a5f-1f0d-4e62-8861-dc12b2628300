package com.zte.uedm.dcdigital.interfaces.web.vo;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@ToString
public class DocDownUserStatisticsVo {

    //用户名
    private String userName;
    //总数
    private String totalNum;

    private Map<String, String> periodTimes;

    private List<Map<String, String>> periodDocDownTimes;// <周期, 次数>
}
