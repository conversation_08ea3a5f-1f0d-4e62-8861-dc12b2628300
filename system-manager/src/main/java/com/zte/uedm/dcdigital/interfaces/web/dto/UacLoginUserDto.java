package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/* Started by AICoder, pid:kb1f621347s3e1514826090fa0adc64a4455e87c */
/**
 * UAC登录用户信息对象。
 */
@Getter
@Setter
@ToString
public class UacLoginUserDto {
    /**
     * 用户的唯一标识符或工号。
     */
    private String id;

    /**
     * 用户的姓名。
     */
    private String name;

    /**
     * 用户的登录名，通常用于登录系统的用户名。
     */
    private String login;


    /**
     * 用户的邮箱地址。
     */
    private String email;

    /**
     * 用户的手机号码。
     */
    private String phone;

    /**
     * 用户的英文姓名。
     */
    private String englishName;
}

/* Ended by AICoder, pid:kb1f621347s3e1514826090fa0adc64a4455e87c */
