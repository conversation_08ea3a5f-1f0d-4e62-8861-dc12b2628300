/* Started by AICoder, pid:o22742c59bdea9b142ae0be7805c5347f2a53f4d */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 登录年统计数据持久化对象
 */
@Getter
@Setter
@ToString
@TableName("login_stat_year")
public class LoginStatYearPo {
    /**
     * 主键ID（UUID格式）
     */
    private String id;

    /**
     * 统计年份（格式：YYYY，如2025表示2025年）
     */
    @TableField("day")
    private Integer day;

    /**
     * 部门/组织ID
     */
    @TableField("dept_id")
    private String deptId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 本年累计登录天数
     */
    @TableField("num")
    private Integer num;


    // 查询条件字段（非数据库字段）
    @TableField(exist = false)
    private Integer beginTime;

    @TableField(exist = false)
    private Integer endTime;
}

/* Ended by AICoder, pid:o22742c59bdea9b142ae0be7805c5347f2a53f4d */