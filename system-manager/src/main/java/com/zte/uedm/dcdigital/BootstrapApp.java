package com.zte.uedm.dcdigital;

import com.zte.oes.dexcloud.commons.annotation.DexCloudApplication;
import com.zte.oes.dexcloud.commons.component.configclient.annotation.EnableConfigClient;
import com.zte.oes.dexcloud.commons.component.retrofit.annotation.EnableRetrofitRPC;
import com.zte.oes.dexcloud.ftpclient.api.annotation.EnableFtpClient;
import com.zte.oes.dexcloud.i18n.api.annotation.EnableI18n;
import com.zte.oes.dexcloud.redis.redisson.mult.annotation.EnableRedissonMult;
import lombok.extern.slf4j.Slf4j;
import org.dexcloud.springboot.kafka.config.annotation.EnableKafka;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@DexCloudApplication
@EnableKafka
@EnableCaching
@EnableRetrofitRPC
@EnableRedissonMult
@EnableConfigClient
@EnableAsync
@EnableFtpClient
@EnableScheduling
@EnableI18n
@ComponentScan(basePackages = {"com.zte.uedm.dcdigital","com.zte.uedm.component.db.scheduled"})
@MapperScan("com.zte.uedm.dcdigital.infrastructure.repository")
@Slf4j
public class BootstrapApp
{
    public static void main(String[] args)
    {
        log.info("服务成功");
        SpringApplication.run(BootstrapApp.class, args);log.info("启动成功");
    }
}
