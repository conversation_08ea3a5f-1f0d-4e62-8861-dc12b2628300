
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;


import com.zte.uedm.dcdigital.domain.common.valueobj.ResourceDepartmentRoleObj;
import com.zte.uedm.dcdigital.infrastructure.repository.po.AuthDepartmentRoleResourcePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户组角色资源映射器接口，继承自BaseMapper以利用MyBatis Plus的通用CRUD方法。
 */
@Mapper
public interface AuthDepartmentRoleResourceMapper extends BaseMapper<AuthDepartmentRoleResourcePo> {


    List<String> queryAreaByDepartmentIdAndRoleCode(@Param("departmentIds") List<String> departmentIds, @Param("roleCode") String roleCode);

    List<ResourceDepartmentRoleObj> queryResourceByIdAndRoleCode(@Param("resourceIds") List<String> resourceIds, @Param("roleCode") String roleCode);

    List<ResourceDepartmentRoleObj> queryByResourceAndRole(@Param("entityId") String entityId, @Param("roleCodes") List<String> roleCodes);
}
