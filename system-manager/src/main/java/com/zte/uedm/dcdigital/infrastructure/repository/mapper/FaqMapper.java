/* Started by AICoder, pid:ufca2u067c9995a14e6e08a910104d34981909eb */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.common.bean.document.DocumentCitedVo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.FaqPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * FAQ数据访问对象接口
 */
@Mapper
public interface FaqMapper extends BaseMapper<FaqPo> {

    /**
     * 根据条件查询FAQ列表。
     *
     * @param searchText  搜索文本
     * @param type        类型
     * @param resourceId  资源ID
     * @param sort        排序字段
     * @param order       排序顺序（升序/降序）
     * @return 符合条件的FAQ列表
     */
    List<FaqPo> queryByCondition(
            @Param("searchText") String searchText,
            @Param("type") Integer type,
            @Param("resourceId") String resourceId,
            @Param("sort") String sort,
            @Param("order") String order);

    /**
     * 更新FAQ记录。
     *
     * @param faqPo 需要更新的FAQ对象
     */
    void update(FaqPo faqPo);

    List<DocumentCitedVo> selectCitedList(List<String> ids);

    List<FaqPo> selectByIds(List<String> ids);

    int selectCountByResourceId(String resourceId);


}

/* Ended by AICoder, pid:ufca2u067c9995a14e6e08a910104d34981909eb */