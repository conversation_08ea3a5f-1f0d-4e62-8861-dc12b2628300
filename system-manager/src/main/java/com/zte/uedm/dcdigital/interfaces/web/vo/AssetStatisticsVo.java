package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class AssetStatisticsVo {
    /**
     * 物料总数
     */
    private long materialTotal = 0L;
    /**
     * 已上架物料数
     */
    private long upNum = 0L;
    /**
     * 已上架物料变化数
     */
    private long upChangeNum = 0L;
    /**
     * 未上架物料变化数
     */
    private long downChangeNum = 0L;
    /**
     * 更新物料数
     */
    private long materialUpdateNum = 0L;

    /**
     * 总FAQ数
     */
    private long FaqTotal = 0L;
    /**
     * 更新FAQ数
     */
    private long FaqUpdateNum = 0L;
    /**
     * FAQ变化数
     */
    private long FaqChangeNum = 0L;

    /**
     * 文档总数
     */
    private long DocTotal = 0L;
    /**
     * 更新文档数
     */
    private long DocUpdateNum = 0L;
    /**
     * 文档变化数
     */
    private long DocChangeNum = 0L;

    /**
     * 新增的商机总数
     */
    private long projectAddNum = 0L;
    /**
     * 新增的启动投标商机数
     */
    private long projectStartNum = 0L;

}
