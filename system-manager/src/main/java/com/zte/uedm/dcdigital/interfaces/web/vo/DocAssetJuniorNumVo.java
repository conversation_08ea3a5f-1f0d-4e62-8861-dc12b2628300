package com.zte.uedm.dcdigital.interfaces.web.vo;


import lombok.*;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@ToString
public class DocAssetJuniorNumVo {
    //总数
    private long allNum;
    //更新总数
    private long updateNum;
    //变化数
    private long changeNum;
    //变化趋势标志
    private String changeFlag;

    private List<DocAssetJuniorNum> juniorList; //下级列表数据统计
    /**
     * 分页前总数据量
     */
    private Integer total;
    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DocAssetJuniorNum {
        //总数
        private long allNum;

        //更新数
        private long updateNum;

        //变化数
        private long changeNum;

        //变化趋势标志
        private String changeFlag;

        //产品小类名称
        private String productCategoryName;
    }

}
