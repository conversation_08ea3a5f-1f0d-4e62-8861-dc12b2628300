package com.zte.uedm.dcdigital.infrastructure.repository.impl;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.*;
import com.zte.uedm.dcdigital.domain.repository.MaterialAssetRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.MaterialAssetMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.MaterialAssetUpdMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * 物料资产仓储实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Repository
public class MaterialAssetRepositoryImpl implements MaterialAssetRepository {

    /**
     * 分批插入的批次大小
     */
    private static final int BATCH_SIZE = 500;

    @Autowired
    private MaterialAssetMapper materialAssetMapper;

    @Autowired
    private MaterialAssetUpdMapper materialAssetUpdMapper;

    @Override
    public List<? extends Object> queryMaterialAssetData(List<String> productCategoryIds, String startTime, String endTime, Integer timeType) {
        if (productCategoryIds == null || productCategoryIds.isEmpty()) {
            log.warn("Product category IDs list is empty, returning empty result");
            return Collections.emptyList();
        }

        try {
            switch (timeType) {
                case 1: // 天
                    return materialAssetMapper.queryDayData(productCategoryIds, startTime, endTime);
                case 2: // 周
                    return materialAssetMapper.queryWeekData(productCategoryIds, startTime, endTime);
                case 3: // 月
                    return materialAssetMapper.queryMonthData(productCategoryIds, startTime, endTime);
                case 4: // 年
                    return materialAssetMapper.queryYearData(productCategoryIds, startTime, endTime);
                default:
                    log.error("Invalid time type: {}", timeType);
                    return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("Error querying material asset data, timeType: {}, startTime: {}, endTime: {}", 
                     timeType, startTime, endTime, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<? extends Object> queryMaterialAssetDataByTimePoint(List<String> productCategoryIds, String timePoint, Integer timeType) {
        if (productCategoryIds == null || productCategoryIds.isEmpty()) {
            log.warn("Product category IDs list is empty, returning empty result");
            return Collections.emptyList();
        }

        try {
            switch (timeType) {
                case 1: // 天
                    return materialAssetMapper.queryDayDataByTimePoint(productCategoryIds, timePoint);
                case 2: // 周
                    return materialAssetMapper.queryWeekDataByTimePoint(productCategoryIds, timePoint);
                case 3: // 月
                    return materialAssetMapper.queryMonthDataByTimePoint(productCategoryIds, timePoint);
                case 4: // 年
                    return materialAssetMapper.queryYearDataByTimePoint(productCategoryIds, timePoint);
                default:
                    log.error("Invalid time type: {}", timeType);
                    return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("Error querying material asset data by time point, timeType: {}, timePoint: {}", 
                     timeType, timePoint, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Object queryPreviousMaterialAssetData(String productCategoryId, String timePoint, Integer timeType) {
        try {
            switch (timeType) {
                case 1: // 天
                    return materialAssetMapper.queryPreviousDayData(productCategoryId, timePoint);
                case 2: // 周
                    return materialAssetMapper.queryPreviousWeekData(productCategoryId, timePoint);
                case 3: // 月
                    return materialAssetMapper.queryPreviousMonthData(productCategoryId, timePoint);
                case 4: // 年
                    return materialAssetMapper.queryPreviousYearData(productCategoryId, timePoint);
                default:
                    log.error("Invalid time type: {}", timeType);
                    return null;
            }
        } catch (Exception e) {
            log.error("Error querying previous material asset data, timeType: {}, timePoint: {}", 
                     timeType, timePoint, e);
            return null;
        }
    }

    @Override
    public List<MaterialAssetDayEntity> queryDayData(List<String> productCategoryIds, String startDay, String endDay) {
        return materialAssetMapper.queryDayData(productCategoryIds, startDay, endDay);
    }

    @Override
    public List<MaterialAssetWeekEntity> queryWeekData(List<String> productCategoryIds, String startWeek, String endWeek) {
        return materialAssetMapper.queryWeekData(productCategoryIds, startWeek, endWeek);
    }

    @Override
    public List<MaterialAssetMonthEntity> queryMonthData(List<String> productCategoryIds, String startMonth, String endMonth) {
        return materialAssetMapper.queryMonthData(productCategoryIds, startMonth, endMonth);
    }

    @Override
    public List<MaterialAssetYearEntity> queryYearData(List<String> productCategoryIds, String startYear, String endYear) {
        return materialAssetMapper.queryYearData(productCategoryIds, startYear, endYear);
    }

    @Override
    public List<MaterialAssetDayEntity> aggregateDayDataByDateRange(String startDay, String endDay) {
        try {
            return materialAssetMapper.aggregateDayDataByDateRange(startDay, endDay);
        } catch (Exception e) {
            log.error("Error aggregating day data by date range, startDay: {}, endDay: {}", startDay, endDay, e);
            return Collections.emptyList();
        }
    }

    @Override
    public void insertOrUpdateWeekData(MaterialAssetWeekEntity weekEntity) {
        try {
            // 先查询是否存在
            MaterialAssetWeekEntity existingEntity = queryWeekDataByDayAndCategory(
                weekEntity.getDay(), weekEntity.getProductCategoryId());

            if (existingEntity != null) {
                // 存在则更新，使用现有记录的ID
                weekEntity.setId(existingEntity.getId());
                updateWeekData(weekEntity);
                log.debug("Successfully updated week data for productCategoryId: {}, day: {}",
                         weekEntity.getProductCategoryId(), weekEntity.getDay());
            } else {
                // 不存在则插入
                materialAssetMapper.insertOrUpdateWeekData(weekEntity);
                log.debug("Successfully inserted week data for productCategoryId: {}, day: {}",
                         weekEntity.getProductCategoryId(), weekEntity.getDay());
            }
        } catch (Exception e) {
            log.error("Error inserting or updating week data for productCategoryId: {}",
                     weekEntity.getProductCategoryId(), e);
        }
    }

    @Override
    public void updateWeekData(MaterialAssetWeekEntity weekEntity) {
        try {
            materialAssetMapper.updateWeekData(weekEntity);
        } catch (Exception e) {
            log.error("Error updating week data for productCategoryId: {}",
                     weekEntity.getProductCategoryId(), e);
        }
    }

    @Override
    public void insertOrUpdateMonthData(MaterialAssetMonthEntity monthEntity) {
        try {
            // 先查询是否存在
            MaterialAssetMonthEntity existingEntity = queryMonthDataByDayAndCategory(
                monthEntity.getDay(), monthEntity.getProductCategoryId());

            if (existingEntity != null) {
                // 存在则更新，使用现有记录的ID
                monthEntity.setId(existingEntity.getId());
                updateMonthData(monthEntity);
                log.debug("Successfully updated month data for productCategoryId: {}, day: {}",
                         monthEntity.getProductCategoryId(), monthEntity.getDay());
            } else {
                // 不存在则插入
                materialAssetMapper.insertOrUpdateMonthData(monthEntity);
                log.debug("Successfully inserted month data for productCategoryId: {}, day: {}",
                         monthEntity.getProductCategoryId(), monthEntity.getDay());
            }
        } catch (Exception e) {
            log.error("Error inserting or updating month data for productCategoryId: {}",
                     monthEntity.getProductCategoryId(), e);
        }
    }

    @Override
    public void updateMonthData(MaterialAssetMonthEntity monthEntity) {
        try {
            materialAssetMapper.updateMonthData(monthEntity);
        } catch (Exception e) {
            log.error("Error updating month data for productCategoryId: {}",
                     monthEntity.getProductCategoryId(), e);
        }
    }

    @Override
    public void insertOrUpdateYearData(MaterialAssetYearEntity yearEntity) {
        try {
            // 先查询是否存在
            MaterialAssetYearEntity existingEntity = queryYearDataByDayAndCategory(
                yearEntity.getDay(), yearEntity.getProductCategoryId());

            if (existingEntity != null) {
                // 存在则更新，使用现有记录的ID
                yearEntity.setId(existingEntity.getId());
                updateYearData(yearEntity);
                log.debug("Successfully updated year data for productCategoryId: {}, day: {}",
                         yearEntity.getProductCategoryId(), yearEntity.getDay());
            } else {
                // 不存在则插入
                materialAssetMapper.insertOrUpdateYearData(yearEntity);
                log.debug("Successfully inserted year data for productCategoryId: {}, day: {}",
                         yearEntity.getProductCategoryId(), yearEntity.getDay());
            }
        } catch (Exception e) {
            log.error("Error inserting or updating year data for productCategoryId: {}",
                     yearEntity.getProductCategoryId(), e);
        }
    }

    @Override
    public void updateYearData(MaterialAssetYearEntity yearEntity) {
        try {
            materialAssetMapper.updateYearData(yearEntity);
        } catch (Exception e) {
            log.error("Error updating year data for productCategoryId: {}",
                     yearEntity.getProductCategoryId(), e);
        }
    }

    @Override
    public MaterialAssetWeekEntity queryWeekDataByDayAndCategory(String day, String productCategoryId) {
        try {
            return materialAssetMapper.queryWeekDataByDayAndCategory(day, productCategoryId);
        } catch (Exception e) {
            log.error("Error querying week data by day and category, day: {}, productCategoryId: {}",
                     day, productCategoryId, e);
            return null;
        }
    }

    @Override
    public MaterialAssetMonthEntity queryMonthDataByDayAndCategory(String day, String productCategoryId) {
        try {
            return materialAssetMapper.queryMonthDataByDayAndCategory(day, productCategoryId);
        } catch (Exception e) {
            log.error("Error querying month data by day and category, day: {}, productCategoryId: {}",
                     day, productCategoryId, e);
            return null;
        }
    }

    @Override
    public MaterialAssetYearEntity queryYearDataByDayAndCategory(String day, String productCategoryId) {
        try {
            return materialAssetMapper.queryYearDataByDayAndCategory(day, productCategoryId);
        } catch (Exception e) {
            log.error("Error querying year data by day and category, day: {}, productCategoryId: {}",
                     day, productCategoryId, e);
            return null;
        }
    }

    @Override
    public boolean hasAnyDayData(List<String> productCategoryIds) {
        if (productCategoryIds == null || productCategoryIds.isEmpty()) {
            log.warn("Product category IDs list is empty, returning false");
            return false;
        }

        try {
            int count = materialAssetMapper.countDayData(productCategoryIds);
            return count > 0;
        } catch (Exception e) {
            log.error("Error checking if day data exists for productCategoryIds: {}", productCategoryIds, e);
            return false;
        }
    }

    @Override
    public void batchInsertDayData(List<MaterialAssetDayEntity> dayEntities) {
        if (dayEntities == null || dayEntities.isEmpty()) {
            log.warn("Day entities list is empty, skipping batch insert");
            return;
        }

        try {
            int totalSize = dayEntities.size();
            log.info("Starting batch insert for {} day data records with batch size {}", totalSize, BATCH_SIZE);

            // 分批处理
            for (int i = 0; i < totalSize; i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, totalSize);
                List<MaterialAssetDayEntity> batch = dayEntities.subList(i, endIndex);

                log.debug("Inserting batch {}/{}: records {} to {}",
                         (i / BATCH_SIZE + 1),
                         (totalSize + BATCH_SIZE - 1) / BATCH_SIZE,
                         i + 1,
                         endIndex);

                materialAssetMapper.batchInsertDayData(batch);
            }

            log.info("Successfully batch inserted {} day data records in {} batches",
                    totalSize, (totalSize + BATCH_SIZE - 1) / BATCH_SIZE);
        } catch (Exception e) {
            log.error("Error batch inserting day data, count: {}", dayEntities.size(), e);
            throw new RuntimeException("Failed to batch insert day data", e);
        }
    }

    @Override
    public void insertOrUpdateDayData(MaterialAssetDayEntity dayEntity) {
        if (dayEntity == null) {
            log.warn("Day entity is null, skipping insert or update");
            return;
        }

        try {
            // 先查询是否存在
            MaterialAssetDayEntity existingEntity = queryDayDataByDayAndCategory(
                dayEntity.getDay(), dayEntity.getProductCategoryId());

            if (existingEntity != null) {
                // 存在则更新，使用现有记录的ID
                dayEntity.setId(existingEntity.getId());
                updateDayData(dayEntity);
                log.debug("Successfully updated day data for productCategoryId: {}, day: {}",
                         dayEntity.getProductCategoryId(), dayEntity.getDay());
            } else {
                // 不存在则插入
                insertDayData(dayEntity);
                log.debug("Successfully inserted day data for productCategoryId: {}, day: {}",
                         dayEntity.getProductCategoryId(), dayEntity.getDay());
            }
        } catch (Exception e) {
            log.error("Error inserting or updating day data for productCategoryId: {}, day: {}",
                     dayEntity.getProductCategoryId(), dayEntity.getDay(), e);
            throw new RuntimeException("插入或更新日表数据失败", e);
        }
    }

    @Override
    public MaterialAssetDayEntity queryDayDataByDayAndCategory(String day, String productCategoryId) {
        if (StringUtils.isBlank(day) || StringUtils.isBlank(productCategoryId)) {
            log.warn("Day or productCategoryId is blank, returning null");
            return null;
        }

        try {
            return materialAssetMapper.queryDayDataByDayAndCategory(day, productCategoryId);
        } catch (Exception e) {
            log.error("Error querying day data by day and category, day: {}, productCategoryId: {}",
                     day, productCategoryId, e);
            return null;
        }
    }

    @Override
    public void updateDayData(MaterialAssetDayEntity dayEntity) {
        if (dayEntity == null || StringUtils.isBlank(dayEntity.getId())) {
            log.warn("Day entity is null or ID is blank, skipping update");
            return;
        }

        try {
            materialAssetMapper.updateDayData(dayEntity);
            log.debug("Successfully updated day data with ID: {}", dayEntity.getId());
        } catch (Exception e) {
            log.error("Error updating day data with ID: {}", dayEntity.getId(), e);
            throw new RuntimeException("更新日表数据失败", e);
        }
    }

    @Override
    public void insertDayData(MaterialAssetDayEntity dayEntity) {
        if (dayEntity == null) {
            log.warn("Day entity is null, skipping insert");
            return;
        }

        try {
            // 确保有ID
            if (StringUtils.isBlank(dayEntity.getId())) {
                dayEntity.setId(UUID.randomUUID().toString());
            }

            materialAssetMapper.insertDayData(dayEntity);
            log.debug("Successfully inserted day data with ID: {}", dayEntity.getId());
        } catch (Exception e) {
            log.error("Error inserting day data for productCategoryId: {}, day: {}",
                     dayEntity.getProductCategoryId(), dayEntity.getDay(), e);
            throw new RuntimeException("插入日表数据失败", e);
        }
    }

    @Override
    public void insertOrUpdateMaterialEditRecord(MaterialAssetUpdEntity updEntity) {
        if (updEntity == null) {
            log.warn("Material edit record entity is null, skipping insert/update");
            return;
        }

        if (StringUtils.isBlank(updEntity.getMaterialId()) || StringUtils.isBlank(updEntity.getDay())) {
            log.warn("Material ID or day is blank, skipping insert/update");
            return;
        }

        try {
            // 先查询是否已存在记录
            MaterialAssetUpdEntity existingRecord = materialAssetUpdMapper.queryRecordByMaterialIdAndDay(
                    updEntity.getMaterialId(), updEntity.getDay());

            if (existingRecord != null) {
                // 存在记录，更新时间
                existingRecord.setCreateTime(updEntity.getCreateTime());
                existingRecord.setProductCategoryId(updEntity.getProductCategoryId()); // 更新产品小类ID（可能会变）
                materialAssetUpdMapper.updateRecord(existingRecord);
                log.debug("Successfully updated existing material edit record with ID: {}", existingRecord.getId());
            } else {
                // 不存在记录，插入新记录
                if (StringUtils.isBlank(updEntity.getId())) {
                    updEntity.setId(UUID.randomUUID().toString());
                }
                materialAssetUpdMapper.insertRecord(updEntity);
                log.debug("Successfully inserted new material edit record with ID: {}", updEntity.getId());
            }

        } catch (Exception e) {
            log.error("Error inserting/updating material edit record for materialId: {}, day: {}",
                     updEntity.getMaterialId(), updEntity.getDay(), e);
            throw new RuntimeException("插入或更新物料编辑记录失败", e);
        }
    }

    @Override
    public MaterialAssetUpdEntity queryMaterialEditRecordByMaterialIdAndDay(String materialId, String day) {
        if (StringUtils.isBlank(materialId) || StringUtils.isBlank(day)) {
            log.warn("Material ID or day is blank, returning null");
            return null;
        }

        try {
            return materialAssetUpdMapper.queryRecordByMaterialIdAndDay(materialId, day);
        } catch (Exception e) {
            log.error("Error querying material edit record by materialId: {}, day: {}", materialId, day, e);
            return null;
        }
    }

    @Override
    public Long countUpdatedMaterialsByDayAndCategory(String day, String productCategoryId) {
        if (StringUtils.isBlank(day) || StringUtils.isBlank(productCategoryId)) {
            log.warn("Day or productCategoryId is blank, returning 0");
            return 0L;
        }

        try {
            Long count = materialAssetUpdMapper.countUpdatedMaterialsByDayAndCategory(day, productCategoryId);
            return count != null ? count : 0L;
        } catch (Exception e) {
            log.error("Error counting updated materials by day and category, day: {}, productCategoryId: {}",
                     day, productCategoryId, e);
            return 0L;
        }
    }

    @Override
    public Map<String, Long> batchCountUpdatedMaterials(String day, List<String> productCategoryIds) {
        if (StringUtils.isBlank(day) || productCategoryIds == null || productCategoryIds.isEmpty()) {
            log.warn("Day is blank or productCategoryIds is empty, returning empty map");
            return new HashMap<>();
        }

        try {
            List<Map<String, Object>> results = materialAssetUpdMapper.batchCountUpdatedMaterials(day, productCategoryIds);
            Map<String, Long> countMap = new HashMap<>();

            for (Map<String, Object> result : results) {
                String categoryId = (String) result.get("productCategoryId");
                Object countObj = result.get("updateCount");
                Long count = 0L;

                if (countObj instanceof Number) {
                    count = ((Number) countObj).longValue();
                }

                countMap.put(categoryId, count);
            }

            return countMap;
        } catch (Exception e) {
            log.error("Error batch counting updated materials for day: {}", day, e);
            return new HashMap<>();
        }
    }
}
