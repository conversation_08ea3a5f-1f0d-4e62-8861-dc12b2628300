package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
/* Started by AICoder, pid:hec65d1cbfy6693148f00be950bf5d71ed08bc6e */
public class EvalDataQueryDto {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 功能点ID
     */
    private String functionId;

    /**
     * 用户唯一标识（工号或系统用户ID）
     */
    private String userId;

    /**
     * 用户显示名称
     */
    private String userName;

    /**
     * 操作类型
     */
    private Integer operationType;

    /**
     * 评价开始时间（ISO-8601格式：yyyy-MM-dd）
     */
    private String beginTime;

    /**
     * 评价结束时间（ISO-8601格式：yyyy-MM-dd）
     */
    private String endTime;

    /**
     * 分页大小（默认值：10）
     */
    private Integer pageSize = 10;

    /**
     * 当前页码（默认值：1）
     */
    private Integer pageNum = 1;
}
/* Ended by AICoder, pid:hec65d1cbfy6693148f00be950bf5d71ed08bc6e */

