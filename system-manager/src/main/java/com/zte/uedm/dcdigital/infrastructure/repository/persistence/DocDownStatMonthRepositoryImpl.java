package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocDownStatEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocDownStatMonthEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.LoginStatMonthEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DocDownStatMonthRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.DocDownStatConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DocDownStatMonthMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DocDownStatMonthPo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class DocDownStatMonthRepositoryImpl implements DocDownStatMonthRepository {
    @Autowired
    private DocDownStatMonthMapper docDownStatMonthMapper;
    @Override
    public void addDocDownStatMonthList(List<DocDownStatMonthEntity> monthEntityList) {
        if (CollectionUtils.isEmpty(monthEntityList)) {
            return;
        }
        List<DocDownStatMonthPo> poList = DocDownStatConverter.INSTANCE.monthEntityListToPoList(monthEntityList);
        docDownStatMonthMapper.batchInsertDocDownStatMonth(poList);
    }

    @Override
    public void updateDocDownStatMonthList(List<DocDownStatMonthEntity> monthEntityList) {
        if (CollectionUtils.isEmpty(monthEntityList)) {
            return;
        }
        List<DocDownStatMonthPo> poList = DocDownStatConverter.INSTANCE.monthEntityListToPoList(monthEntityList);
        docDownStatMonthMapper.batchUpdateDocDownStatMonth(poList);
    }

    @Override
    public List<DocDownStatMonthEntity> getDownAndViewNum(DocDownStatMonthEntity monthEntity, List<String> deptUserIds) {
        DocDownStatMonthPo po = DocDownStatConverter.INSTANCE.monthEntityToPo(monthEntity);
        List<DocDownStatMonthPo> poList = docDownStatMonthMapper.getDownAndViewNum(po, deptUserIds);
        return DocDownStatConverter.INSTANCE.monthPoListToEntityList(poList);
    }

    @Override
    public List<DocDownStatMonthEntity> getPreviewTop50Data(DocDownStatMonthEntity monthEntity, List<String> deptUserIds) {
        DocDownStatMonthPo po = DocDownStatConverter.INSTANCE.monthEntityToPo(monthEntity);
        List<DocDownStatMonthPo> poList = docDownStatMonthMapper.getPreviewTop50Data(po, deptUserIds);
        return DocDownStatConverter.INSTANCE.monthPoListToEntityList(poList);
    }

    @Override
    public List<DocDownStatMonthEntity> getDownloadTop50Data(DocDownStatMonthEntity monthEntity, List<String> deptUserIds) {
        DocDownStatMonthPo po = DocDownStatConverter.INSTANCE.monthEntityToPo(monthEntity);
        List<DocDownStatMonthPo> poList = docDownStatMonthMapper.getDownloadTop50Data(po, deptUserIds);
        return DocDownStatConverter.INSTANCE.monthPoListToEntityList(poList);
    }

    @Override
    public List<DocDownStatMonthEntity> getPreviewDetailsData(DocDownStatMonthEntity monthEntity, List<String> deptUserIds) {
        DocDownStatMonthPo po = DocDownStatConverter.INSTANCE.monthEntityToPo(monthEntity);
        List<DocDownStatMonthPo> poList = docDownStatMonthMapper.getPreviewDetailsData(po, deptUserIds);
        return DocDownStatConverter.INSTANCE.monthPoListToEntityList(poList);
    }

    @Override
    public List<DocDownStatMonthEntity> getDownloadDetailsData(DocDownStatMonthEntity monthEntity, List<String> deptUserIds) {
        DocDownStatMonthPo po = DocDownStatConverter.INSTANCE.monthEntityToPo(monthEntity);
        List<DocDownStatMonthPo> poList = docDownStatMonthMapper.getDownloadDetailsData(po, deptUserIds);
        return DocDownStatConverter.INSTANCE.monthPoListToEntityList(poList);
    }

    @Override
    public List<DocDownStatEntity> getUserStatisticFileDate(DocDownStatEntity entity, List<String> deptUserIds) {
        return docDownStatMonthMapper.getUserStatisticFileDate(entity, deptUserIds);
    }
}
