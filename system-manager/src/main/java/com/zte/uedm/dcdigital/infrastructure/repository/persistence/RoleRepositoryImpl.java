/* Started by AICoder, pid:b0e18o44e4l50c414e340931b0d4b332a5a2fce4 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.RoleEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.RoleRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.RolePoConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.RoleMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.RolePo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Repository
public class RoleRepositoryImpl implements RoleRepository {

    @Autowired
    private RoleMapper roleMapper;

    @Override
    public RoleEntity findByRoleId(String roleId) {
        RolePo rolePo = roleMapper.selectById(roleId);
        return RolePoConverter.INSTANCE.convertPoToEntity(rolePo);
    }

    @Override
    public RoleEntity findByRoleName(String roleName) {
        RolePo rolePo = roleMapper.selectByName(roleName);
        return RolePoConverter.INSTANCE.convertPoToEntity(rolePo);
    }

    @Override
    public RoleEntity findByRoleCode(String code) {
        RolePo rolePo = roleMapper.selectByCode(code);
        return RolePoConverter.INSTANCE.convertPoToEntity(rolePo);
    }

    @Override
    public List<RoleEntity> findByRoleCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        List<RolePo> rolePos = roleMapper.selectByCodes(codes);
        return RolePoConverter.INSTANCE.convertPosToEntities(rolePos);
    }

    @Override
    public List<RoleEntity> findByRoleIds(Set<String> roleIds) {
        List<RolePo> rolePos = roleMapper.selectBatchIds(roleIds);
        return RolePoConverter.INSTANCE.convertPosToEntities(rolePos);
    }
}

/* Ended by AICoder, pid:b0e18o44e4l50c414e340931b0d4b332a5a2fce4 */