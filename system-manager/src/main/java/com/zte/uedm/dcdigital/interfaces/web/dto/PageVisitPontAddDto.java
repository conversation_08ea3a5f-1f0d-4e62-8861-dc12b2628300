/* Started by AICoder, pid:gb65a58547t5682140a60add80ca4e3b3b718575 */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 页面访问新增DTO类
 */
@Getter
@Setter
@ToString
public class PageVisitPontAddDto {
    /**
     * 页面访问资源类型
     * 1、页面访问-品牌，2、页面访问-物料，3、页面访问-文档，4、页面访问-FAQ，
     * 5、文档下载-预览，6、文档下载-下载，
     * 7、搜索-物料，8、搜索-文档，9、搜索-FAQ
     */
    private String type;

    /**
     * 资源id(品牌id、物料id、文档id、FAQid)
     */
    private String resourceId;

    /**
     * 产品小类id
     */
    private String productCategoryId;
}

/* Ended by AICoder, pid:gb65a58547t5682140a60add80ca4e3b3b718575 */