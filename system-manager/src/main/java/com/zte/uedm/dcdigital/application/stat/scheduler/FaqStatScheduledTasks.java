package com.zte.uedm.dcdigital.application.stat.scheduler;

import com.zte.uedm.dcdigital.application.stat.executor.FaqStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class FaqStatScheduledTasks {

    @Autowired
    private FaqStatService faqStatService;

    // 开发测试时使用 10分钟执行一次
    @Scheduled(cron = "0 0/5 * * * ?")
    //线上暂定每天凌晨1点执行
    //@Scheduled(cron = "0 0 1 * * ?")
    public void statFaqData(){
        log.info("start stat faq data !!!");
        faqStatService.timedStat();
        log.info("end stat faq data !!!");
    }
}
