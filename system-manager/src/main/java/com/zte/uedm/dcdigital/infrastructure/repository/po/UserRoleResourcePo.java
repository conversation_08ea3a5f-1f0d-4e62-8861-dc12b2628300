/* Started by AICoder, pid:t282ffc01ed8bec1428a089430ddbd53774177dd */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 用户-角色-资源关联实体类
 */
@Getter
@Setter
@ToString
@TableName("auth_user_role_resource")
public class UserRoleResourcePo {

    /**
     * 用户-角色-资源关联ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 最后更新时间
     */
    private String updateTime;

    /**
     * 创建该记录的用户标识符
     */
    private String createBy;

    /**
     * 最后更新该记录的用户标识符
     */
    private String updateBy;
}

/* Ended by AICoder, pid:t282ffc01ed8bec1428a089430ddbd53774177dd */