/* Started by AICoder, pid:h5153o15dfvad1314d860bb970f3ea3332e87f58 */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class EvalDataHistoryDto {
    // 主键ID
    private String id;

    // 功能点ID
    private String functionId;

    // 用户ID/工号
    private String userId;

    // 用户名称
    private String userName;

    /**
     * 操作类型
     * 0: 赞
     * 1: 踩
     */
    private Integer operationType;

    // 评价内容
    private String evalContent;

    // 创建人
    private String createBy;

    // 创建时间
    private String createTime;
}

/* Ended by AICoder, pid:h5153o15dfvad1314d860bb970f3ea3332e87f58 */