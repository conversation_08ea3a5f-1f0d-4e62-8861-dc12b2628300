package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqDayPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqWeekPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AssetFaqWeekMapper {

    /**
     * 根据产品小类ID和时间范围查询统计记录
     */
    List<AssetFaqWeekPo> selectByCategoryAndTimeRange(@Param("productCategoryId") String productCategoryId,
                                                      @Param("startTime") String startTime,
                                                      @Param("endTime") String endTime);

    /**
     * 根据时间范围查询统计记录
     */
    List<AssetFaqWeekPo> selectByTimeRange(@Param("startTime") String startTime,
                                           @Param("endTime") String endTime,
                                           @Param("categoryIds") List<String> categoryIds);

    /**
     * 根据时间查询统计记录
     */
    List<AssetFaqWeekPo> selectByTime(@Param("weekStr") String weekStr);

    /**
     * 批量更新周表
     */
    void update(AssetFaqWeekPo weekPo);

    /**
     * 批量插入周表数据
     */
    void batchInsert(@Param("list") List<AssetFaqWeekPo> toInsert);

    List<AssetFaqWeekPo> selectDateByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
