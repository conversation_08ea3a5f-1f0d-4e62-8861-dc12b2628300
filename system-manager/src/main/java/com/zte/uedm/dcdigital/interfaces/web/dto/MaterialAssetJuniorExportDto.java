package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 物料资产下级统计导出DTO
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class MaterialAssetJuniorExportDto {

    /**
     * 节点ID（产品线/大类/小类ID）
     */
    @NotBlank(message = "节点ID不能为空")
    private String nodeId;

    /**
     * 层级：1-产品线，2-产品大类，3-产品小类
     */
    @NotNull(message = "层级不能为空")
    private Integer level;

    /**
     * 单个时间节点
     */
    @NotBlank(message = "时间节点不能为空")
    private String timePoint;

    /**
     * 时间类型：1-天，2-周，3-月，4-年
     */
    @NotNull(message = "时间类型不能为空")
    private Integer timeType;
}
