/* Started by AICoder, pid:9d373k78d6w4c661462c080500a2d7426107e615 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.EvalDataEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.EvalRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.EvalConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.EvalDataMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.EvalDataPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.EvalDataDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class EvalRepositoryImpl implements EvalRepository {
    @Autowired
    private EvalDataMapper evalDataMapper;

    @Override
    public List<EvalDataEntity> currentEvalData(EvalDataDto evalDataDto) {
        EvalDataPo evalDataPo = EvalConverter.INSTANCE.evalDataDtoToEvalDataPo(evalDataDto);
        List<EvalDataPo> evalDataPos = evalDataMapper.selectEvalDataList(evalDataPo);
        return EvalConverter.INSTANCE.evalDataPoListToEvalDataEntityList(evalDataPos);
    }

    @Override
    public List<EvalDataEntity> selectEvalDataByFunctionId(String functionId) {
        List<EvalDataPo> evalDataPos = evalDataMapper.selectEvalDataByFunctionId(functionId);
        return EvalConverter.INSTANCE.evalDataPoListToEvalDataEntityList(evalDataPos);
    }

    @Override
    public void addEvalData(EvalDataEntity evalDataEntity) {
        EvalDataPo evalDataPo = EvalConverter.INSTANCE.evalDataEntityToEvalDataPo(evalDataEntity);
        evalDataMapper.insertEvalData(evalDataPo);
    }

    @Override
    public void deleteEvalDataById(String id) {
        if (StringUtils.isBlank(id)) {
            return;
        }
        String[] ids = id.split(",");
        evalDataMapper.deleteEvalDataByIds(ids);
    }
}

/* Ended by AICoder, pid:9d373k78d6w4c661462c080500a2d7426107e615 */