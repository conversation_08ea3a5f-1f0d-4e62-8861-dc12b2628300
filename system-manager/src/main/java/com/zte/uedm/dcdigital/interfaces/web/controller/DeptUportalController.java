/* Started by AICoder, pid:t8169ya7f8j10d814b150b935045c43047a11f77 */
package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.dept.executor.DeptCommandService;
import com.zte.uedm.dcdigital.application.dept.executor.DeptQueryService;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.dto.DeptQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.AuthDeptVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;
@Slf4j
@Path("/uportal/dept")
@Api(value = "组织管理", tags = {"组织管理接口"})
@Controller
public class DeptUportalController {

    @Autowired
    private DeptCommandService deptCommandService;

    @Autowired
    private DeptQueryService deptQueryService;

    @POST
    @Path("/list-dept-tree")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询组织树形列表", notes = "查询组织树形列表", httpMethod = "POST")
    public BaseResult<List<AuthDeptVo>> queryList(DeptQueryDto queryDto) {
        List<AuthDeptVo> authDeptVoList = deptQueryService.getDeptTree(queryDto);
        return BaseResult.success(authDeptVoList);
    }
}