/* Started by AICoder, pid:9c7d2d1e69z125c14a310983505593439a686e70 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.AuthDeptUserRelationEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DeptUserRelationRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.DeptUserRelationConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DeptUserRelationMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.AuthDeptUserRelationPo;
import com.zte.uedm.dcdigital.interfaces.web.vo.DeptUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class DeptUserRelationRepositoryImpl implements DeptUserRelationRepository {

    @Autowired
    private DeptUserRelationMapper deptUserRelationMapper;

    @Override
    public int addDeptUsers(List<AuthDeptUserRelationEntity> deptUserRelationEntities) {
        List<AuthDeptUserRelationPo> authDeptUserRelationPos = DeptUserRelationConverter.INSTANCE.entityListToPoList(deptUserRelationEntities);
        return deptUserRelationMapper.batchInsertRelations(authDeptUserRelationPos);
    }

    @Override
    public List<AuthDeptUserRelationEntity> findDeptUserByDeptId(String deptId) {
        LambdaQueryWrapper<AuthDeptUserRelationPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AuthDeptUserRelationPo::getDeptId, deptId);
        List<AuthDeptUserRelationPo> authDeptUserRelationPos = deptUserRelationMapper.selectList(queryWrapper);
        return DeptUserRelationConverter.INSTANCE.poListToEntityList(authDeptUserRelationPos);
    }

    @Override
    public int deleteDeptUserByIds(List<String> ids) {
        return deptUserRelationMapper.deleteBatchIds(ids);
    }

    @Override
    public int deleteDeptUserByDeptId(String deptId) {
        QueryWrapper<AuthDeptUserRelationPo> wrapper = new QueryWrapper<>();
        wrapper.eq("dept_id", deptId);
        return deptUserRelationMapper.delete(wrapper);
    }

    @Override
    public List<AuthDeptUserRelationEntity> findDeptByUserId(String userId) {
        LambdaQueryWrapper<AuthDeptUserRelationPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AuthDeptUserRelationPo::getUserId,userId);
        List<AuthDeptUserRelationPo> authDeptUserRelationPos = deptUserRelationMapper.selectList(queryWrapper);
        return DeptUserRelationConverter.INSTANCE.poListToEntityList(authDeptUserRelationPos);
    }

    @Override
    public List<AuthDeptUserRelationEntity> findAllDeptUser() {
        List<AuthDeptUserRelationPo> authDeptUserRelationPos = deptUserRelationMapper.selectList(null);
        return  DeptUserRelationConverter.INSTANCE.poListToEntityList(authDeptUserRelationPos);
    }

    @Override
    public List<AuthDeptUserRelationEntity> findAllDeptUserByDeptId(String deptId) {
        LambdaQueryWrapper<AuthDeptUserRelationPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AuthDeptUserRelationPo::getDeptId, deptId);
        List<AuthDeptUserRelationPo> authDeptUserRelationPos = deptUserRelationMapper.selectList(queryWrapper);
        return  DeptUserRelationConverter.INSTANCE.poListToEntityList(authDeptUserRelationPos);
    }

    @Override
    public List<AuthDeptUserRelationEntity> querySubDeptUserList(List<String> deptIds) {
        LambdaQueryWrapper<AuthDeptUserRelationPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AuthDeptUserRelationPo::getDeptId, deptIds);
        List<AuthDeptUserRelationPo> authDeptUserRelationPos = deptUserRelationMapper.selectList(queryWrapper);
        return  DeptUserRelationConverter.INSTANCE.poListToEntityList(authDeptUserRelationPos);
    }
}

/* Ended by AICoder, pid:9c7d2d1e69z125c14a310983505593439a686e70 */