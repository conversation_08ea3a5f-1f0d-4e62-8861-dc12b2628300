/* Started by AICoder, pid:5290bf7775s9fea141b409f2202445263287a126 */
package com.zte.uedm.dcdigital.application.system.executor;

import com.zte.uedm.dcdigital.common.bean.system.ResourceVo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ResourceQueryVo;
import java.util.List;

/**
 * 资源查询服务接口，定义了通过用户ID和类型查询资源的方法。
 */
public interface ResourceQueryService {

    /**
     * 通过用户ID和类型查询资源。
     *
     * @param userId 用户ID，不能为空。
     * @param type   资源类型，必须为有效类型。
     * @return 包含资源ID的列表，可能为空。
     */
    List<String> queryResourceByUserId(String userId, Integer type);

    /**
     * 查询所有指定类型的资源。
     *
     * @param type 资源类型，必须为有效类型。
     * @return 包含所有资源的列表，可能为空。
     */
    List<ResourceQueryVo> queryAllResource(Integer type);

    List<ResourceVo> getResourceByIdAndRoleCode(List<String> ids, String roleCode);

    /* Started by AICoder, pid:p4c9e2f638d9ea914bd30ad91041750e6e57cb08 */
    /**
     * 根据用户ID获取该用户关联的所有地区ID。
     *
     * @param userId 用户的唯一标识符。
     * @param roleCode 角色代码，用于确定用户所属的角色。
     * @return 一个包含所有关联地区ID的列表。
     */
    List<String> getAreaIdsByUserId(String userId,String roleCode);

    ResourceVo queryByResourceAndRoleCode(String resourceId, List<String> roleCodes);
    /* Ended by AICoder, pid:p4c9e2f638d9ea914bd30ad91041750e6e57cb08 */
}

/* Ended by AICoder, pid:5290bf7775s9fea141b409f2202445263287a126 */