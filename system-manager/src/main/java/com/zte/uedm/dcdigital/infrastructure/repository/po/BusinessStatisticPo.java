/* Started by AICoder, pid:0c5c6pe2ceie66a1417b0a8d403a7358fb38229c */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BusinessStatisticPo {
    /**
     * 主键ID。
     */
    private String id;

    /**
     * 产品类别ID。
     */
    private String productCategoryId;

    /**
     * 统计日期（格式：YYYY-MM-DD）。
     */
    private String day;

    /**
     * 总任务数。
     */
    private Long allTaskNum;

    /**
     * 正常任务数。
     */
    private Long normalTaskNum;

    /**
     * 扩展任务数。
     */
    private Long extensionTaskNum;

    /**
     * 总标书数量。
     */
    private Long allLectotypeNum;

    /**
     * 普通标书数量。
     */
    private Long bidLectotypeNum;

    /**
     * 指定标书数量。
     */
    private Long appointLectotypeNum;

    /**
     * 创建时间。
     */
    private String createTime;
}
/* Ended by AICoder, pid:0c5c6pe2ceie66a1417b0a8d403a7358fb38229c */