/* Started by AICoder, pid:q56fb7fce098e7214c000a9f008bc93e1a3935b2 */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class EvalDataVo {
    // 主键ID
    private String id;

    // 功能点ID
    private String functionId;

    // 用户ID/工号
    private String userId;

    // 用户名称
    private String userName;

    /**
     * 当前操作类型
     * 0: 赞
     * 1: 踩
     * 2: 无操作
     */
    private Integer operationType;

    // 评价内容
    private String evalContent;

    // 当前功能赞的数量
    private Integer likeNumber;

    // 当前功能踩的数量
    private Integer stepNumber;
}

/* Ended by AICoder, pid:q56fb7fce098e7214c000a9f008bc93e1a3935b2 */