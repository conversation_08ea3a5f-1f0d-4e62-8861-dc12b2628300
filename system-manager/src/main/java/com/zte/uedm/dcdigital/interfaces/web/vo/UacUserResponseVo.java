/* Started by AICoder, pid:d885dv5a8bd804014048095660d1b02dd1d986ed */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 批量搜索UAC用户回显对象
 */
@Getter
@Setter
@ToString
public class UacUserResponseVo {
    // 工号
    private String accountId;
    private String unionId;
    private String source;
    // 用户类型
    private String userType;
    private String email;
    private String phone;
    // 登录账号（这个字段uac测试环境返回的是账号，不要误认为用户名了）
    private String userName;
    private String accountStatus;
    // 用户中文名
    private String personName;
    // 用户英文名
    private String personNameEn;
}

/* Ended by AICoder, pid:d885dv5a8bd804014048095660d1b02dd1d986ed */