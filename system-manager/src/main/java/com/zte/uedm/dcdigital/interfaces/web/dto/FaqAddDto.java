/* Started by AICoder, pid:j02a9i5549xe3c614e500802d0b0d36e4e41ec78 */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.security.annotation.DcResourceField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@ToString
public class FaqAddDto {

    /**
     * 问题类型：1-产品小类，2-项目
     */
    @NotNull
    @LogMark(range = {OperationMethodEnum.ADD})
    private Integer type;

    /**
     * 资源ID
     */
    @NotNull
    @LogMark(range = OperationMethodEnum.ADD)
    @DcResourceField
    private String resourceId;

    /**
     * 问题
     */
    @LogMark(range = OperationMethodEnum.ADD)
    @NotNull
    private String question;

    /**
     * 答案
     */
    @NotNull
    @LogMark(range = OperationMethodEnum.ADD)
    private String answer;

    /**
     * 文档ID列表
     */
    private List<String> documentIds;
}

/* Ended by AICoder, pid:j02a9i5549xe3c614e500802d0b0d36e4e41ec78 */