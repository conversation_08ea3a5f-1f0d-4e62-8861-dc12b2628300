package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("asset_faq_week")
public class AssetFaqWeekPo {

    /**
     * 主表id
     */
    private String id;

    /**
     * 日期（例：202506）
     */
    private String day;

    /**
     * 小类ID
     */
    private String productCategoryId;

    /**
     * 总FAQ数（所有的FAQ）
     */
    private String allNum;

    /**
     * FAQ变化数（相比头一天的数量变化）
     */
    private String changeNum;

    /**
     * 更新FAQ数（当天编辑的FAQ）
     */
    private String updateNum;

    /**
     * 时间
     */
    private String createTime;
}
