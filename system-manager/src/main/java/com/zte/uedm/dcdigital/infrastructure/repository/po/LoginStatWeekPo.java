/* Started by AICoder, pid:ndd3a309445309714ae60977205822440376c3b7 */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 登录周统计数据持久化对象
 */
@Getter
@Setter
@ToString
@TableName("login_stat_week")
public class LoginStatWeekPo {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 统计周（格式：YYYYWW，如202501表示2025年第1周）
     */
    @TableField("day")
    private Integer day;

    /**
     * 部门/组织ID
     */
    @TableField("dept_id")
    private String deptId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 本周登录天数
     */
    @TableField("num")
    private Integer num;


    // 查询条件字段（非数据库字段）
    @TableField(exist = false)
    private Integer beginTime;

    @TableField(exist = false)
    private Integer endTime;
}

/* Ended by AICoder, pid:ndd3a309445309714ae60977205822440376c3b7 */