/* Started by AICoder, pid:r4ffbv9206v821514f6a0bd530395463a1147d49 */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.*;
import com.zte.uedm.dcdigital.infrastructure.repository.po.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        imports = {
                DocDownStatDayPo.class,
                DocDownStatDayEntity.class,
                DocDownStatMonthPo.class,
                DocDownStatMonthEntity.class,
                DocDownStatWeekPo.class,
                DocDownStatWeekEntity.class,
                DocDownStatYearPo.class,
                DocDownStatYearEntity.class
        }
)
public interface DocDownStatConverter {
    // 使用MapStruct的实例获取方式
    DocDownStatConverter INSTANCE = Mappers.getMapper(DocDownStatConverter.class);

    // DayPo转DayEntity
    DocDownStatDayEntity dayPoToDocDownStatDayEntity(DocDownStatDayPo docDownStatDayPo);

    // DayPoList转DayEntityList
    List<DocDownStatDayEntity> dayPoListToDocDownStatDayEntityList(List<DocDownStatDayPo> docDownStatDayPoList);

    // DayEntity转DayPo
    DocDownStatDayPo dayEntityToDocDownStatDayPo(DocDownStatDayEntity docDownStatDayEntity);

    // DayEntityList转DayPoList
    List<DocDownStatDayPo> dayEntityListToDocDownStatDayPoList(List<DocDownStatDayEntity> docDownStatDayEntityList);

    // MonthEntity转DocDownStatMonthPo
    DocDownStatMonthPo monthEntityToPo(DocDownStatMonthEntity entity);

    // MonthEntityList转poList
    List<DocDownStatMonthPo> monthEntityListToPoList(List<DocDownStatMonthEntity> entityList);

    // MonthPoList转entityList
    List<DocDownStatMonthEntity> monthPoListToEntityList(List<DocDownStatMonthPo> poList);

    // WeekEntityList转WeekPoList
    List<DocDownStatWeekPo> weekEntityListToPoList(List<DocDownStatWeekEntity> entityList);

    // WeekEntity转WeekPo
    DocDownStatWeekPo weekEntityToPo(DocDownStatWeekEntity entity);

    // WeekEntity转WeekPo
    DocDownStatWeekEntity weekPoTOEntity(DocDownStatWeekPo weekPo);

    // WeekPoList转WeekEntityList
    List<DocDownStatWeekEntity> weekPoListToEntityList(List<DocDownStatWeekPo> poList);


    // YearEntityList转YearPoList
    List<DocDownStatYearPo> yearEntityListToPoList(List<DocDownStatYearEntity> entityList);

    // YearEntity转YearPo
    DocDownStatYearPo yearEntityToPo(DocDownStatYearEntity entity);

    // YearPoList转YearEntityList
    List<DocDownStatYearEntity> yearPoListToEntityList(List<DocDownStatYearPo> poList);
}

/* Ended by AICoder, pid:r4ffbv9206v821514f6a0bd530395463a1147d49 */