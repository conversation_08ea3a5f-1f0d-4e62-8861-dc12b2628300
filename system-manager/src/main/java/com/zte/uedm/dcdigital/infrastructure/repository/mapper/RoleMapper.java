/* Started by AICoder, pid:5e4b1gffd3nb0dd14df90b53f036a72515e37db3 */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.RolePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * RoleMapper接口，用于角色数据的持久化操作。
 */
@Mapper
public interface RoleMapper extends BaseMapper<RolePo> {

    /**
     * 根据角色名称查询角色信息。
     *
     * @param roleName 角色名称
     * @return 匹配的角色信息，如果没有找到则返回null
     */
    RolePo selectByName(@Param("roleName") String roleName);

    RolePo selectByCode(@Param("code") String code);

    List<RolePo> selectByCodes(@Param("codes") List<String> codes);
}

/* Ended by AICoder, pid:5e4b1gffd3nb0dd14df90b53f036a72515e37db3 */