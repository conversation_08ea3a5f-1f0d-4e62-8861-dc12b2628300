/* Started by AICoder, pid:f67511129a6017214ee20bc360e514668bb4b8bd */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.dcdigital.common.bean.document.DocumentCitedVo;
import com.zte.uedm.dcdigital.domain.aggregate.model.faq.FaqObj;
import com.zte.uedm.dcdigital.domain.aggregate.repository.FaqRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.FaqConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.FaqMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.FaqPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class FaqRepositoryImpl implements FaqRepository {

    @Autowired
    private FaqMapper faqMapper;

    @Override
    public FaqObj selectById(String id) {
        FaqPo faqPo = faqMapper.selectById(id);
        return FaqConverter.INSTANCE.convertPo2Obj(faqPo);
    }

    @Override
    public List<FaqObj> selectByIds(List<String> ids) {
        List<FaqPo> faqPos = faqMapper.selectByIds(ids);
        return FaqConverter.INSTANCE.convertPos2Objs(faqPos);
    }

    @Override
    public PageInfo<FaqObj> queryByCondition(String searchText, Integer type, String resourceId, String sort, String order, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<FaqPo> faqPos = faqMapper.queryByCondition(searchText, type, resourceId, sort, order);
        PageInfo<FaqPo> pageInfo = new PageInfo<>(faqPos);
        long total = pageInfo.getTotal();
        List<FaqObj> faqObjs = FaqConverter.INSTANCE.convertPos2Objs(faqPos);
        PageInfo<FaqObj> faqObjPageInfo = new PageInfo<>(faqObjs);
        faqObjPageInfo.setTotal(total);
        return faqObjPageInfo;
    }

    @Override
    public void insert(FaqObj faqObj) {
        FaqPo faqPo = FaqConverter.INSTANCE.convertObj2Po(faqObj);
        faqMapper.insert(faqPo);
    }

    @Override
    public void update(FaqObj faqObj) {
        FaqPo faqPo = FaqConverter.INSTANCE.convertObj2Po(faqObj);
        faqMapper.update(faqPo);
    }

    @Override
    public void deleteById(String id) {
        faqMapper.deleteById(id);
    }

    @Override
    public List<DocumentCitedVo> selectCitedList(List<String> ids) {
        return faqMapper.selectCitedList(ids);
    }

    /* Started by AICoder, pid:nee329abaaz2ad3140dd09cfa0686405236991df */
    @Override
    public Boolean existRelatedFaq(String resourceId) {
        // 使用 LambdaQueryWrapper 构建查询条件，确保 resourceId 匹配
        LambdaQueryWrapper<FaqPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FaqPo::getResourceId,resourceId);

        // 执行查询并返回结果
        return faqMapper.exists(queryWrapper);
    }
    /* Ended by AICoder, pid:nee329abaaz2ad3140dd09cfa0686405236991df */
}

/* Ended by AICoder, pid:f67511129a6017214ee20bc360e514668bb4b8bd */