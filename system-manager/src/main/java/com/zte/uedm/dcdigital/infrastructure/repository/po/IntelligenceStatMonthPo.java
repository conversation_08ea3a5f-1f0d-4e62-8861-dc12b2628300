package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 智能体统计月表PO
 */
@Getter
@Setter
@ToString
@TableName("intelligence_stat_month")
public class IntelligenceStatMonthPo {

    /**
     * 主表id
     */
    private String id;

    /**
     * 日期（例：202506）
     */
    private String day;

    /**
     * 组织ID
     */
    private String deptId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 物料智能体次数
     */
    private String materialNum;

    /**
     * 标书分析智能体次数
     */
    private String bidNum;
}
