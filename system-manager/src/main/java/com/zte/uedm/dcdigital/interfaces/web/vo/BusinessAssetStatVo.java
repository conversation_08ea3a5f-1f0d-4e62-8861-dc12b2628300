package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 商机资产统计响应VO
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BusinessAssetStatVo {

    /**
     * 时间范围内的汇总统计数据（按时间节点）
     */
    private List<TimeStatData> timeStatList;

    /**
     * 汇总数据（包含立项阶段字段）
     */
    private AreaStatData summaryData;

    /**
     * 下级地区统计数据（指定时间点的快照，不包含立项阶段字段）
     */
    private List<AreaStatData> areaStatList;

    /**
     * 时间统计数据（时间范围内每个时间节点的汇总数据）
     */
    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TimeStatData {

        /**
         * 时间节点（例：20250620、202506、2025）
         */
        private String timeNode;

        /**
         * 新增商机数
         */
        private Long projectAddNum;

        /**
         * 启动投标数
         */
        private Long projectStartNum;
    }

    /**
     * 地区统计数据（指定时间点各地区的详细数据）
     */
    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AreaStatData {

        /**
         * 地区名称
         */
        private String areaName;

        /**
         * 总数
         */
        private Long allNum;

        /**
         * 新增商机数
         */
        private Long projectAddNum;

        /**
         * 投标阶段数
         */
        private Long bidNum;

        /**
         * 启动投标数
         */
        private Long projectStartNum;

        /**
         * 交标阶段数
         */
        private Long subBidNum;

        /**
         * 标前阶段数
         */
        private Long beforeBidNum;

        /**
         * 立项阶段数
         */
        private Long projectApprovalNum;
    }
}
