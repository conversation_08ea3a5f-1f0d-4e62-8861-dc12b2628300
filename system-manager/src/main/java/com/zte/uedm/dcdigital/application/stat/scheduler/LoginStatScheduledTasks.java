/* Started by AICoder, pid:v4f1dj9f16v54dd149270923805de42c6aa89c8a */
package com.zte.uedm.dcdigital.application.stat.scheduler;

import com.zte.uedm.dcdigital.domain.service.LoginStatsDomainService;
import com.zte.uedm.dcdigital.domain.utils.StatCurrentDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 这是登录统计相关的定时任务 主要用于将日表数据统计到周表、月表、年表
 */
@Slf4j
@Component
public class LoginStatScheduledTasks {
    @Autowired
    private LoginStatsDomainService loginStatsDomainService;

    // 开发测试时使用 10分钟执行一次
     //@Scheduled(cron = "0 0/10 * * * ?")

     //线上暂定每天凌晨1点执行
    @Scheduled(cron = "0 0 1 * * ?")
    public void synchronizeLoginStatData() {
        log.info("The scheduled task begins to execute");
        Integer previousDayNumber = StatCurrentDateUtils.getPreviousDayNumber();
        Integer previousWeekNumber = StatCurrentDateUtils.getPreviousWeekOfYear();
        Integer previousMonthNumber = StatCurrentDateUtils.getPreviousMonthOfYear();
        Integer previousYearNumber = StatCurrentDateUtils.getPreviousYear();
        loginStatsDomainService.synchronizeLoginStatData(previousDayNumber,previousWeekNumber, previousMonthNumber, previousYearNumber);
        log.info("synchronizeLoginStatData executed successfully");
    }
}

/* Ended by AICoder, pid:v4f1dj9f16v54dd149270923805de42c6aa89c8a */