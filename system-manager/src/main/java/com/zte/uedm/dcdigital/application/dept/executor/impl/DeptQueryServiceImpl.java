/* Started by AICoder, pid:e60b4l275ca11481452b08df605e1328db6225e4 */
package com.zte.uedm.dcdigital.application.dept.executor.impl;

import com.zte.uedm.dcdigital.application.dept.executor.DeptQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.service.DeptDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.DeptQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.AuthDeptVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.DeptUserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DeptQueryServiceImpl implements DeptQueryService {

    @Autowired
    private DeptDomainService deptDomainService;

    @Override
    public List<AuthDeptVo> getDeptTree(DeptQueryDto queryDto) {
        return deptDomainService.getDeptTree(queryDto);
    }

    @Override
    public List<DeptUserVo> getDeptUserListByDeptId(String deptId) {
        return deptDomainService.getDeptUserListByDeptId(deptId);
    }

    @Override
    public PageVO<DeptUserVo> getDeptUserListPage(DeptQueryDto queryDto) {
        return deptDomainService.getDeptUserListPage(queryDto);
    }
    @Override
    public PageVO<DeptUserVo> getSubAllDeptUserListPage(DeptQueryDto queryDto) {
        return deptDomainService.getSubAllDeptUserListPage(queryDto);
    }

    @Override
    public String departmentalLevelByDeptId(String deptId) {
        return deptDomainService.departmentalLevelByDeptId(deptId);
    }
}

/* Ended by AICoder, pid:e60b4l275ca11481452b08df605e1328db6225e4 */