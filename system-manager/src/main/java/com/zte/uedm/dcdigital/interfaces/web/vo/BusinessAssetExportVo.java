package com.zte.uedm.dcdigital.interfaces.web.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商机资产统计导出VO
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BusinessAssetExportVo {

    /**
     * 时间
     */
    @ExcelProperty("时间")
    private String time;

    /**
     * 地区
     */
    @ExcelProperty("地区")
    private String areaName;

    /**
     * 商机总数
     */
    @ExcelProperty("商机总数")
    private Long allNum;

    /**
     * 新增商机
     */
    @ExcelProperty("新增商机")
    private Long projectAddNum;

    /**
     * 投标阶段数
     */
    @ExcelProperty("投标阶段数")
    private Long bidNum;

    /**
     * 启动投标数
     */
    @ExcelProperty("启动投标数")
    private Long projectStartNum;

    /**
     * 交标阶段数
     */
    @ExcelProperty("交标阶段数")
    private Long subBidNum;

    /**
     * 标前阶段数
     */
    @ExcelProperty("标前阶段数")
    private Long beforeBidNum;

    /**
     * 立项阶段数
     */
    @ExcelProperty("立项阶段数")
    private Long projectApprovalNum;
}
