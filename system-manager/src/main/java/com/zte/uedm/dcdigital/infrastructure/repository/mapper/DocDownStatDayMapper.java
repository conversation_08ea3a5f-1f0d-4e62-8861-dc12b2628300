package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocDownStatEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DocDownStatDayPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocDownStatDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginStatDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DocDownStatDayMapper {
    /**
     * 获取指定日期范围内的文档下载统计数据（包含周、月、年的下载次数、预览次数）
     *
     * @param previousDayNumber 前一天的日期
     * @param previousWeekNumber 前一天是当年的第几周
     * @param previousMonthNumber 前一天是当年的第几个月
     * @param previousYearNumber 前一天所在年份
     * @return 文档下载统计数据列表
     */
    List<DocDownStatDto> getDocDownStatWithDataList(@Param("previousDayNumber") Integer previousDayNumber, @Param("previousWeekNumber") Integer previousWeekNumber, @Param("previousMonthNumber") Integer previousMonthNumber, @Param("previousYearNumber") Integer previousYearNumber);

    DocDownStatDayPo selectByUserIdAndResourceIdAndDay(@Param("userId")String userId, @Param("resourceId")String resourceId, @Param("day")int day);

    int addRecordStatDay(DocDownStatDayPo dayPo);

    int updateRecordStatDay(DocDownStatDayPo dayPo);

    List<DocDownStatDayPo> getDownAndViewNum(@Param("item")DocDownStatDayPo dayPo, @Param("list")List<String> deptUserId);

    List<DocDownStatDayPo> getPreviewTop50Data(@Param("item")DocDownStatDayPo dayPo, @Param("list")List<String> deptUserId);

    List<DocDownStatDayPo> getDownloadTop50Data(@Param("item")DocDownStatDayPo dayPo, @Param("list")List<String> deptUserIds);

    List<DocDownStatDayPo> getPreviewDetailsData(@Param("item")DocDownStatDayPo dayPo, @Param("list")List<String> deptUserIds);

    List<DocDownStatDayPo> getDownloadDetailsData(@Param("item")DocDownStatDayPo dayPo, @Param("list")List<String> deptUserIds);

    List<DocDownStatEntity> getUserStatisticFileDate(@Param("item")DocDownStatEntity entity, @Param("list")List<String> deptUserIds);

    List<DocDownStatDayPo> selectByTimeRange(@Param("startTime")Integer startTime, @Param("endTime")Integer endTime);
}
