package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 搜索统计TOP详情查询DTO
 */
@Getter
@Setter
@ToString
public class SearchStatisticTopQueryDto {

    /**
     * 部门id
     */
    @NotBlank(message = "部门id不能为空")
    private String deptId;

    /**
     * 开始时间
     */
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    /**
     * 结束时间
     */
    @NotBlank(message = "结束时间不能为空")
    private String endTime;

    /**
     * 时间类型：1-天，2-周，3-月，4-年
     */
    @NotNull(message = "时间类型不能为空")
    private Integer timeType;

    /**
     * 类型：2-物料，3-文档，4-FAQ
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

    private String pageSize;

    private String pageNum;
}
