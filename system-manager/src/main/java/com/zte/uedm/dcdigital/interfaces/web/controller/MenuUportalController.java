package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.brand.executor.MenuQueryService;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.vo.MenuVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("uportal/menu")
@Api(value = "菜单", tags = {"菜单接口"})
@Controller
public class MenuUportalController {

    @Autowired
    private MenuQueryService menuQueryService;

    @GET
    @Path("/query")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询菜单列表", notes = "查询菜单列表", httpMethod = "GET")
    public BaseResult<List<MenuVo>> queryList()
    {
        List<MenuVo> result = menuQueryService.queryMenuList();
        return BaseResult.success(result);
    }
}
