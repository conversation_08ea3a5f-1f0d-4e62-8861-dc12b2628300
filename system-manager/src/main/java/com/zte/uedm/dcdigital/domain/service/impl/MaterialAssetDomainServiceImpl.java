package com.zte.uedm.dcdigital.domain.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.zte.uedm.dcdigital.common.bean.product.MaterialStatisticsQueryDto;
import com.zte.uedm.dcdigital.common.bean.product.MaterialStatisticsVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductSubcategoryWithCategoryVo;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.*;
import com.zte.uedm.dcdigital.domain.repository.MaterialAssetRepository;
import com.zte.uedm.dcdigital.domain.service.MaterialAssetDomainService;
import com.zte.uedm.dcdigital.domain.utils.DatabaseLockUtil;
import com.zte.uedm.dcdigital.domain.utils.MaterialAssetTimeUtils;
import com.zte.uedm.dcdigital.domain.utils.StatTimeUtils;
import com.zte.uedm.dcdigital.interfaces.web.dto.MaterialAssetBuryingPointDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.MaterialAssetJuniorExportDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.MaterialAssetJuniorQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.MaterialAssetQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.MaterialAssetJuniorVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.MaterialAssetStatVo;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 物料资产领域服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MaterialAssetDomainServiceImpl implements MaterialAssetDomainService {

    @Autowired
    private MaterialAssetRepository materialAssetRepository;

    @Autowired
    private ProductService productService;

    @Autowired
    private DatabaseLockUtil databaseLockUtil;

    // 产品小类名称缓存：key为产品小类ID，value为产品小类名称
    private final Map<String, ProductSubcategoryWithCategoryVo> subcategoryNameCache = new ConcurrentHashMap<>();

    @Override
    public MaterialAssetStatVo getStatMaterial(MaterialAssetQueryDto queryDto) {
        log.info("Start getting material asset statistics, queryDto: {}", queryDto);

        try {

            // 2. 获取产品小类ID列表
            List<String> productCategoryIds = getProductCategoryIds(queryDto.getNodeId(), queryDto.getLevel());
            List<String> allProductCategoryIds = getProductCategoryIds(null, queryDto.getLevel());
            if (CollectionUtils.isEmpty(productCategoryIds)) {
                log.warn("No product categories found for nodeId: {}, level: {}", queryDto.getNodeId(), queryDto.getLevel());
                return new MaterialAssetStatVo();
            }

            // 3. 初始化物料资产数据
            initializeMaterialAssetData(allProductCategoryIds);

            // 3. 查询数据
            List<? extends Object> rawDataList = materialAssetRepository.queryMaterialAssetData(
                productCategoryIds, queryDto.getStartTime(), queryDto.getEndTime(), queryDto.getTimeType());

            // 4. 转换数据格式
            List<MaterialAssetStatVo.MaterialStatData> statDataList = convertToStatData(rawDataList, queryDto.getTimeType());

            // 5. 聚合数据（按时间维度汇总）
            List<MaterialAssetStatVo.MaterialStatData> aggregatedDataList = aggregateDataByTime(statDataList);

            // 6. 补充缺失的时间点
            List<MaterialAssetStatVo.MaterialStatData> completeDataList = fillMissingTimePoints(
                aggregatedDataList, queryDto.getStartTime(), queryDto.getEndTime(), queryDto.getTimeType());

            // 7. 计算变化数
            List<MaterialAssetStatVo.MaterialStatData> dataListWithChanges = calculateChangeNumbers(completeDataList, productCategoryIds, queryDto.getTimeType());

            // 8. 格式化时间显示（在最后一步进行）
            formatTimeDisplayForStatData(dataListWithChanges, queryDto.getTimeType());

            // 9. 构造返回结果
            MaterialAssetStatVo result = new MaterialAssetStatVo();
            result.setDataList(dataListWithChanges);

            log.info("Successfully got material asset statistics, result size: {}", dataListWithChanges.size());
            return result;

        } catch (Exception e) {
            log.error("Error getting material asset statistics", e);
            return new MaterialAssetStatVo();
        }
    }

    @Override
    public MaterialAssetJuniorVo getStatMaterialJunior(MaterialAssetJuniorQueryDto queryDto) {
        log.info("Start getting material asset junior statistics, queryDto: {}", queryDto);

        try {

            // 2. 获取产品小类ID列表
            List<String> productCategoryIds = getProductCategoryIds(queryDto.getNodeId(), queryDto.getLevel());
            List<String> allProductCategoryIds = getProductCategoryIds(null, queryDto.getLevel());
            if (CollectionUtils.isEmpty(productCategoryIds)) {
                log.warn("No product categories found for nodeId: {}, level: {}", queryDto.getNodeId(), queryDto.getLevel());
                return new MaterialAssetJuniorVo();
            }

            // 3. 初始化物料资产数据
            initializeMaterialAssetData(allProductCategoryIds);

            // 3. 查询指定时间点的数据
            List<? extends Object> rawDataList = materialAssetRepository.queryMaterialAssetDataByTimePoint(
                productCategoryIds, queryDto.getTimePoint(), queryDto.getTimeType());

            // 4. 转换为下级统计数据
            List<MaterialAssetJuniorVo.MaterialJuniorData> juniorDataList = convertToJuniorData(rawDataList, queryDto.getTimeType());

            // 5. 计算变化标志
            calculateChangeFlags(juniorDataList, queryDto.getTimePoint(), queryDto.getTimeType());

            // 6. 计算汇总数据
            MaterialAssetJuniorVo.MaterialJuniorData summary = calculateSummary(juniorDataList);

            // 7. 计算汇总数据的变化数和变化标志
            calculateSummaryChangeNumbers(summary, productCategoryIds, queryDto.getTimePoint(), queryDto.getTimeType());

            // 8. 分页处理
            int total = juniorDataList.size();
            List<MaterialAssetJuniorVo.MaterialJuniorData> pagedDataList = applyPagination(
                juniorDataList, queryDto.getPageNum(), queryDto.getPageSize());

            // 9. 构造返回结果
            MaterialAssetJuniorVo result = new MaterialAssetJuniorVo();
            result.setJuniorList(pagedDataList);
            result.setSummary(summary);
            result.setTotal(total);

            log.info("Successfully got material asset junior statistics, total: {}, page size: {}", 
                    total, pagedDataList.size());
            return result;

        } catch (Exception e) {
            log.error("Error getting material asset junior statistics", e);
            return new MaterialAssetJuniorVo();
        }
    }


    /**
     * 根据节点ID和层级获取产品小类ID列表
     */
    private List<String> getProductCategoryIds(String nodeId, Integer level) {
        log.info("Getting product category IDs for nodeId: {}, level: {}", nodeId, level);

        try {
            List<String> ids = new ArrayList<>();
            // 调用产品服务获取下级分类
            List<ProductSubcategoryWithCategoryVo> subcategoryList = productService.querySubcategoriesWithCategoryByParentId(nodeId);
            if (CollectionUtils.isNotEmpty(subcategoryList)) {
                // 提取产品小类ID并缓存产品小类信息
                for (ProductSubcategoryWithCategoryVo subcategory : subcategoryList) {
                    String subcategoryId = subcategory.getSubcategoryId();
                    ids.add(subcategoryId);
                    // 将产品小类信息存入缓存
                    subcategoryNameCache.put(subcategoryId, subcategory);
                    log.debug("Cached subcategory: {} -> {}/{}", subcategoryId,
                            subcategory.getCategoryName(), subcategory.getSubcategoryName());
                }
            }

            log.info("Found {} product category IDs", ids.size());
            return ids;

        } catch (Exception e) {
            log.error("Error getting product category IDs for nodeId: {}, level: {}", nodeId, level, e);
            return Collections.emptyList();
        }
    }

    /**
     * 转换原始数据为统计数据
     */
    private List<MaterialAssetStatVo.MaterialStatData> convertToStatData(List<? extends Object> rawDataList, Integer timeType) {
        if (CollectionUtils.isEmpty(rawDataList)) {
            return Collections.emptyList();
        }

        List<MaterialAssetStatVo.MaterialStatData> statDataList = new ArrayList<>();

        for (Object rawData : rawDataList) {
            MaterialAssetStatVo.MaterialStatData statData = new MaterialAssetStatVo.MaterialStatData();

            switch (timeType) {
                case 1: // 天
                    MaterialAssetDayEntity dayEntity = (MaterialAssetDayEntity) rawData;
                    statData.setDay(dayEntity.getDay());
                    statData.setAllNum(dayEntity.getAllNum());
                    statData.setUpNum(dayEntity.getUpNum());
                    statData.setUpChangeNum(dayEntity.getUpChangeNum());
                    statData.setDownNum(dayEntity.getDownNum());
                    statData.setDownChangeNum(dayEntity.getDownChangeNum());
                    statData.setUpdateNum(dayEntity.getUpdateNum());
                    break;
                case 2: // 周
                    MaterialAssetWeekEntity weekEntity = (MaterialAssetWeekEntity) rawData;
                    statData.setDay(weekEntity.getDay());
                    statData.setAllNum(weekEntity.getAllNum());
                    statData.setUpNum(weekEntity.getUpNum());
                    statData.setUpChangeNum(weekEntity.getUpChangeNum());
                    statData.setDownNum(weekEntity.getDownNum());
                    statData.setDownChangeNum(weekEntity.getDownChangeNum());
                    statData.setUpdateNum(weekEntity.getUpdateNum());
                    break;
                case 3: // 月
                    MaterialAssetMonthEntity monthEntity = (MaterialAssetMonthEntity) rawData;
                    statData.setDay(monthEntity.getDay());
                    statData.setAllNum(monthEntity.getAllNum());
                    statData.setUpNum(monthEntity.getUpNum());
                    statData.setUpChangeNum(monthEntity.getUpChangeNum());
                    statData.setDownNum(monthEntity.getDownNum());
                    statData.setDownChangeNum(monthEntity.getDownChangeNum());
                    statData.setUpdateNum(monthEntity.getUpdateNum());
                    break;
                case 4: // 年
                    MaterialAssetYearEntity yearEntity = (MaterialAssetYearEntity) rawData;
                    statData.setDay(yearEntity.getDay());
                    statData.setAllNum(yearEntity.getAllNum());
                    statData.setUpNum(yearEntity.getUpNum());
                    statData.setUpChangeNum(yearEntity.getUpChangeNum());
                    statData.setDownNum(yearEntity.getDownNum());
                    statData.setDownChangeNum(yearEntity.getDownChangeNum());
                    statData.setUpdateNum(yearEntity.getUpdateNum());
                    break;
                default:
                    log.warn("Unknown time type: {}", timeType);
                    continue;
            }

            statDataList.add(statData);
        }

        return statDataList;
    }

    /**
     * 按时间维度聚合数据
     */
    private List<MaterialAssetStatVo.MaterialStatData> aggregateDataByTime(List<MaterialAssetStatVo.MaterialStatData> statDataList) {
        if (CollectionUtils.isEmpty(statDataList)) {
            return Collections.emptyList();
        }

        // 按时间分组并聚合
        Map<String, List<MaterialAssetStatVo.MaterialStatData>> groupedData = statDataList.stream()
            .collect(Collectors.groupingBy(MaterialAssetStatVo.MaterialStatData::getDay));

        List<MaterialAssetStatVo.MaterialStatData> aggregatedList = new ArrayList<>();

        for (Map.Entry<String, List<MaterialAssetStatVo.MaterialStatData>> entry : groupedData.entrySet()) {
            String day = entry.getKey();
            List<MaterialAssetStatVo.MaterialStatData> dataList = entry.getValue();

            MaterialAssetStatVo.MaterialStatData aggregated = new MaterialAssetStatVo.MaterialStatData();
            aggregated.setDay(day);
            aggregated.setAllNum(dataList.stream().mapToLong(d -> d.getAllNum() != null ? d.getAllNum() : 0L).sum());
            aggregated.setUpNum(dataList.stream().mapToLong(d -> d.getUpNum() != null ? d.getUpNum() : 0L).sum());
            aggregated.setUpChangeNum(dataList.stream().mapToLong(d -> d.getUpChangeNum() != null ? d.getUpChangeNum() : 0L).sum());
            aggregated.setDownNum(dataList.stream().mapToLong(d -> d.getDownNum() != null ? d.getDownNum() : 0L).sum());
            aggregated.setDownChangeNum(dataList.stream().mapToLong(d -> d.getDownChangeNum() != null ? d.getDownChangeNum() : 0L).sum());
            aggregated.setUpdateNum(dataList.stream().mapToLong(d -> d.getUpdateNum() != null ? d.getUpdateNum() : 0L).sum());

            aggregatedList.add(aggregated);
        }

        // 按时间排序
        aggregatedList.sort(Comparator.comparing(MaterialAssetStatVo.MaterialStatData::getDay));

        return aggregatedList;
    }

    /**
     * 填充缺失的时间点
     */
    private List<MaterialAssetStatVo.MaterialStatData> fillMissingTimePoints(
            List<MaterialAssetStatVo.MaterialStatData> dataList, String startTime, String endTime, Integer timeType) {

        // 生成完整的时间序列
        List<String> timePoints = MaterialAssetTimeUtils.generateTimePoints(startTime, endTime, timeType);

        // 创建数据映射
        Map<String, MaterialAssetStatVo.MaterialStatData> dataMap = dataList.stream()
            .collect(Collectors.toMap(MaterialAssetStatVo.MaterialStatData::getDay, data -> data));

        // 填充缺失的时间点
        List<MaterialAssetStatVo.MaterialStatData> result = new ArrayList<>();
        for (String timePoint : timePoints) {
            MaterialAssetStatVo.MaterialStatData data = dataMap.getOrDefault(timePoint, createEmptyStatData(timePoint, timeType));
            result.add(data);
        }

        // 注意：不在这里格式化时间显示，保持原始格式用于后续处理
        // 时间格式化将在最后一步进行

        return result;
    }

    /**
     * 创建空的统计数据
     */
    private MaterialAssetStatVo.MaterialStatData createEmptyStatData(String timePoint, Integer timeType) {
        MaterialAssetStatVo.MaterialStatData emptyData = new MaterialAssetStatVo.MaterialStatData();
        // 保持原始时间格式，不进行格式化
        emptyData.setDay(timePoint);
        emptyData.setAllNum(0L);
        emptyData.setUpNum(0L);
        emptyData.setUpChangeNum(0L);
        emptyData.setDownNum(0L);
        emptyData.setDownChangeNum(0L);
        emptyData.setUpdateNum(0L);
        return emptyData;
    }

    /**
     * 格式化统计数据中的时间显示
     */
    private void formatTimeDisplayForStatData(List<MaterialAssetStatVo.MaterialStatData> dataList, Integer timeType) {
        if (CollectionUtils.isEmpty(dataList) || timeType == null) {
            return;
        }

        for (MaterialAssetStatVo.MaterialStatData data : dataList) {
            if (data.getDay() != null) {
                String formattedDay = StatTimeUtils.formatTimeDisplay(data.getDay(), timeType);
                data.setDay(formattedDay);
            }
        }
    }

    /**
     * 计算变化数
     * 重新计算每个时间点的变化数，解决变化数显示不正确的问题
     * 变化数应该用当前时间节点数减去前一个时间节点数，除非真没查到前一时间节点的数据才能直接变化数等于上架/未上架数
     */
    private List<MaterialAssetStatVo.MaterialStatData> calculateChangeNumbers(
            List<MaterialAssetStatVo.MaterialStatData> dataList, List<String> productCategoryIds, Integer timeType) {

        log.info("Start recalculating change numbers for {} data points", dataList.size());

        // 按时间排序数据
        List<MaterialAssetStatVo.MaterialStatData> sortedDataList = dataList.stream()
                .sorted((a, b) -> a.getDay().compareTo(b.getDay()))
                .collect(Collectors.toList());

        // 重新计算变化数：基于数据库中前一个时间节点的实际数据
        for (MaterialAssetStatVo.MaterialStatData currentData : sortedDataList) {
            try {
                // 计算前一个时间节点
                String previousTimePoint = getString(timeType, currentData);
                if (previousTimePoint == null) continue;

                // 查询前一个时间节点的汇总数据
                long previousUpNum = 0L;
                long previousDownNum = 0L;
                boolean hasPreviousData = false;

                // 遍历所有产品小类，查询前一个时间节点的数据并汇总
                for (String productCategoryId : productCategoryIds) {
                    Object previousData = materialAssetRepository.queryPreviousMaterialAssetData(
                        productCategoryId, previousTimePoint, timeType);

                    if (previousData != null) {
                        hasPreviousData = true;
                        previousUpNum += getPreviousUpNum(previousData, timeType);
                        previousDownNum += getPreviousDownNum(previousData, timeType);
                    }
                }

                // 计算变化数
                Long currentUpNum = currentData.getUpNum() != null ? currentData.getUpNum() : 0L;
                Long currentDownNum = currentData.getDownNum() != null ? currentData.getDownNum() : 0L;

                extracted(currentData, hasPreviousData, currentUpNum, previousUpNum, currentDownNum, previousDownNum);
            } catch (Exception e) {
                log.error("Error calculating change numbers for timePoint: {}", currentData.getDay(), e);
                // 出错时设置默认值
                currentData.setUpChangeNum(0L);
                currentData.setDownChangeNum(0L);
            }
        }

        log.info("Successfully recalculated change numbers for all data points");
        return sortedDataList;
    }

    private void extracted(MaterialAssetStatVo.MaterialStatData currentData, boolean hasPreviousData, Long currentUpNum, long previousUpNum, Long currentDownNum, long previousDownNum) {
        if (hasPreviousData) {
            // 有前一个时间节点的数据，变化数 = 当前数量 - 前一时间节点数量
            Long upChangeNum = currentUpNum - previousUpNum;
            Long downChangeNum = currentDownNum - previousDownNum;
            currentData.setUpChangeNum(upChangeNum);
            currentData.setDownChangeNum(downChangeNum);

            log.info("Recalculated change for time {}: currentUp={}, previousUp={}, upChange={}, currentDown={}, previousDown={}, downChange={}",
                     currentData.getDay(), currentUpNum, previousUpNum, upChangeNum,
                    currentDownNum, previousDownNum, downChangeNum);
        } else {
            // 没有前一个时间节点的数据，变化数为当前数量（相当于从0开始）
            currentData.setUpChangeNum(currentUpNum);
            currentData.setDownChangeNum(currentDownNum);

            log.info("No previous data for time {}: upChange={}, downChange={}",
                     currentData.getDay(), currentUpNum, currentDownNum);
        }
    }

    private String getString(Integer timeType, MaterialAssetStatVo.MaterialStatData currentData) {
        String previousTimePoint = MaterialAssetTimeUtils.getPreviousTimePoint(currentData.getDay(), timeType);
        if (previousTimePoint == null) {
            log.warn("Cannot calculate previous time point for timePoint: {}, timeType: {}", currentData.getDay(), timeType);
            // 无法计算前一个时间节点，变化数设为当前数量
            Long currentUpNum = currentData.getUpNum() != null ? currentData.getUpNum() : 0L;
            Long currentDownNum = currentData.getDownNum() != null ? currentData.getDownNum() : 0L;
            currentData.setUpChangeNum(currentUpNum);
            currentData.setDownChangeNum(currentDownNum);
            return null;
        }
        return previousTimePoint;
    }

    /**
     * 转换为下级统计数据
     */
    private List<MaterialAssetJuniorVo.MaterialJuniorData> convertToJuniorData(List<? extends Object> rawDataList, Integer timeType) {
        if (CollectionUtils.isEmpty(rawDataList)) {
            return Collections.emptyList();
        }

        List<MaterialAssetJuniorVo.MaterialJuniorData> juniorDataList = new ArrayList<>();

        for (Object rawData : rawDataList) {
            MaterialAssetJuniorVo.MaterialJuniorData juniorData = new MaterialAssetJuniorVo.MaterialJuniorData();

            String productCategoryId = null;

            switch (timeType) {
                case 1: // 天
                    MaterialAssetDayEntity dayEntity = (MaterialAssetDayEntity) rawData;
                    productCategoryId = dayEntity.getProductCategoryId();
                    juniorData.setAllNum(dayEntity.getAllNum());
                    juniorData.setUpNum(dayEntity.getUpNum());
                    juniorData.setUpChangeNum(dayEntity.getUpChangeNum());
                    juniorData.setDownNum(dayEntity.getDownNum());
                    juniorData.setDownChangeNum(dayEntity.getDownChangeNum());
                    juniorData.setUpdateNum(dayEntity.getUpdateNum());
                    break;
                case 2: // 周
                    MaterialAssetWeekEntity weekEntity = (MaterialAssetWeekEntity) rawData;
                    productCategoryId = weekEntity.getProductCategoryId();
                    juniorData.setAllNum(weekEntity.getAllNum());
                    juniorData.setUpNum(weekEntity.getUpNum());
                    juniorData.setUpChangeNum(weekEntity.getUpChangeNum());
                    juniorData.setDownNum(weekEntity.getDownNum());
                    juniorData.setDownChangeNum(weekEntity.getDownChangeNum());
                    juniorData.setUpdateNum(weekEntity.getUpdateNum());
                    break;
                case 3: // 月
                    MaterialAssetMonthEntity monthEntity = (MaterialAssetMonthEntity) rawData;
                    productCategoryId = monthEntity.getProductCategoryId();
                    juniorData.setAllNum(monthEntity.getAllNum());
                    juniorData.setUpNum(monthEntity.getUpNum());
                    juniorData.setUpChangeNum(monthEntity.getUpChangeNum());
                    juniorData.setDownNum(monthEntity.getDownNum());
                    juniorData.setDownChangeNum(monthEntity.getDownChangeNum());
                    juniorData.setUpdateNum(monthEntity.getUpdateNum());
                    break;
                case 4: // 年
                    MaterialAssetYearEntity yearEntity = (MaterialAssetYearEntity) rawData;
                    productCategoryId = yearEntity.getProductCategoryId();
                    juniorData.setAllNum(yearEntity.getAllNum());
                    juniorData.setUpNum(yearEntity.getUpNum());
                    juniorData.setUpChangeNum(yearEntity.getUpChangeNum());
                    juniorData.setDownNum(yearEntity.getDownNum());
                    juniorData.setDownChangeNum(yearEntity.getDownChangeNum());
                    juniorData.setUpdateNum(yearEntity.getUpdateNum());
                    break;
                default:
                    log.warn("Unknown time type: {}", timeType);
                    continue;
            }

            // 设置产品小类ID和名称
            juniorData.setProductCategoryId(productCategoryId);
            juniorData.setProductCategoryName(getProductCategoryName(productCategoryId));

            juniorDataList.add(juniorData);
        }

        return juniorDataList;
    }

    /**
     * 获取产品小类名称（格式：产品大类名称/产品小类名称）
     */
    private String getProductCategoryName(String productCategoryId) {
        try {
            // 首先从缓存中查找产品小类名称
            ProductSubcategoryWithCategoryVo categoryName = subcategoryNameCache.get(productCategoryId);

            if (categoryName != null) {
                return categoryName.getCategoryName() + "/" + categoryName.getSubcategoryName();
            } else {
                // 缓存中没有找到，使用默认格式
                log.debug("Product category {} not found in cache, using default format", productCategoryId);
                return "Product Category/" + productCategoryId;
            }

        } catch (Exception e) {
            log.error("Error getting product category name for id: {}", productCategoryId, e);
            return "Unknown Category/" + productCategoryId;
        }
    }

    /**
     * 计算变化标志
     */
    private void calculateChangeFlags(List<MaterialAssetJuniorVo.MaterialJuniorData> juniorDataList, String currentTimePoint, Integer timeType) {
        String previousTimePoint = MaterialAssetTimeUtils.getPreviousTimePoint(currentTimePoint, timeType);

        for (MaterialAssetJuniorVo.MaterialJuniorData current : juniorDataList) {
            try {
                // 查询前一个时间节点的数据
                Object previousData = materialAssetRepository.queryPreviousMaterialAssetData(
                    current.getProductCategoryId(), previousTimePoint, timeType);

                if (previousData != null) {
                    Long previousUpNum = getPreviousUpNum(previousData, timeType);
                    Long previousDownNum = getPreviousDownNum(previousData, timeType);

                    // 重新计算变化数：当前数量 - 前一天数量
                    Long upChangeNum = (current.getUpNum() != null ? current.getUpNum() : 0L) -
                                      (previousUpNum != null ? previousUpNum : 0L);
                    Long downChangeNum = (current.getDownNum() != null ? current.getDownNum() : 0L) -
                                        (previousDownNum != null ? previousDownNum : 0L);

                    // 设置重新计算的变化数
                    current.setUpChangeNum(upChangeNum);
                    current.setDownChangeNum(downChangeNum);

                    // 基于重新计算的变化数计算变化标志
                    current.setUpChangeFlag(calculateChangeFlagByChangeNum(upChangeNum));
                    current.setDownChangeFlag(calculateChangeFlagByChangeNum(downChangeNum));
                } else {
                    // 没有前一个时间节点的数据，变化数为当前数量（相当于从0开始）
                    Long currentUpNum = current.getUpNum() != null ? current.getUpNum() : 0L;
                    Long currentDownNum = current.getDownNum() != null ? current.getDownNum() : 0L;

                    current.setUpChangeNum(currentUpNum);
                    current.setDownChangeNum(currentDownNum);

                    // 基于变化数计算变化标志
                    current.setUpChangeFlag(calculateChangeFlagByChangeNum(currentUpNum));
                    current.setDownChangeFlag(calculateChangeFlagByChangeNum(currentDownNum));
                }
            } catch (Exception e) {
                log.error("Error calculating change flags for productCategoryId: {}", current.getProductCategoryId(), e);
                // 出错时设置默认值
                current.setUpChangeNum(0L);
                current.setDownChangeNum(0L);
                current.setUpChangeFlag(0);
                current.setDownChangeFlag(0);
            }
        }
    }

    /**
     * 获取前一个时间节点的已上架数量
     */
    private Long getPreviousUpNum(Object previousData, Integer timeType) {
        switch (timeType) {
            case 1:
                return ((MaterialAssetDayEntity) previousData).getUpNum();
            case 2:
                return ((MaterialAssetWeekEntity) previousData).getUpNum();
            case 3:
                return ((MaterialAssetMonthEntity) previousData).getUpNum();
            case 4:
                return ((MaterialAssetYearEntity) previousData).getUpNum();
            default:
                return 0L;
        }
    }

    /**
     * 获取前一个时间节点的未上架数量
     */
    private Long getPreviousDownNum(Object previousData, Integer timeType) {
        switch (timeType) {
            case 1:
                return ((MaterialAssetDayEntity) previousData).getDownNum();
            case 2:
                return ((MaterialAssetWeekEntity) previousData).getDownNum();
            case 3:
                return ((MaterialAssetMonthEntity) previousData).getDownNum();
            case 4:
                return ((MaterialAssetYearEntity) previousData).getDownNum();
            default:
                return 0L;
        }
    }

    /**
     * 计算变化标志
     */
    private Integer calculateChangeFlag(Long changeNum, Long previousNum) {
        if (changeNum == null || previousNum == null) {
            return 0; // 不变
        }

        if (changeNum > 0) {
            return 1; // 上升
        } else if (changeNum < 0) {
            return 2; // 下降
        } else {
            return 0; // 不变
        }
    }

    /**
     * 基于变化数计算变化标志（用于汇总数据）
     */
    private Integer calculateChangeFlagByChangeNum(Long changeNum) {
        if (changeNum == null) {
            return 0; // 不变
        }

        if (changeNum > 0) {
            return 1; // 上升
        } else if (changeNum < 0) {
            return 2; // 下降
        } else {
            return 0; // 不变
        }
    }

    /**
     * 计算汇总数据
     */
    private MaterialAssetJuniorVo.MaterialJuniorData calculateSummary(List<MaterialAssetJuniorVo.MaterialJuniorData> juniorDataList) {
        if (CollectionUtils.isEmpty(juniorDataList)) {
            return new MaterialAssetJuniorVo.MaterialJuniorData();
        }

        MaterialAssetJuniorVo.MaterialJuniorData summary = new MaterialAssetJuniorVo.MaterialJuniorData();
        summary.setProductCategoryName("汇总");

        long totalAllNum = 0L;
        long totalUpNum = 0L;
        long totalUpChangeNum = 0L;
        long totalDownNum = 0L;
        long totalDownChangeNum = 0L;
        long totalUpdateNum = 0L;

        for (MaterialAssetJuniorVo.MaterialJuniorData data : juniorDataList) {
            totalAllNum += (data.getAllNum() != null ? data.getAllNum() : 0L);
            totalUpNum += (data.getUpNum() != null ? data.getUpNum() : 0L);
            totalUpChangeNum += (data.getUpChangeNum() != null ? data.getUpChangeNum() : 0L);
            totalDownNum += (data.getDownNum() != null ? data.getDownNum() : 0L);
            totalDownChangeNum += (data.getDownChangeNum() != null ? data.getDownChangeNum() : 0L);
            totalUpdateNum += (data.getUpdateNum() != null ? data.getUpdateNum() : 0L);
        }

        summary.setAllNum(totalAllNum);
        summary.setUpNum(totalUpNum);
        summary.setUpChangeNum(totalUpChangeNum);
        summary.setDownNum(totalDownNum);
        summary.setDownChangeNum(totalDownChangeNum);
        summary.setUpdateNum(totalUpdateNum);

        // 根据汇总的变化数计算变化标志
        summary.setUpChangeFlag(calculateChangeFlag(totalUpChangeNum, null));
        summary.setDownChangeFlag(calculateChangeFlag(totalDownChangeNum, null));

        return summary;
    }

    /**
     * 计算汇总数据的变化数
     */
    private void calculateSummaryChangeNumbers(MaterialAssetJuniorVo.MaterialJuniorData summary,
                                             List<String> productCategoryIds,
                                             String timePoint,
                                             Integer timeType) {
        if (summary == null || CollectionUtils.isEmpty(productCategoryIds)) {
            return;
        }

        try {
            // 计算前一个时间节点
            String previousTimePoint = MaterialAssetTimeUtils.getPreviousTimePoint(timePoint, timeType);
            if (previousTimePoint == null) {
                log.warn("Cannot calculate previous time point for timePoint: {}, timeType: {}", timePoint, timeType);
                return;
            }

            // 查询前一个时间节点的汇总数据
            long previousUpNum = 0L;
            long previousDownNum = 0L;

            for (String productCategoryId : productCategoryIds) {
                Object previousData = materialAssetRepository.queryPreviousMaterialAssetData(
                    productCategoryId, previousTimePoint, timeType);

                if (previousData != null) {
                    previousUpNum += getPreviousUpNum(previousData, timeType);
                    previousDownNum += getPreviousDownNum(previousData, timeType);
                }
            }

            // 计算变化数：当前汇总数量 - 前一时间点汇总数量
            Long currentUpNum = summary.getUpNum() != null ? summary.getUpNum() : 0L;
            Long currentDownNum = summary.getDownNum() != null ? summary.getDownNum() : 0L;

            Long upChangeNum = currentUpNum - previousUpNum;
            Long downChangeNum = currentDownNum - previousDownNum;

            // 设置变化数
            summary.setUpChangeNum(upChangeNum);
            summary.setDownChangeNum(downChangeNum);

            // 重新计算变化标志（基于变化数的值）
            summary.setUpChangeFlag(calculateChangeFlagByChangeNum(upChangeNum));
            summary.setDownChangeFlag(calculateChangeFlagByChangeNum(downChangeNum));

            log.debug("Summary change numbers calculated - upChangeNum: {}, downChangeNum: {}, upChangeFlag: {}, downChangeFlag: {}",
                     upChangeNum, downChangeNum, summary.getUpChangeFlag(), summary.getDownChangeFlag());

        } catch (Exception e) {
            log.error("Error calculating summary change numbers", e);
            // 出错时设置默认值
            summary.setUpChangeNum(0L);
            summary.setDownChangeNum(0L);
            summary.setUpChangeFlag(0);
            summary.setDownChangeFlag(0);
        }
    }

    /**
     * 应用分页
     */
    private List<MaterialAssetJuniorVo.MaterialJuniorData> applyPagination(
            List<MaterialAssetJuniorVo.MaterialJuniorData> dataList, Integer pageNum, Integer pageSize) {

        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        int total = dataList.size();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);

        if (fromIndex >= total) {
            return Collections.emptyList();
        }

        return dataList.subList(fromIndex, toIndex);
    }

    @Override
    public void aggregateMaterialAssetData() {
        log.info("Starting material asset data aggregation with improved logic");
        log.info("Aggregation strategy: Stock data (all_num, up_num, down_num) from latest day, " +
                "Incremental data (up_change_num, down_change_num, update_num) summed over period");

        try {
            // 获取昨天的日期
            LocalDate yesterday = LocalDate.now().minusDays(1);

            log.info("Aggregating material asset data for date: {}", yesterday);

            // 汇聚到周表
            aggregateToWeekTable(yesterday);

            // 汇聚到月表
            aggregateToMonthTable(yesterday);

            // 汇聚到年表
            aggregateToYearTable(yesterday);

            log.info("Material asset data aggregation completed successfully");

        } catch (Exception e) {
            log.error("Failed to aggregate material asset data", e);
            throw new RuntimeException("汇聚物料资产统计数据失败", e);
        }
    }

    /**
     * 汇聚到周表
     */
    private void aggregateToWeekTable(LocalDate date) {
        try {
            // 计算周数
            int year = date.getYear();
            int weekOfYear = date.get(WeekFields.ISO.weekOfYear());
            String weekStr = String.format("%04d%02d", year, weekOfYear);

            // 计算该周的开始和结束日期
            LocalDate startOfWeek = date.with(WeekFields.ISO.dayOfWeek(), 1);
            LocalDate endOfWeek = date.with(WeekFields.ISO.dayOfWeek(), 7);

            String startDayStr = startOfWeek.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String endDayStr = endOfWeek.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            log.info("Aggregating material asset data to week table for week: {}, date range: {} to {}",
                    weekStr, startDayStr, endDayStr);
            log.info("Using improved aggregation logic: stock data from latest day, incremental data summed");

            // 查询该周的汇聚数据（使用改进的汇聚逻辑）
            List<MaterialAssetDayEntity> aggregatedData = materialAssetRepository.aggregateDayDataByDateRange(startDayStr, endDayStr);

            // 处理汇聚数据
            for (MaterialAssetDayEntity data : aggregatedData) {
                log.debug("Processing week aggregation for productCategoryId: {}, allNum: {}, upNum: {}, downNum: {}, " +
                         "upChangeNum: {}, downChangeNum: {}, updateNum: {}",
                         data.getProductCategoryId(), data.getAllNum(), data.getUpNum(), data.getDownNum(),
                         data.getUpChangeNum(), data.getDownChangeNum(), data.getUpdateNum());
                processWeekAggregation(data, weekStr);
            }

            log.info("Week table aggregation completed for week: {}, processed {} product categories",
                    weekStr, aggregatedData.size());

        } catch (Exception e) {
            log.error("Failed to aggregate to week table", e);
        }
    }

    /**
     * 处理周表汇聚
     */
    private void processWeekAggregation(MaterialAssetDayEntity dayData, String weekStr) {
        try {
            // 验证汇聚数据的合理性
            if (dayData.getAllNum() == null || dayData.getUpNum() == null || dayData.getDownNum() == null) {
                log.warn("Stock data is null for productCategoryId: {}, week: {}, " +
                        "allNum: {}, upNum: {}, downNum: {}",
                        dayData.getProductCategoryId(), weekStr,
                        dayData.getAllNum(), dayData.getUpNum(), dayData.getDownNum());
            }

            // 创建周表实体
            MaterialAssetWeekEntity weekEntity = new MaterialAssetWeekEntity();
            weekEntity.setId(UUID.randomUUID().toString());
            weekEntity.setDay(weekStr);
            weekEntity.setProductCategoryId(dayData.getProductCategoryId());
            weekEntity.setAllNum(dayData.getAllNum());
            weekEntity.setUpNum(dayData.getUpNum());
            weekEntity.setUpChangeNum(dayData.getUpChangeNum());
            weekEntity.setDownNum(dayData.getDownNum());
            weekEntity.setDownChangeNum(dayData.getDownChangeNum());
            weekEntity.setUpdateNum(dayData.getUpdateNum());
            weekEntity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 插入或更新数据（Repository层会处理查询和更新逻辑）
            materialAssetRepository.insertOrUpdateWeekData(weekEntity);

            log.debug("Successfully processed week aggregation for productCategoryId: {}, week: {}",
                     dayData.getProductCategoryId(), weekStr);

        } catch (Exception e) {
            log.error("Failed to process week aggregation for productCategoryId: {}",
                     dayData.getProductCategoryId(), e);
        }
    }

    /**
     * 汇聚到月表
     */
    private void aggregateToMonthTable(LocalDate date) {
        try {
            // 计算月份标识（格式：YYYYMM，如202506表示2025年6月）
            String monthStr = date.format(DateTimeFormatter.ofPattern("yyyyMM"));

            log.info("Aggregating to month table for month: {}", monthStr);

            // 计算该月的开始和结束日期
            LocalDate startOfMonth = date.withDayOfMonth(1);
            LocalDate endOfMonth = date.withDayOfMonth(date.lengthOfMonth());

            String startDayStr = startOfMonth.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String endDayStr = endOfMonth.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            log.info("Aggregating material asset data to month table for month: {}, date range: {} to {}",
                    monthStr, startDayStr, endDayStr);
            log.info("Using improved aggregation logic: stock data from latest day, incremental data summed");

            // 查询该月的汇聚数据（使用改进的汇聚逻辑）
            List<MaterialAssetDayEntity> aggregatedData = materialAssetRepository.aggregateDayDataByDateRange(startDayStr, endDayStr);

            // 处理汇聚数据
            for (MaterialAssetDayEntity data : aggregatedData) {
                log.debug("Processing month aggregation for productCategoryId: {}, allNum: {}, upNum: {}, downNum: {}, " +
                         "upChangeNum: {}, downChangeNum: {}, updateNum: {}",
                         data.getProductCategoryId(), data.getAllNum(), data.getUpNum(), data.getDownNum(),
                         data.getUpChangeNum(), data.getDownChangeNum(), data.getUpdateNum());
                processMonthAggregation(data, monthStr);
            }

            log.info("Month table aggregation completed for month: {}, processed {} product categories",
                    monthStr, aggregatedData.size());

        } catch (Exception e) {
            log.error("Failed to aggregate to month table", e);
        }
    }

    /**
     * 处理月表汇聚
     */
    private void processMonthAggregation(MaterialAssetDayEntity dayData, String monthStr) {
        try {
            // 验证汇聚数据的合理性
            if (dayData.getAllNum() == null || dayData.getUpNum() == null || dayData.getDownNum() == null) {
                log.warn("Stock data is null for productCategoryId: {}, month: {}, " +
                        "allNum: {}, upNum: {}, downNum: {}",
                        dayData.getProductCategoryId(), monthStr,
                        dayData.getAllNum(), dayData.getUpNum(), dayData.getDownNum());
            }

            // 创建月表实体
            MaterialAssetMonthEntity monthEntity = new MaterialAssetMonthEntity();
            monthEntity.setId(UUID.randomUUID().toString());
            monthEntity.setDay(monthStr);
            monthEntity.setProductCategoryId(dayData.getProductCategoryId());
            monthEntity.setAllNum(dayData.getAllNum());
            monthEntity.setUpNum(dayData.getUpNum());
            monthEntity.setUpChangeNum(dayData.getUpChangeNum());
            monthEntity.setDownNum(dayData.getDownNum());
            monthEntity.setDownChangeNum(dayData.getDownChangeNum());
            monthEntity.setUpdateNum(dayData.getUpdateNum());
            monthEntity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 插入或更新数据（Repository层会处理查询和更新逻辑）
            materialAssetRepository.insertOrUpdateMonthData(monthEntity);

            log.debug("Successfully processed month aggregation for productCategoryId: {}, month: {}",
                     dayData.getProductCategoryId(), monthStr);

        } catch (Exception e) {
            log.error("Failed to process month aggregation for productCategoryId: {}",
                     dayData.getProductCategoryId(), e);
        }
    }

    /**
     * 汇聚到年表
     */
    private void aggregateToYearTable(LocalDate date) {
        try {
            // 计算年份标识（格式：YYYY，如2025表示2025年）
            String yearStr = String.valueOf(date.getYear());

            log.info("Aggregating to year table for year: {}", yearStr);

            // 计算该年的开始和结束日期
            LocalDate startOfYear = date.withDayOfYear(1);
            LocalDate endOfYear = date.withDayOfYear(date.lengthOfYear());

            String startDayStr = startOfYear.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String endDayStr = endOfYear.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            log.info("Aggregating material asset data to year table for year: {}, date range: {} to {}",
                    yearStr, startDayStr, endDayStr);
            log.info("Using improved aggregation logic: stock data from latest day, incremental data summed");

            // 查询该年的汇聚数据（使用改进的汇聚逻辑）
            List<MaterialAssetDayEntity> aggregatedData = materialAssetRepository.aggregateDayDataByDateRange(startDayStr, endDayStr);

            // 处理汇聚数据
            for (MaterialAssetDayEntity data : aggregatedData) {
                log.debug("Processing year aggregation for productCategoryId: {}, allNum: {}, upNum: {}, downNum: {}, " +
                         "upChangeNum: {}, downChangeNum: {}, updateNum: {}",
                         data.getProductCategoryId(), data.getAllNum(), data.getUpNum(), data.getDownNum(),
                         data.getUpChangeNum(), data.getDownChangeNum(), data.getUpdateNum());
                processYearAggregation(data, yearStr);
            }

            log.info("Year table aggregation completed for year: {}, processed {} product categories",
                    yearStr, aggregatedData.size());

        } catch (Exception e) {
            log.error("Failed to aggregate to year table", e);
        }
    }

    /**
     * 处理年表汇聚
     */
    private void processYearAggregation(MaterialAssetDayEntity dayData, String yearStr) {
        try {
            // 验证汇聚数据的合理性
            if (dayData.getAllNum() == null || dayData.getUpNum() == null || dayData.getDownNum() == null) {
                log.warn("Stock data is null for productCategoryId: {}, year: {}, " +
                        "allNum: {}, upNum: {}, downNum: {}",
                        dayData.getProductCategoryId(), yearStr,
                        dayData.getAllNum(), dayData.getUpNum(), dayData.getDownNum());
            }

            // 创建年表实体
            MaterialAssetYearEntity yearEntity = new MaterialAssetYearEntity();
            yearEntity.setId(UUID.randomUUID().toString());
            yearEntity.setDay(yearStr);
            yearEntity.setProductCategoryId(dayData.getProductCategoryId());
            yearEntity.setAllNum(dayData.getAllNum());
            yearEntity.setUpNum(dayData.getUpNum());
            yearEntity.setUpChangeNum(dayData.getUpChangeNum());
            yearEntity.setDownNum(dayData.getDownNum());
            yearEntity.setDownChangeNum(dayData.getDownChangeNum());
            yearEntity.setUpdateNum(dayData.getUpdateNum());
            yearEntity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 插入或更新数据（Repository层会处理查询和更新逻辑）
            materialAssetRepository.insertOrUpdateYearData(yearEntity);

            log.debug("Successfully processed year aggregation for productCategoryId: {}, year: {}",
                     dayData.getProductCategoryId(), yearStr);

        } catch (Exception e) {
            log.error("Failed to process year aggregation for productCategoryId: {}",
                     dayData.getProductCategoryId(), e);
        }
    }

    @Override
    public void exportMaterialAssetJunior(MaterialAssetJuniorExportDto exportDto) {
        log.info("exportMaterialAssetJunior start, exportDto: {}", exportDto);

        try {
            // 1. 参数验证
            if (exportDto == null) {
                throw new IllegalArgumentException("Export parameters cannot be null");
            }

            // 2. 构建查询参数，获取全量数据（不分页）
            MaterialAssetJuniorQueryDto queryDto = new MaterialAssetJuniorQueryDto();
            queryDto.setNodeId(exportDto.getNodeId());
            queryDto.setLevel(exportDto.getLevel());
            queryDto.setTimePoint(exportDto.getTimePoint());
            queryDto.setTimeType(exportDto.getTimeType());
            // 不设置分页参数，获取全量数据

            // 3. 获取全量数据
            List<MaterialAssetJuniorVo.MaterialJuniorData> allDataList = getAllMaterialAssetJuniorData(queryDto);

            if (CollectionUtils.isEmpty(allDataList)) {
                log.warn("No data found for export, exportDto: {}", exportDto);
                // 即使没有数据也要生成空的Excel文件
                allDataList = new ArrayList<>();
            }

            // 4. 导出Excel
            exportMaterialJuniorDataToExcel(allDataList, exportDto);

            log.info("exportMaterialAssetJunior completed successfully, exported {} records", allDataList.size());

        } catch (Exception e) {
            log.error("exportMaterialAssetJunior error", e);
            throw new RuntimeException("Export material asset junior statistics failed", e);
        }
    }

    /**
     * 获取物料下级统计全量数据（不分页）
     */
    private List<MaterialAssetJuniorVo.MaterialJuniorData> getAllMaterialAssetJuniorData(MaterialAssetJuniorQueryDto queryDto) {
        log.info("Getting all material asset junior data for export, queryDto: {}", queryDto);

        try {
            // 2. 获取产品小类ID列表
            List<String> productCategoryIds = getProductCategoryIds(queryDto.getNodeId(), queryDto.getLevel());
            if (CollectionUtils.isEmpty(productCategoryIds)) {
                log.warn("No product categories found for nodeId: {}, level: {}", queryDto.getNodeId(), queryDto.getLevel());
                return Collections.emptyList();
            }

            // 3. 查询指定时间点的数据（全量，不分页）
            List<? extends Object> rawDataList = materialAssetRepository.queryMaterialAssetDataByTimePoint(
                productCategoryIds, queryDto.getTimePoint(), queryDto.getTimeType());

            // 4. 转换为下级统计数据
            List<MaterialAssetJuniorVo.MaterialJuniorData> juniorDataList = convertToJuniorData(rawDataList, queryDto.getTimeType());

            // 5. 计算变化标志
            calculateChangeFlags(juniorDataList, queryDto.getTimePoint(), queryDto.getTimeType());

            log.info("Found {} material asset junior data records for export", juniorDataList.size());
            return juniorDataList;

        } catch (Exception e) {
            log.error("Error getting all material asset junior data for export", e);
            return Collections.emptyList();
        }
    }

    /**
     * 导出物料下级统计数据到Excel
     */
    private void exportMaterialJuniorDataToExcel(List<MaterialAssetJuniorVo.MaterialJuniorData> dataList,
                                                MaterialAssetJuniorExportDto exportDto) {
        log.info("Exporting material junior data to Excel, data size: {}", dataList.size());

        try {
            // 获取HttpServletResponse
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                throw new RuntimeException("Cannot get HttpServletResponse from RequestContextHolder");
            }
            HttpServletResponse response = attributes.getResponse();
            if (response == null) {
                throw new RuntimeException("HttpServletResponse is null");
            }

            // 设置响应头
            String fileName = generateExcelFileName(exportDto);
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);

            // 构建Excel表头
            List<List<String>> headers = buildMaterialJuniorExcelHeaders();

            // 构建Excel数据
            List<List<Object>> excelData = buildMaterialJuniorExcelData(dataList);

            // 写入Excel
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                EasyExcel.write(outputStream)
                        .excelType(ExcelTypeEnum.XLSX)
                        .charset(StandardCharsets.UTF_8)
                        .registerWriteHandler(new SimpleColumnWidthStyleStrategy(20))
                        .head(headers)
                        .sheet("物料下级统计")
                        .doWrite(excelData);
            }

            log.info("Excel export completed successfully");

        } catch (Exception e) {
            log.error("Error exporting material junior data to Excel", e);
            throw new RuntimeException("Export to Excel failed", e);
        }
    }

    /**
     * 生成Excel文件名
     */
    private String generateExcelFileName(MaterialAssetJuniorExportDto exportDto) {
        return String.format("物料下级统计_%s.xlsx",
                exportDto.getTimePoint().replace("-", "").replace(":", ""));
    }


    /**
     * 构建Excel表头
     */
    private List<List<String>> buildMaterialJuniorExcelHeaders() {
        List<List<String>> headers = new ArrayList<>();

        headers.add(Arrays.asList("产品分类"));
        headers.add(Arrays.asList("物料总数"));
        headers.add(Arrays.asList("已上架数量"));
        headers.add(Arrays.asList("已上架变化数"));
        headers.add(Arrays.asList("未上架数量"));
        headers.add(Arrays.asList("未上架变化数"));
        headers.add(Arrays.asList("更新物料数"));

        return headers;
    }

    /**
     * 构建Excel数据
     */
    private List<List<Object>> buildMaterialJuniorExcelData(List<MaterialAssetJuniorVo.MaterialJuniorData> dataList) {
        List<List<Object>> excelData = new ArrayList<>();

        for (MaterialAssetJuniorVo.MaterialJuniorData data : dataList) {
            List<Object> row = new ArrayList<>();

            row.add(data.getProductCategoryName() != null ? data.getProductCategoryName() : "");
            row.add(data.getAllNum() != null ? data.getAllNum() : 0);
            row.add(data.getUpNum() != null ? data.getUpNum() : 0);
            row.add(data.getUpChangeNum() != null ? data.getUpChangeNum() : 0);
            row.add(data.getDownNum() != null ? data.getDownNum() : 0);
            row.add(data.getDownChangeNum() != null ? data.getDownChangeNum() : 0);
            row.add(data.getUpdateNum() != null ? data.getUpdateNum() : 0);

            excelData.add(row);
        }

        return excelData;
    }


    /**
     * 初始化物料资产数据
     * 如果日表中没有数据，则从ProductService获取过去一年的数据并存储
     * 如果有数据，则查询当前日期的数据并存储
     * 使用分布式锁避免并发初始化导致的数据错乱
     */
    private void initializeMaterialAssetData(List<String> productCategoryIds) {
        log.info("Starting to initialize material asset data, product category count: {}", productCategoryIds.size());

        // 使用数据库锁执行初始化操作
        boolean lockAcquired = databaseLockUtil.executeWithLock(productCategoryIds, () -> {
            try {
                // 在锁内再次检查日表中是否有数据（双重检查）
                boolean hasData = materialAssetRepository.hasAnyDayData(productCategoryIds);

                if (!hasData) {
                    // 没有数据，查询过去一年的数据
                    log.info("No data found in day table, starting to query historical data for the past year");
                    initializeHistoricalData(productCategoryIds);
                } else {
                    // 有数据，查询当前日期的数据并存储
                    log.info("Data exists in day table, querying current date data");
                    updateCurrentDayData(productCategoryIds);
                }

            } catch (Exception e) {
                log.error("Failed to initialize material asset data within lock", e);
                // 在锁内的异常需要抛出，让外层处理
                throw new RuntimeException("Failed to initialize material asset data", e);
            }
        });

        if (!lockAcquired) {
            // 获取锁失败，可能其他线程正在初始化，等待一段时间后再次检查数据是否已存在
            log.warn("Failed to acquire lock for material asset initialization, checking if data exists after waiting");
            try {
                // 等待一段时间，让其他线程完成初始化
                Thread.sleep(1000);

                // 检查数据是否已经被其他线程初始化
                boolean hasData = materialAssetRepository.hasAnyDayData(productCategoryIds);
                if (hasData) {
                    log.info("Data has been initialized by another thread, proceeding with query");
                } else {
                    log.warn("Data still not available after waiting, query may return empty results");
                }
            } catch (InterruptedException e) {
                log.warn("Interrupted while waiting for data initialization", e);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                log.error("Error checking data availability after lock acquisition failure", e);
            }
        }
    }

    /**
     * 初始化历史数据（过去一年）
     */
    private void initializeHistoricalData(List<String> productCategoryIds) {
        try {
            // 计算时间范围：当前日期往前推一年
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusYears(1);

            String startTime = startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String endTime = endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 构建查询参数
            MaterialStatisticsQueryDto queryDto = new MaterialStatisticsQueryDto();
            queryDto.setProductCategoryIds(productCategoryIds);
            queryDto.setStartTime(startTime);
            queryDto.setEndTime(endTime);
            queryDto.setTimeType(1); // 按天查询

            // 调用ProductService查询数据
            List<MaterialStatisticsVo> materialStatisticsVos = productService.queryMaterialStatisticsByTimeRange(queryDto);

            if (CollectionUtils.isNotEmpty(materialStatisticsVos)) {
                // 转换为MaterialAssetDayEntity并批量插入
                List<MaterialAssetDayEntity> dayEntities = convertMaterialStatisticsToEntity(materialStatisticsVos);
                materialAssetRepository.batchInsertDayData(dayEntities);
                log.info("Successfully initialized historical data, inserted records count: {}", dayEntities.size());
            } else {
                log.warn("Historical data queried from ProductService is empty");
            }

        } catch (Exception e) {
            log.error("Failed to initialize historical data", e);
            throw new RuntimeException("Failed to initialize historical data", e);
        }
    }

    /**
     * 更新当前日期数据
     * 根据产品小类ID和当前日期判断是否需要插入或更新数据
     */
    private void updateCurrentDayData(List<String> productCategoryIds) {
        try {
            // 获取当前日期
            String currentDay = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 为每个产品小类ID查询当前日期的数据
            for (String productCategoryId : productCategoryIds) {
                updateCurrentDayDataForCategory(productCategoryId, currentDay);
            }

            log.info("Successfully updated current date data, processed product categories count: {}", productCategoryIds.size());

        } catch (Exception e) {
            log.error("Failed to update current date data", e);
            throw new RuntimeException("Failed to update current date data", e);
        }
    }

    /**
     * 为单个产品小类更新当前日期数据
     */
    private void updateCurrentDayDataForCategory(String productCategoryId, String currentDay) {
        try {
            // 构建查询参数，只查询当前产品小类
            MaterialStatisticsQueryDto queryDto = new MaterialStatisticsQueryDto();
            queryDto.setProductCategoryIds(Arrays.asList(productCategoryId));
            queryDto.setStartTime(currentDay);
            queryDto.setEndTime(currentDay);
            queryDto.setTimeType(1); // 按天查询

            // 调用ProductService查询当前日期数据
            List<MaterialStatisticsVo> materialStatisticsVos = productService.queryMaterialStatisticsByTimeRange(queryDto);

            if (CollectionUtils.isNotEmpty(materialStatisticsVos)) {
                // 转换为MaterialAssetDayEntity
                List<MaterialAssetDayEntity> dayEntities = convertMaterialStatisticsToEntity(materialStatisticsVos);

                // 对每个实体进行插入或更新（基于产品小类ID和日期）
                for (MaterialAssetDayEntity entity : dayEntities) {
                    // 统计当前日期该产品小类的更新物料数
                    Long updateCount = materialAssetRepository.countUpdatedMaterialsByDayAndCategory(
                            entity.getDay(), entity.getProductCategoryId());
                    entity.setUpdateNum(updateCount);

                    materialAssetRepository.insertOrUpdateDayData(entity);
                }

                log.debug("Successfully updated product category {} data for date {}", productCategoryId, currentDay);
            } else {
                log.warn("No data found for product category {} on date {}", productCategoryId, currentDay);
            }

        } catch (Exception e) {
            log.error("Failed to update data for product category {} on date {}", productCategoryId, currentDay, e);
            // 不抛出异常，继续处理其他产品小类
        }
    }

    /**
     * 将MaterialStatisticsVo转换为MaterialAssetDayEntity
     */
    private List<MaterialAssetDayEntity> convertMaterialStatisticsToEntity(List<MaterialStatisticsVo> materialStatisticsVos) {
        List<MaterialAssetDayEntity> dayEntities = new ArrayList<>();
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        for (MaterialStatisticsVo vo : materialStatisticsVos) {
            MaterialAssetDayEntity entity = new MaterialAssetDayEntity();
            entity.setId(UUID.randomUUID().toString());
            entity.setDay(vo.getTimePoint());
            entity.setProductCategoryId(vo.getProductCategoryId());
            entity.setAllNum(vo.getTotalMaterialCount());
            entity.setUpNum(vo.getListedMaterialCount());
            entity.setUpChangeNum(vo.getListedMaterialChange());
            entity.setDownNum(vo.getUnlistedMaterialCount());
            entity.setDownChangeNum(vo.getUnlistedMaterialChange());
            // 统计该日期该产品小类的更新物料数
            Long updateCount = materialAssetRepository.countUpdatedMaterialsByDayAndCategory(
                    vo.getTimePoint(), vo.getProductCategoryId());
            entity.setUpdateNum(updateCount);
            entity.setCreateTime(currentTime);

            dayEntities.add(entity);
        }

        return dayEntities;
    }

    @Override
    public void processMaterialEditBuryingPoint(MaterialAssetBuryingPointDto buryingPointDto) {
        log.info("Processing material edit burying point, dto: {}", buryingPointDto);

        try {
            // 参数验证
            if (buryingPointDto == null) {
                log.warn("Burying point DTO is null, skipping processing");
                return;
            }

            if (StringUtils.isBlank(buryingPointDto.getResourceId())) {
                log.warn("Resource ID (material ID) is blank, skipping processing");
                return;
            }

            if (buryingPointDto.getOperationType() == null || buryingPointDto.getOperationType() != 1) {
                log.warn("Operation type is not for material (should be 1), skipping processing");
                return;
            }

            // 获取当前日期
            String currentDay = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            // 构造物料编辑记录实体
            MaterialAssetUpdEntity updEntity = new MaterialAssetUpdEntity();
            updEntity.setId(UUID.randomUUID().toString());
            updEntity.setDay(currentDay);
            updEntity.setProductCategoryId(buryingPointDto.getTypeId());
            updEntity.setMaterialId(buryingPointDto.getResourceId());
            updEntity.setCreateTime(currentTime);

            // 插入或更新记录（同一物料同一日期只保留一条记录）
            materialAssetRepository.insertOrUpdateMaterialEditRecord(updEntity);

            log.info("Successfully processed material edit burying point for materialId: {}, day: {}",
                    buryingPointDto.getResourceId(), currentDay);

        } catch (Exception e) {
            log.error("Error processing material edit burying point", e);
            // 埋点处理失败不应该影响主流程，只记录日志
        }
    }

    @Override
    @Async
    public void processMaterialEditBuryingPointAsync(MaterialAssetBuryingPointDto buryingPointDto) {
        log.info("Processing material edit burying point asynchronously, dto: {}", buryingPointDto);

        try {
            // 调用同步方法处理埋点数据
            processMaterialEditBuryingPoint(buryingPointDto);
            log.info("Async material edit burying point processing completed successfully");
        } catch (Exception e) {
            log.error("Error in async material edit burying point processing", e);
            // 异步埋点处理失败不应该影响主流程，只记录日志
        }
    }
}
