package com.zte.uedm.dcdigital.infrastructure.repository.converter;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.PageStatDayEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.PageStatMonthEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.PageStatWeekEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.PageStatYearEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatDayPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatMonthPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatWeekPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatYearPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        imports = {
                PageStatDayPo.class,
                PageStatDayEntity.class,
                PageStatMonthPo.class,
                PageStatMonthEntity.class,
                PageStatWeekPo.class,
                PageStatWeekEntity.class,
                PageStatYearPo.class,
                PageStatYearEntity.class
        }
)
public interface PageVisitConverter {
    // 使用MapStruct的实例获取方式
    PageVisitConverter INSTANCE = Mappers.getMapper(PageVisitConverter.class);

    //PageStatDayEntity转PageStatDayPo
    PageStatDayPo pageStatDayEntityToPageStatDayPo(PageStatDayEntity pageStatDayEntity);
    //PageStatDayPo转PageStatDayEntity
    PageStatDayEntity pageStatDayPoToPageStatDayEntity(PageStatDayPo pageStatDayPo);
    //PageStatWeekEntity转PageStatWeekPo
    PageStatWeekPo pageStatWeekEntityToPageStatWeekPo(PageStatWeekEntity pageStatWeekEntity);
    //PageStatWeekPo转PageStatWeekEntity
    PageStatWeekEntity pageStatWeekPoToPageStatWeekEntity(PageStatWeekPo pageStatWeekPo);
    //PageStatMonthEntity转PageStatMonthPo
    PageStatMonthPo pageStatMonthEntityToPageStatMonthPo(PageStatMonthEntity pageStatMonthEntity);
    //PageStatMonthPo转PageStatMonthEntity
    PageStatMonthEntity pageStatMonthPoToPageStatMonthEntity(PageStatMonthPo pageStatMonthPo);
    //PageStatYearEntity转PageStatYearPo
    PageStatYearPo pageStatYearEntityToPageStatYearPo(PageStatYearEntity pageStatYearEntity);
    //PageStatYearPo转PageStatYearEntity
    PageStatYearEntity pageStatYearPoToPageStatYearEntity(PageStatYearPo pageStatYearPo);

    //PageStatDayEntityList转PageStatDayPoList
    List<PageStatDayPo> pageStatDayEntityListToPageStatDayPoList(List<PageStatDayEntity> pageStatDayEntityList);
    //PageStatDayPoList转PageStatDayEntityList
    List<PageStatDayEntity> pageStatDayPoListToPageStatDayEntityList(List<PageStatDayPo> pageStatDayPoList);
    //PageStatWeekEntityList转PageStatWeekPoList
    List<PageStatWeekPo> pageStatWeekEntityListToPageStatWeekPoList(List<PageStatWeekEntity> pageStatWeekEntityList);
    //PageStatWeekPoList转PageStatWeekEntityList
    List<PageStatWeekEntity> pageStatWeekPoListToPageStatWeekEntityList(List<PageStatWeekPo> pageStatWeekPoList);
    //PageStatMonthEntityList转PageStatMonthPoList
    List<PageStatMonthPo> pageStatMonthEntityListToPageStatMonthPoList(List<PageStatMonthEntity> pageStatMonthEntityList);
    //PageStatMonthPoList转PageStatMonthEntityList
    List<PageStatMonthEntity> pageStatMonthPoListToPageStatMonthEntityList(List<PageStatMonthPo> pageStatMonthPoList);
    //PageStatYearEntityList转PageStatYearPoList
    List<PageStatYearPo> pageStatYearEntityListToPageStatYearPoList(List<PageStatYearEntity> pageStatYearEntityList);
    //PageStatYearPoList转PageStatYearEntityList
    List<PageStatYearEntity> pageStatYearPoListToPageStatYearEntityList(List<PageStatYearPo> pageStatYearPoList);
}
