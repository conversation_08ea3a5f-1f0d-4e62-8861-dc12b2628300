package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 文档下载周统计数据持久化对象
 */
@Getter
@Setter
@ToString
@TableName("file_stat_year")
public class DocDownStatYearPo {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 统计日期（格式：YYYY）
     */
    @TableField("day")
    private Integer day;

    /**
     * 部门/组织ID
     */
    @TableField("dept_id")
    private String deptId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 资源ID
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * 下载次数
     */
    @TableField("download_num")
    private Integer downloadNum;

    /**
     * 预览次数
     */
    @TableField("preview_num")
    private Integer previewNum;

    // 查询条件字段（非数据库字段）
    @TableField(exist = false)
    private Integer beginTime;

    @TableField(exist = false)
    private Integer endTime;

}
