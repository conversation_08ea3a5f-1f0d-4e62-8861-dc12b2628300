package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/* Started by AICoder, pid:91976xd506l8665148430a67f0ae9e55c2c6b1ce */
/**
 * 常见问题解答持久化对象 (PO)
 */
@Getter
@Setter
@ToString
@TableName("faq")
public class FaqPo {
    /**
     * 唯一标识符
     */
    @LogMark(range = {OperationMethodEnum.DELETE})
    private String id;

    /**
     * 类型
     */
    @LogMark(range = {OperationMethodEnum.DELETE})
    private Integer type;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 问题
     */
    @LogMark(range = {OperationMethodEnum.DELETE})
    private String question;

    /**
     * 答案
     */
    private String answer;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;
}

/* Ended by AICoder, pid:91976xd506l8665148430a67f0ae9e55c2c6b1ce */
