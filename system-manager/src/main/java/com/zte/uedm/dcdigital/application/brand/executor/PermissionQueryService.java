package com.zte.uedm.dcdigital.application.brand.executor;

import com.zte.uedm.dcdigital.interfaces.web.vo.PermissionVo;

import java.util.List;

public interface PermissionQueryService {

    List<PermissionVo> queryPermissionList();

    /**
     * 根据工号查询当前工号拥有的操作码
     * @param workNumber 工号
     * @return
     */
    List<PermissionVo> queryPermissionListByWorkNum(String workNumber);

    /**
     * 根据工号和资源id查询当前工号拥有的操作码
     * @param workNumber 工号
     * @param resourceId 资源id
     * @return
     */
    List<PermissionVo> queryPermissionListByWorkNumAndResourceId(String workNumber, String resourceId);

    List<PermissionVo> queryPermissionListByWorkNumAndResourceId(String workNumber,List<String> resourceIds);
}
