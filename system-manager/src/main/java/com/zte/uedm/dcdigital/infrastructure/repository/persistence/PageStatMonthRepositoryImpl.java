/* Started by AICoder, pid:357807344c5069514bb10a62d0690a544b8462b4 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.PageStatMonthEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.PageStatMonthRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.PageVisitConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.PageStatMonthMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatMonthPo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class PageStatMonthRepositoryImpl implements PageStatMonthRepository {

    @Autowired
    private PageStatMonthMapper pageStatMonthMapper;

    @Override
    public void addPageStatMonth(PageStatMonthEntity monthEntity) {
        PageStatMonthPo po = PageVisitConverter.INSTANCE.pageStatMonthEntityToPageStatMonthPo(monthEntity);
        pageStatMonthMapper.insertPageStatMonth(po);
    }

    @Override
    public void addPageStatMonthList(List<PageStatMonthEntity> monthEntityList) {
        if (CollectionUtils.isEmpty(monthEntityList)) {
            return;
        }

        List<PageStatMonthPo> poList = PageVisitConverter.INSTANCE.pageStatMonthEntityListToPageStatMonthPoList(monthEntityList);
        pageStatMonthMapper.batchInsertPageStatMonth(poList);
    }

    @Override
    public List<PageStatMonthEntity> getPageStatMonthList(PageStatMonthEntity monthEntity) {
        PageStatMonthPo queryPo = PageVisitConverter.INSTANCE.pageStatMonthEntityToPageStatMonthPo(monthEntity);
        List<PageStatMonthPo> resultPos = pageStatMonthMapper.selectPageStatMonthList(queryPo);
        return PageVisitConverter.INSTANCE.pageStatMonthPoListToPageStatMonthEntityList(resultPos);
    }

    @Override
    public void updatePageStatMonthList(List<PageStatMonthEntity> monthEntityList) {
        if (CollectionUtils.isEmpty(monthEntityList)) {
            return;
        }

        List<PageStatMonthPo> poList = PageVisitConverter.INSTANCE.pageStatMonthEntityListToPageStatMonthPoList(monthEntityList);
        pageStatMonthMapper.batchUpdatePageStatMonth(poList);
    }

    @Override
    public void deleteByMonthNumber(Integer monthNumber) {
        pageStatMonthMapper.deleteByMonthNumber(monthNumber);
    }
}

/* Ended by AICoder, pid:357807344c5069514bb10a62d0690a544b8462b4 */