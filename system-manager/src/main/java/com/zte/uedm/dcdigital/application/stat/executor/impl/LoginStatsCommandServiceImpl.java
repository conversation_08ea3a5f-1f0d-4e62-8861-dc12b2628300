package com.zte.uedm.dcdigital.application.stat.executor.impl;

import com.zte.uedm.dcdigital.application.stat.executor.LoginStatsCommandService;
import com.zte.uedm.dcdigital.domain.service.LoginStatsDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginStatQueryDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;

@Service
@Slf4j
public class LoginStatsCommandServiceImpl implements LoginStatsCommandService {
    @Autowired
    private LoginStatsDomainService loginStatsDomainService;
    @Override
    public void export(LoginStatQueryDto queryDto, HttpServletResponse response) {
        loginStatsDomainService.export(queryDto,response);
    }
}
