package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqDayPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqYearPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AssetFaqYearMapper {

    /**
     * 根据产品小类ID和时间范围查询统计记录
     */
    List<AssetFaqYearPo> selectByCategoryAndTimeRange(@Param("productCategoryId") String productCategoryId,
                                                      @Param("startTime") String startTime,
                                                      @Param("endTime") String endTime);

    /**
     * 根据时间范围查询统计记录
     */
    List<AssetFaqYearPo> selectByTimeRange(@Param("startTime") String startTime,
                                           @Param("endTime") String endTime,
                                           @Param("categoryIds") List<String> categoryIds);

    List<AssetFaqYearPo> selectByTime(@Param("yearStr")String yearStr);

    void update(AssetFaqYearPo yearPo);

    void batchInsert(@Param("list") List<AssetFaqYearPo> toInsert);

    List<AssetFaqYearPo> selectDateByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
