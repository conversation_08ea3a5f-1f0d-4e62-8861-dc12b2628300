/* Started by AICoder, pid:4ebe2u3b2bk9f41148c30a4050692d355238528e */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.common.bean.system.RoleVo;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.RoleEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.RolePo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface RolePoConverter {

    // 使用常量来存储实例，确保线程安全和单例模式
    RolePoConverter INSTANCE = Mappers.getMapper(RolePoConverter.class);

    /**
     * 将 RolePo 转换为 RoleEntity。
     *
     * @param rolePo 需要转换的 RolePo 对象
     * @return 转换后的 RoleEntity 对象
     */
    RoleEntity convertPoToEntity(RolePo rolePo);

    List<RoleEntity> convertPosToEntities(List<RolePo> rolePos);

    /**
     * 将 RoleEntity 转换为 RolePo。
     *
     * @param roleEntity 需要转换的 RoleEntity 对象
     * @return 转换后的 RolePo 对象
     */
    RolePo convertEntityToPo(RoleEntity roleEntity);

    /**
     * 将 RoleEntity 转换为 RoleVo。
     *
     * @param roleEntity 需要转换的 RoleEntity 对象
     * @return 转换后的 RoleVo 对象
     */
    RoleVo convertEntityToVo(RoleEntity roleEntity);

    List<RoleVo> convertEntitiesToVos(List<RoleEntity> roleEntities);
}

/* Ended by AICoder, pid:4ebe2u3b2bk9f41148c30a4050692d355238528e */