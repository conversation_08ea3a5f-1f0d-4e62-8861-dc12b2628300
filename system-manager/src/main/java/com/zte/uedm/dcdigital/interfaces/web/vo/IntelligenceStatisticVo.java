package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 智能体统计响应VO
 */
@Getter
@Setter
@ToString
public class IntelligenceStatisticVo {

    /**
     * 总数
     */
    private Long total;

    /**
     * 人均数
     */
    private Long avg;

    /**
     * 智能体数
     */
    private Long intelligenceNum;

    /**
     * 全系统统计
     */
    private List<TotalSystem> totalSystem;

    /**
     * 智能体列表
     */
    private List<IntelligenceDetail> intelligenceList;

    /**
     * 部门统计
     * key=时间点（格式根据timeType：天-yyyy年MM月dd日，周-yyyy年ww周，月-yyyy年MM月，年-yyyy年）
     * value=该时间点各部门的统计数据
     */
    private Map<String, List<DepartmentDetail>> levelDepart;

    /**
     * 用户访问统计
     */
    private List<UserStatDetail> userStatList;

    /**
     * 分页总数
     */
    private Integer totalSize;

    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TotalSystem {
        /**
         * 总数
         */
        private Long total;

        /**
         * 人均数
         */
        private Long avg;

        /**
         * 时间类型
         */
        private String timePeriod;
    }

    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IntelligenceDetail {
        /**
         * 物料智能体使用数
         */
        private Long materialNum;

        /**
         * 标书智能体使用数
         */
        private Long bidNum;

        /**
         * 时间点
         */
        private String timePeriod;
    }

    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DepartmentDetail {
        /**
         * 部门名称
         */
        private String departName;

        /**
         * 人均数
         */
        private Long avgNum;

        /**
         * 总使用数
         */
        private Long totalNum;
    }

    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserStatDetail {
        /**
         * 个人访问次数
         */
        private Long num;

        /**
         * 用户名称
         */
        private String userName;

        /**
         * 时间序列访问数据
         * key=时间（格式根据timeType：天-yyyy年MM月dd日，周-yyyy年ww周，月-yyyy年MM月，年-yyyy年）
         * value=该时间段的访问次数
         */
        private Map<String, Long> timeSeriesData;
    }
}
