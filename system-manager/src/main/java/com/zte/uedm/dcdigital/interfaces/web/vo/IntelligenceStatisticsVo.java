package com.zte.uedm.dcdigital.interfaces.web.vo;
/* Started by AICoder, pid:eab78f0d6fea6071438f0bd99027536a52616d2f */
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 智能体统计vo
 */
@Setter
@Getter
@ToString
public class IntelligenceStatisticsVo {

    /**
     * 应用总数
     */
    private long total = 0;

    /**
     * 应用平均数
     */
    private long avg = 0;

    /**
     * 物料推荐总数
     */
    private long totalMaterial = 0;

    /**
     * 物料推荐平均数
     */
    private long avgMaterial = 0;

    /**
     * 标书分析总数
     */
    private long totalBid = 0;

    /**
     * 标书分析平均数
     */
    private long avgBid = 0;
}
/* Ended by AICoder, pid:eab78f0d6fea6071438f0bd99027536a52616d2f */
