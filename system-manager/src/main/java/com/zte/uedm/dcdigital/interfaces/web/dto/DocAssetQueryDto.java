/* Started by AICoder, pid:02b30v95fbc60c5142970adb300de8543858fb0a */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 文档数量统计业务实体
 */
@Getter
@Setter
@ToString
public class DocAssetQueryDto {
    private Integer timeType; // 1、天 ，2、周 ，3、月，4、年

    /**
     * 小类id
     */
    private String productCategoryId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    private Integer pageNum;
    private Integer pageSize;
}
