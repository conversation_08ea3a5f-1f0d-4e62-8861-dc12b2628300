/* Started by AICoder, pid:d47ccddf5c636ae14c630ba140e70821c403c731 */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class EvalDataAddDto {
    // 功能点ID
    private String functionId;

    /**
     * 操作类型
     * 0: 赞
     * 1: 踩
     */
    private Integer operationType;

    // 评价内容
    private String evalContent;
}

/* Ended by AICoder, pid:d47ccddf5c636ae14c630ba140e70821c403c731 */