/* Started by AICoder, pid:r952bk636ed212214ca50ad55081444b1865c6a9 */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 登录月统计数据持久化对象
 */
@Getter
@Setter
@ToString
@TableName("login_stat_month")
public class LoginStatMonthPo {
    /**
     * 主键ID（UUID格式）
     */
    private String id;

    /**
     * 统计月份（格式：YYYYMM，如202506表示2025年6月）
     */
    @TableField("day")
    private Integer day;

    /**
     * 部门/组织ID
     */
    @TableField("dept_id")
    private String deptId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 本月累计登录天数
     */
    @TableField("num")
    private Integer num;


    // 查询条件字段（非数据库字段）
    @TableField(exist = false)
    private Integer beginTime;

    @TableField(exist = false)
    private Integer endTime;
}

/* Ended by AICoder, pid:r952bk636ed212214ca50ad55081444b1865c6a9 */