package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;

/* Started by AICoder, pid:42bd432f17ke3aa1459a0ae5d0e2b54e80b357cf */
@Getter
@Setter
public class PermissionVo {

    /**
     * ID
     */
    private String id;

    /**
     * 权限名称
     */
    private String name;

    /**
     * 权限类型，0 - 菜单，1 - 操作
     */
    private Integer type;

    /**
     * 操作码
     */
    private String permissionCode;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建用户id
     */
    private String createBy;

    /**
     * 更新用户id
     */
    private String updateBy;
}

/* Ended by AICoder, pid:42bd432f17ke3aa1459a0ae5d0e2b54e80b357cf */
