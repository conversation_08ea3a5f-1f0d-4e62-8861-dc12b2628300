package com.zte.uedm.dcdigital.application.stat.executor.impl;

import com.zte.uedm.dcdigital.application.stat.executor.IntelligenceStatService;
import com.zte.uedm.dcdigital.domain.service.IntelligenceStatDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.IntelligenceStatisticQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.IntelligenceStatisticExportDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.IntelligenceStatisticVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 智能体统计服务实现类
 */
@Service
@Slf4j
public class IntelligenceStatServiceImpl implements IntelligenceStatService {

    @Autowired
    private IntelligenceStatDomainService intelligenceStatDomainService;

    @Override
    public IntelligenceStatisticVo statisticIntelligence(IntelligenceStatisticQueryDto queryDto) {
        log.info("IntelligenceStatService.statisticIntelligence start, queryDto: {}", queryDto);
        
        // 参数验证
        validateIntelligenceStatisticQuery(queryDto);
        
        // 调用智能体统计领域服务
        return intelligenceStatDomainService.queryIntelligenceStatistic(queryDto);
    }

    @Override
    public void statisticIntelligenceExport(IntelligenceStatisticExportDto exportDto) {
        log.info("IntelligenceStatService.statisticIntelligenceExport start, exportDto: {}", exportDto);

        // 参数验证
        validateIntelligenceStatisticExport(exportDto);

        // 调用智能体统计领域服务
        intelligenceStatDomainService.exportIntelligenceStatistic(exportDto);
    }

    @Override
    @Async
    public void intelligenceBuryingPoint(Integer operationType, String userId) {
        log.info("IntelligenceStatService.intelligenceBuryingPoint start, operationType: {}", operationType);

        try {
            // 参数验证
            validateIntelligenceBuryingPoint(operationType);

            // 异步处理埋点数据
            intelligenceStatDomainService.processIntelligenceBuryingPoint(operationType, userId);
        } catch (Exception e) {
            log.error("intelligenceBuryingPoint error", e);
            // 埋点处理失败不应该影响主流程
        }
    }

    @Override
    public void triggerIntelligenceStatAggregation() {
        log.info("Manual trigger intelligence statistics aggregation start");

        try {
            // 调用智能体统计领域服务的汇聚方法
            intelligenceStatDomainService.aggregateIntelligenceStatData();
            log.info("Manual trigger intelligence statistics aggregation completed successfully");
        } catch (Exception e) {
            log.error("Manual trigger intelligence statistics aggregation failed", e);
            throw new RuntimeException("Failed to trigger intelligence statistics aggregation", e);
        }
    }

    private void validateIntelligenceStatisticQuery(IntelligenceStatisticQueryDto queryDto) {
        if (queryDto == null) {
            throw new IllegalArgumentException("查询参数不能为空");
        }
        // TODO: 添加更多参数验证逻辑
    }

    private void validateIntelligenceStatisticExport(IntelligenceStatisticExportDto exportDto) {
        if (exportDto == null) {
            throw new IllegalArgumentException("导出参数不能为空");
        }
        // TODO: 添加更多参数验证逻辑
    }

    private void validateIntelligenceBuryingPoint(Integer operationType) {
        if (operationType == null) {
            throw new IllegalArgumentException("操作类型不能为空");
        }

        if (operationType != 1 && operationType != 2) {
            throw new IllegalArgumentException("操作类型必须是1（物料智能体）或2（标书分析智能体）");
        }
    }
}
