/* Started by AICoder, pid:6eb5fv6b74l7d0f1432f09bf3078634138a387a2 */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.EvalDataHistoryPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface EvalDataHistoryMapper extends BaseMapper<EvalDataHistoryPo> {

    /**
     * 插入评价数据历史记录
     *
     * @param evalDataHistory 评价数据历史记录对象
     * @return 影响行数
     */
    int insertEvalDataHistory(EvalDataHistoryPo evalDataHistory);

    /**
     * 查询评价数据历史记录列表（支持条件查询）
     *
     * @param evalDataHistory 查询条件封装对象
     * @return 符合条件的评价数据历史记录列表
     */
    List<EvalDataHistoryPo> selectEvalDataHistoryList(EvalDataHistoryPo evalDataHistory);

    /**
     * 批量删除评价数据历史记录
     *
     * @param ids 需要删除的ID数组
     * @return 影响行数
     */
    int deleteEvalDataHistoryByIds(String[] ids);

    /**
     * 根据主键ID查询评价数据历史记录
     *
     * @param id 主键ID
     * @return 对应的评价数据历史记录对象
     */
    EvalDataHistoryPo selectEvalDataHistoryById(String id);
}

/* Ended by AICoder, pid:6eb5fv6b74l7d0f1432f09bf3078634138a387a2 */