/* Started by AICoder, pid:ob9f2nee0f1cf2a1445b0b2dd0fab55e66d22fcd */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * UacTokenInfoDto 类表示登录UAC后返回的所有相关信息对象。
 * 这个数据传输对象（DTO）包含了用户认证和授权所需的各种令牌和信息。
 */
@Getter
@Setter
@ToString
public class UacTokenInfoDto {

    /**
     * accessToken: 接口调用凭证，用于API调用的身份验证。
     */
    private String accessToken;

    /**
     * tokenType: 凭证类型，通常为"Bearer"。
     */
    private String tokenType;

    /**
     * expiresIn: 接口调用凭证的超时时间，表示凭证的有效期。
     */
    private Integer expiresIn;

    /**
     * refreshToken: 刷新令牌，用于在访问令牌过期后获取新的访问令牌。
     */
    private String refreshToken;

    /**
     * idToken: 用户身份令牌，包含用户的身份信息。
     */
    private String idToken;

    /**
     * uacToken: 用户中心身份令牌，特定于UAC系统的身份令牌。
     */
    private String uacToken;

    /**
     * scope: 授权范围，定义了访问令牌的权限范围。
     */
    private List<String> scope;
}

/* Ended by AICoder, pid:ob9f2nee0f1cf2a1445b0b2dd0fab55e66d22fcd */