package com.zte.uedm.dcdigital.application.page.executor;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.interfaces.web.dto.PageStatQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.CategoryStatVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.PageStatDeptVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.PageStatVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.UserStatVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface PageStatQueryService {
    PageStatVo queryStat(PageStatQueryDto queryDto);
    PageStatVo queryStatByCategory(PageStatQueryDto queryDto);
    PageVO<UserStatVo> queryUserStat(PageStatQueryDto queryDto);
    PageVO<CategoryStatVo> queryCategoryStat(PageStatQueryDto queryDto);
    void exportUserStat(PageStatQueryDto queryDto, HttpServletResponse response);

    List<PageStatDeptVo> queryPageVisitStatByDept(PageStatQueryDto queryDto);
    List<PageStatDeptVo> queryPageVisitStatByCategory(PageStatQueryDto queryDto);

    void queryLog(String startTime, String endTime);
    void syncData(Integer day);
}
