package com.zte.uedm.dcdigital.application.stat.executor.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.zte.uedm.dcdigital.application.stat.executor.FaqStatService;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.utils.SearchStatTimeUtils;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.AssetFaqDayMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.AssetFaqMonthMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.AssetFaqWeekMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.AssetFaqYearMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqDayPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqMonthPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqWeekPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.AssetFaqYearPo;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.*;
import com.zte.uedm.dcdigital.infrastructure.repository.po.*;
import com.zte.uedm.dcdigital.interfaces.web.dto.AssetStatBuryingPointDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.FaqStatisticDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.FaqStatisticExportDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.FaqStatisticVo;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.time.LocalDate;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FaqStatServiceImpl implements FaqStatService {

    @Autowired
    private ProductService productService;

    @Autowired
    private AssetFaqDayMapper assetFaqDayMapper;

    @Autowired
    private AssetFaqWeekMapper assetFaqWeekMapper;

    @Autowired
    private AssetFaqMonthMapper assetFaqMonthMapper;

    @Autowired
    private AssetFaqYearMapper assetFaqYearMapper;

    @Autowired
    private AssetFaqUpdMapper assetFaqUpdMapper;

    @Autowired
    private FaqMapper faqMapper;

    /* Started by AICoder, pid:s09aaf08cb7acc51455a0bc2901fdf0bd7d2b226 */
    private static final DateTimeFormatter DATE_FORMATTER =
            DateTimeFormatter.ofPattern("yyyyMMdd");
    /* Ended by AICoder, pid:s09aaf08cb7acc51455a0bc2901fdf0bd7d2b226 */


    @Override
    public FaqStatisticVo statisticFaq(FaqStatisticDto faqStatisticDto) {
        // 查询所有产品小类ID
        String cateId = faqStatisticDto.getProductCategoryId();
        List<String> categoryIds = productService.selectByCategoryId(cateId);
        log.info("categoryIds: {}", categoryIds);

        if (categoryIds.isEmpty()) {
            log.warn("categoryIds is empty");
            return new FaqStatisticVo();
        }

        // 返回根据产品小类汇总的数据
        return getFaqStatByCategoryIds(categoryIds, faqStatisticDto);
    }

    private FaqStatisticVo getFaqStatByCategoryIds(List<String> categoryIds, FaqStatisticDto faqStatisticDto) {
        Integer timeType = faqStatisticDto.getTimeType();
        String startTime = faqStatisticDto.getStartTime();
        String endTime = faqStatisticDto.getEndTime();

        FaqStatisticVo faqStatisticVo = new FaqStatisticVo();
        // 1、天 ，2、周 ，3、月，4、年
        switch (timeType) {
            case 1:
                faqStatisticVo = getFaqStatDayData(startTime, endTime, categoryIds);
                break;
            case 2:
                faqStatisticVo = getFaqStatWeekData(startTime, endTime, categoryIds);
                break;

            case 3:
                faqStatisticVo = getFaqStatMonthData(startTime, endTime, categoryIds);
                break;

            case 4:
                faqStatisticVo = getFaqStatYearData(startTime, endTime, categoryIds);
                break;

            default:
                log.error("timeType = {}, invalid", timeType);
                break;
        }

        return faqStatisticVo;
    }

    private FaqStatisticVo getFaqStatYearData(String startTime, String endTime, List<String> categoryIds) {
        List<AssetFaqYearPo> poList = assetFaqYearMapper.selectByTimeRange(startTime, endTime, categoryIds);
        if (poList.isEmpty()) {
            log.error("selectByTimeRange {} and {} no data", startTime, endTime);
            return new FaqStatisticVo();
        }

        for (AssetFaqYearPo po: poList) {
            po.setDay(SearchStatTimeUtils.formatTimeDisplay(po.getDay(), 4));
        }

        // 生成完整的时间范围
        List<String> completeTimeRange = SearchStatTimeUtils.generateCompleteTimeRange(startTime, endTime, 4);
        log.info("Generated complete time range for total system: {}", completeTimeRange);

        List<FaqStatisticVo.FaqStatDataVo> faqStatDataVos = initFaqStatData(completeTimeRange);
        // 补全空缺数据
        for (FaqStatisticVo.FaqStatDataVo data: faqStatDataVos) {
            for (AssetFaqYearPo po: poList) {
                if (data.getDay().equals(po.getDay())) {
                    data.setAllNum(data.getAllNum() + Long.parseLong(po.getAllNum()));
                    data.setUpdateNum(data.getUpdateNum() + Long.parseLong(po.getUpdateNum()));
                    data.setChangeNum(data.getChangeNum() + Long.parseLong(po.getChangeNum()));
                }
            }
        }

        // 获取分类名称映射
        Map<String, String> coteInfoMap = productService.selectProductCategoryList(categoryIds)
                .stream().
                collect(Collectors.toMap(ProductCategoryInfoVo::getId, ProductCategoryInfoVo::getProductName));

        // 计算下级统计信息
        Map<String, List<AssetFaqYearPo>> cateMap = poList.stream()
                .collect(Collectors.groupingBy(AssetFaqYearPo::getProductCategoryId));
        log.info("cateMap size: {}", cateMap.size());
        List<FaqStatisticVo.FaqStatJuniorVo> faqStatJuniorVos = cateMap.entrySet().stream()
                .map(entry -> {
                    String name = coteInfoMap.get(entry.getKey());
                    long allNum = 0L;
                    long updateNum = 0L;
                    long changeNum = 0L;
                    for (AssetFaqYearPo po: entry.getValue())
                    {
                        if (po.getDay().equals(completeTimeRange.get(completeTimeRange.size()-1))) {
                            allNum = Long.parseLong(po.getAllNum());
                            updateNum = Long.parseLong(po.getUpdateNum());
                            changeNum = Long.parseLong(po.getChangeNum());
                        }
                    }

                    return new FaqStatisticVo.FaqStatJuniorVo(name, allNum, updateNum, changeNum);
                })
                .sorted(Comparator.comparingLong(FaqStatisticVo.FaqStatJuniorVo::getAllNum).reversed()
                        .thenComparing(Comparator.comparingLong(FaqStatisticVo.FaqStatJuniorVo::getUpdateNum).reversed())
                        .thenComparing(Comparator.comparingLong(FaqStatisticVo.FaqStatJuniorVo::getChangeNum).reversed()))
                .collect(Collectors.toList());

        FaqStatisticVo faqStatisticVo = getFaqJoniorTotal(faqStatDataVos, SearchStatTimeUtils.formatTimeDisplay(endTime, 4));
        faqStatisticVo.getDataList().addAll(faqStatDataVos);
        faqStatisticVo.getJuniorList().addAll(faqStatJuniorVos);
        return faqStatisticVo;
    }

    private FaqStatisticVo getFaqStatMonthData(String startTime, String endTime, List<String> categoryIds) {
        List<AssetFaqMonthPo> poList = assetFaqMonthMapper.selectByTimeRange(startTime, endTime, categoryIds);
        if (poList.isEmpty()) {
            log.error("selectByTimeRange {} and {} no data", startTime, endTime);
            return new FaqStatisticVo();
        }

        for (AssetFaqMonthPo po: poList) {
            po.setDay(SearchStatTimeUtils.formatTimeDisplay(po.getDay(), 3));
        }

        // 生成完整的时间范围
        List<String> completeTimeRange = SearchStatTimeUtils.generateCompleteTimeRange(startTime, endTime, 3);
        log.info("Generated complete time range for total system: {}", completeTimeRange);

        List<FaqStatisticVo.FaqStatDataVo> faqStatDataVos = initFaqStatData(completeTimeRange);
        // 补全空缺数据
        for (FaqStatisticVo.FaqStatDataVo data: faqStatDataVos) {
            for (AssetFaqMonthPo po: poList) {
                if (data.getDay().equals(po.getDay())) {
                    data.setAllNum(data.getAllNum() + Long.parseLong(po.getAllNum()));
                    data.setUpdateNum(data.getUpdateNum() + Long.parseLong(po.getUpdateNum()));
                    data.setChangeNum(data.getChangeNum() + Long.parseLong(po.getChangeNum()));
                }
            }
        }

        // 获取分类名称映射
        Map<String, String> coteInfoMap = productService.selectProductCategoryList(categoryIds)
                .stream().
                collect(Collectors.toMap(ProductCategoryInfoVo::getId, ProductCategoryInfoVo::getProductName));

        // 计算下级统计信息
        Map<String, List<AssetFaqMonthPo>> cateMap = poList.stream()
                .collect(Collectors.groupingBy(AssetFaqMonthPo::getProductCategoryId));
        log.info("cateMap size: {}", cateMap.size());
        List<FaqStatisticVo.FaqStatJuniorVo> faqStatJuniorVos = cateMap.entrySet().stream()
                .map(entry -> {
                    String name = coteInfoMap.get(entry.getKey());
                    long allNum = 0L;
                    long updateNum = 0L;
                    long changeNum = 0L;
                    for (AssetFaqMonthPo po: entry.getValue())
                    {
                        if (po.getDay().equals(completeTimeRange.get(completeTimeRange.size()-1))) {
                            allNum = Long.parseLong(po.getAllNum());
                            updateNum = Long.parseLong(po.getUpdateNum());
                            changeNum = Long.parseLong(po.getChangeNum());
                        }
                    }

                    return new FaqStatisticVo.FaqStatJuniorVo(name, allNum, updateNum, changeNum);
                })
                .sorted(Comparator.comparingLong(FaqStatisticVo.FaqStatJuniorVo::getAllNum).reversed()
                        .thenComparing(Comparator.comparingLong(FaqStatisticVo.FaqStatJuniorVo::getUpdateNum).reversed())
                        .thenComparing(Comparator.comparingLong(FaqStatisticVo.FaqStatJuniorVo::getChangeNum).reversed()))
                .collect(Collectors.toList());

        FaqStatisticVo faqStatisticVo = getFaqJoniorTotal(faqStatDataVos, SearchStatTimeUtils.formatTimeDisplay(endTime, 3));
        faqStatisticVo.getDataList().addAll(faqStatDataVos);
        faqStatisticVo.getJuniorList().addAll(faqStatJuniorVos);
        return faqStatisticVo;
    }


    private FaqStatisticVo getFaqStatWeekData(String startTime, String endTime, List<String> categoryIds) {
        List<AssetFaqWeekPo> poList = assetFaqWeekMapper.selectByTimeRange(startTime, endTime, categoryIds);
        if (poList.isEmpty()) {
            log.error("selectByTimeRange {} and {} no data", startTime, endTime);
            return new FaqStatisticVo();
        }

        for (AssetFaqWeekPo po: poList) {
            po.setDay(SearchStatTimeUtils.formatTimeDisplay(po.getDay(), 2));
        }

        // 生成完整的时间范围
        List<String> completeTimeRange = SearchStatTimeUtils.generateCompleteTimeRange(startTime, endTime, 2);
        log.info("Generated complete time range for total system: {}", completeTimeRange);

        List<FaqStatisticVo.FaqStatDataVo> faqStatDataVos = initFaqStatData(completeTimeRange);
        // 补全空缺数据
        for (FaqStatisticVo.FaqStatDataVo data: faqStatDataVos) {
            for (AssetFaqWeekPo po: poList) {
                if (data.getDay().equals(po.getDay())) {
                    data.setAllNum(data.getAllNum() + Long.parseLong(po.getAllNum()));
                    data.setUpdateNum(data.getUpdateNum() + Long.parseLong(po.getUpdateNum()));
                    data.setChangeNum(data.getChangeNum() + Long.parseLong(po.getChangeNum()));
                }
            }
        }

        // 获取分类名称映射
        Map<String, String> coteInfoMap = productService.selectProductCategoryList(categoryIds)
                .stream().
                collect(Collectors.toMap(ProductCategoryInfoVo::getId, ProductCategoryInfoVo::getProductName));

        // 计算下级统计信息
        Map<String, List<AssetFaqWeekPo>> cateMap = poList.stream()
                .collect(Collectors.groupingBy(AssetFaqWeekPo::getProductCategoryId));
        log.info("cateMap size: {}", cateMap.size());
        List<FaqStatisticVo.FaqStatJuniorVo> faqStatJuniorVos = cateMap.entrySet().stream()
                .map(entry -> {
                    String name = coteInfoMap.get(entry.getKey());
                    long allNum = 0L;
                    long updateNum = 0L;
                    long changeNum = 0L;
                    for (AssetFaqWeekPo po: entry.getValue())
                    {
                        if (po.getDay().equals(completeTimeRange.get(completeTimeRange.size()-1))) {
                            allNum = Long.parseLong(po.getAllNum());
                            updateNum = Long.parseLong(po.getUpdateNum());
                            changeNum = Long.parseLong(po.getChangeNum());
                        }
                    }

                    return new FaqStatisticVo.FaqStatJuniorVo(name, allNum, updateNum, changeNum);
                })
                .sorted(Comparator.comparingLong(FaqStatisticVo.FaqStatJuniorVo::getAllNum).reversed()
                        .thenComparing(Comparator.comparingLong(FaqStatisticVo.FaqStatJuniorVo::getUpdateNum).reversed())
                        .thenComparing(Comparator.comparingLong(FaqStatisticVo.FaqStatJuniorVo::getChangeNum).reversed()))
                .collect(Collectors.toList());

        FaqStatisticVo faqStatisticVo = getFaqJoniorTotal(faqStatDataVos, SearchStatTimeUtils.formatTimeDisplay(endTime, 2));
        faqStatisticVo.getDataList().addAll(faqStatDataVos);
        faqStatisticVo.getJuniorList().addAll(faqStatJuniorVos);
        return faqStatisticVo;
    }

    private FaqStatisticVo getFaqStatDayData(String startTime, String endTime, List<String> categoryIds) {
        List<AssetFaqDayPo> poList = assetFaqDayMapper.selectByTimeRange(startTime, endTime, categoryIds);
        if (poList.isEmpty()) {
            log.error("selectByTimeRange {} and {} no data", startTime, endTime);
            return new FaqStatisticVo();
        }

        for (AssetFaqDayPo po: poList) {
            po.setDay(SearchStatTimeUtils.formatTimeDisplay(po.getDay(), 1));
        }

        // 生成完整的时间范围
        List<String> completeTimeRange = SearchStatTimeUtils.generateCompleteTimeRange(startTime, endTime, 1);
        log.info("Generated complete time range for total system: {}", completeTimeRange);

        List<FaqStatisticVo.FaqStatDataVo> faqStatDataVos = initFaqStatData(completeTimeRange);
        // 补全空缺数据
        for (FaqStatisticVo.FaqStatDataVo data: faqStatDataVos) {
            for (AssetFaqDayPo po: poList) {
                if (data.getDay().equals(po.getDay())) {
                    data.setAllNum(data.getAllNum() + Long.parseLong(po.getAllNum()));
                    data.setUpdateNum(data.getUpdateNum() + Long.parseLong(po.getUpdateNum()));
                    data.setChangeNum(data.getChangeNum() + Long.parseLong(po.getChangeNum()));
                }
            }
        }

        // 获取分类名称映射
        Map<String, String> coteInfoMap = productService.selectProductCategoryList(categoryIds)
                .stream().
                collect(Collectors.toMap(ProductCategoryInfoVo::getId, ProductCategoryInfoVo::getProductName));

        // 计算下级统计信息
        Map<String, List<AssetFaqDayPo>> cateMap = poList.stream()
                .collect(Collectors.groupingBy(AssetFaqDayPo::getProductCategoryId));
        log.info("cateMap size: {}", cateMap.size());
        List<FaqStatisticVo.FaqStatJuniorVo> faqStatJuniorVos = cateMap.entrySet().stream()
                .map(entry -> {
                    String name = coteInfoMap.get(entry.getKey());
                    long allNum = 0L;
                    long updateNum = 0L;
                    long changeNum = 0L;
                    for (AssetFaqDayPo po: entry.getValue())
                    {
                        if (po.getDay().equals(completeTimeRange.get(completeTimeRange.size()-1))) {
                            allNum = Long.parseLong(po.getAllNum());
                            updateNum = Long.parseLong(po.getUpdateNum());
                            changeNum = Long.parseLong(po.getChangeNum());
                        }
                    }

                    return new FaqStatisticVo.FaqStatJuniorVo(name, allNum, updateNum, changeNum);
                })
                .sorted(Comparator.comparingLong(FaqStatisticVo.FaqStatJuniorVo::getAllNum).reversed()
                        .thenComparing(Comparator.comparingLong(FaqStatisticVo.FaqStatJuniorVo::getUpdateNum).reversed())
                        .thenComparing(Comparator.comparingLong(FaqStatisticVo.FaqStatJuniorVo::getChangeNum).reversed()))
                .collect(Collectors.toList());

        FaqStatisticVo faqStatisticVo = getFaqJoniorTotal(faqStatDataVos, SearchStatTimeUtils.formatTimeDisplay(endTime, 1));
        faqStatisticVo.getDataList().addAll(faqStatDataVos);
        faqStatisticVo.getJuniorList().addAll(faqStatJuniorVos);
        return faqStatisticVo;
    }

    private List<FaqStatisticVo.FaqStatDataVo> initFaqStatData(List<String> timeRange) {
        List<FaqStatisticVo.FaqStatDataVo> poList = new ArrayList<>();
        for (String time: timeRange)
        {
            poList.add(new FaqStatisticVo.FaqStatDataVo(time, 0, 0, 0));
        }

        return poList;
    }

    /* Started by AICoder, pid:2c4e17808a782e61483e092610e76a140f4375db */
    private FaqStatisticVo getFaqJoniorTotal(List<FaqStatisticVo.FaqStatDataVo> faqStatDataVos, String endTime) {
        FaqStatisticVo faqStatisticVo = new FaqStatisticVo();
        faqStatisticVo.setAllNum(0);
        faqStatisticVo.setUpdateNum(0);
        faqStatisticVo.setChangeNum(0);
        for (FaqStatisticVo.FaqStatDataVo vo: faqStatDataVos) {
            if (vo.getDay().equals(endTime)) {
                faqStatisticVo.setAllNum(vo.getAllNum());
                faqStatisticVo.setUpdateNum(vo.getUpdateNum());
                faqStatisticVo.setChangeNum(vo.getChangeNum());
            }
        }

        return faqStatisticVo;
    }
    /* Ended by AICoder, pid:2c4e17808a782e61483e092610e76a140f4375db */

    /* Started by AICoder, pid:u092ck72b2oea5414c9a0a1080f78f47c2289878 */
    @Override
    public void statisticFaqExport(FaqStatisticExportDto exportDto, HttpServletResponse response) {
        log.info("statisticFaqExport start, exportDto: {}", exportDto);

        // 验证参数
        checkExportParam(exportDto);

        // 获取导出数据
        List<FaqStatisticVo.FaqStatJuniorVo> exportData = getFaqExportData(exportDto);

        // 导出数据
        exportFaqStatistic(exportData, response);

    }
    /* Ended by AICoder, pid:u092ck72b2oea5414c9a0a1080f78f47c2289878 */

    /* Started by AICoder, pid:e5b595d470fb7831472d0a00e05b191a9133c123 */
    private List<FaqStatisticVo.FaqStatJuniorVo> getFaqExportData(FaqStatisticExportDto exportDto) {
        // 查询所有产品小类ID
        String cateId = exportDto.getProductCategoryId();
        List<String> categoryIds = productService.selectByCategoryId(cateId);
        log.info("categoryIds: {}", categoryIds);

        // 返回根据产品小类汇总的数据
        return getFaqStatExportData(categoryIds, exportDto).getJuniorList();
    }
    /* Ended by AICoder, pid:e5b595d470fb7831472d0a00e05b191a9133c123 */

    /* Started by AICoder, pid:w79be24be3780b214ef60b85f0d822473d248ad0 */
    private FaqStatisticVo getFaqStatExportData(List<String> categoryIds, FaqStatisticExportDto exportDto) {
        Integer timeType = exportDto.getTimeType();
        String startTime = exportDto.getStartTime();
        String endTime = exportDto.getEndTime();

        FaqStatisticVo faqStatisticVo = new FaqStatisticVo();
        // 1、天 ，2、周 ，3、月，4、年
        switch (timeType) {
            case 1:
                faqStatisticVo = getFaqStatDayData(startTime, endTime, categoryIds);
                break;
            case 2:
                faqStatisticVo = getFaqStatWeekData(startTime, endTime, categoryIds);
                break;

            case 3:
                faqStatisticVo = getFaqStatMonthData(startTime, endTime, categoryIds);
                break;

            case 4:
                faqStatisticVo = getFaqStatYearData(startTime, endTime, categoryIds);
                break;

            default:
                log.error("timeType = {}, invalid", timeType);
                break;
        }

        return faqStatisticVo;
    }
    /* Ended by AICoder, pid:w79be24be3780b214ef60b85f0d822473d248ad0 */

    /* Started by AICoder, pid:a3a9bb6245sc83e14ce20add5087af3f82d339d7 */
    private void exportFaqStatistic(List<FaqStatisticVo.FaqStatJuniorVo> fileList, HttpServletResponse response) {
        log.info("exportFaqStatistic start");
        try {
            try {
                String fileNameStr = "FAQ统计_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xls";
                String encodedFileName = URLEncoder.encode(new String(fileNameStr.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8.toString());
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);
            } catch (Exception e) {
                log.error("set response header error", e);
                throw new BusinessException(StatusCode.SYSTEM_ERROR);
            }
            try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), FaqStatisticVo.FaqStatJuniorVo.class)
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(15))
                    .excelType(ExcelTypeEnum.XLS)
                    .charset(StandardCharsets.UTF_8)
                    .autoCloseStream(Boolean.TRUE).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet("FAQ统计").build();
                excelWriter.write(fileList, writeSheet);
            }
        } catch (IOException e) {
            log.error("export productCoreParamList data is failed", e);
            throw new BusinessException(StatusCode.FAILED);
        }
    }
    /* Ended by AICoder, pid:a3a9bb6245sc83e14ce20add5087af3f82d339d7 */

    /* Started by AICoder, pid:dac8ax8a0bg8b58141300a91a030a72d24b1a586 */
    private void checkExportParam(FaqStatisticExportDto exportDto) {

        // 非空校验
        if (exportDto == null) {
            throw new IllegalArgumentException("Export parameters cannot be null");
        }

        if (StringUtils.isBlank(exportDto.getProductCategoryId())) {
            throw new IllegalArgumentException("ProductCategoryId cannot be null");
        }

        if (StringUtils.isBlank(exportDto.getStartTime())) {
            throw new IllegalArgumentException("StartTime cannot be null");
        }

        if (StringUtils.isBlank(exportDto.getEndTime())) {
            throw new IllegalArgumentException("EndTime cannot be null");
        }

        // 数据校验
        checkTimeType(exportDto.getTimeType());
    }
    /* Ended by AICoder, pid:dac8ax8a0bg8b58141300a91a030a72d24b1a586 */

    /* Started by AICoder, pid:d82ca77a77sf22a1439a0a23506f40028f567776 */
    private void checkTimeType(Integer timeType) {
        if (timeType == null) {
            throw new IllegalArgumentException("Time type cannot be null");
        }

        if (timeType < 1 || timeType > 4) {
            throw new IllegalArgumentException("Time type must be 1-4 (1:day, 2:week, 3:month, 4:year)");
        }
    }
    /* Ended by AICoder, pid:d82ca77a77sf22a1439a0a23506f40028f567776 */

    @Override
    public synchronized void updateAssetFaqDay(String productCategoryId, int num) {
        String dateStr = LocalDate.now().format(DATE_FORMATTER);
        AssetFaqDayPo faqDayPo = assetFaqDayMapper.selectByTimeAndProductCategoryId(dateStr, productCategoryId);
        if (faqDayPo != null){
            faqDayPo.setAllNum(String.valueOf((Long.parseLong(faqDayPo.getAllNum()) + num)));
            faqDayPo.setChangeNum(String.valueOf((Long.parseLong(faqDayPo.getChangeNum()) + num)));
            assetFaqDayMapper.update(faqDayPo);
        }else {
            int allNum = faqMapper.selectCountByResourceId(productCategoryId);
            faqDayPo = new AssetFaqDayPo();
            faqDayPo.setId(UUID.randomUUID().toString());
            faqDayPo.setDay(dateStr);
            faqDayPo.setProductCategoryId(productCategoryId);
            faqDayPo.setAllNum(String.valueOf(allNum));
            faqDayPo.setUpdateNum("0");
            faqDayPo.setChangeNum(String.valueOf(num));
            faqDayPo.setCreateTime(DateTimeUtils.getCurrentTime());
            assetFaqDayMapper.insert(faqDayPo);
        }
    }

    @Override
    public void buryingPoint(AssetStatBuryingPointDto dto) {
        String faqId = dto.getResourceId();
        String productCategoryId = dto.getTypeId();
        if (StringUtils.isBlank(productCategoryId)){
            log.error("productCategoryId error:{}", productCategoryId);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
        LocalDate today = LocalDate.now();
        String todayStr = today.format(DATE_FORMATTER);
        int count = assetFaqUpdMapper.selectUpdateNum(faqId, todayStr);
        AssetFaqUpdPo updPo =  new AssetFaqUpdPo();
        updPo.setId(UUID.randomUUID().toString());
        updPo.setProductCategoryId(productCategoryId);
        updPo.setFaqId(faqId);
        updPo.setDay(todayStr);
        updPo.setCreateTime(DateTimeUtils.getCurrentTime());
        assetFaqUpdMapper.insert(updPo);
        //大于1说明之前已经记录了该faq当天更新过，不能再记录
        if (count > 0){
            return;
        }
        AssetFaqDayPo faqDayPo = assetFaqDayMapper.selectByTimeAndProductCategoryId(todayStr, productCategoryId);
        if (faqDayPo != null){
            faqDayPo.setUpdateNum(String.valueOf(Long.parseLong(faqDayPo.getUpdateNum()) + 1));
            assetFaqDayMapper.update(faqDayPo);
        }else {
            int allNum = faqMapper.selectCountByResourceId(productCategoryId);
            faqDayPo = new AssetFaqDayPo();
            faqDayPo.setId(UUID.randomUUID().toString());
            faqDayPo.setDay(todayStr);
            faqDayPo.setProductCategoryId(productCategoryId);
            faqDayPo.setAllNum(String.valueOf(allNum));
            faqDayPo.setUpdateNum("1");
            faqDayPo.setChangeNum("0");
            faqDayPo.setCreateTime(DateTimeUtils.getCurrentTime());
            assetFaqDayMapper.insert(faqDayPo);
        }

    }

    /* Started by AICoder, pid:i82f7c98147c1ba1417e08b611c3af42fba969c6 */
    @Override
    public void timedStat() {
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);

        aggregateToDayTable();
        // 汇聚到周表
        aggregateToWeekTable(yesterday);

        // 汇聚到月表
        aggregateToMonthTable(yesterday);

        // 汇聚到年表
        aggregateToYearTable(yesterday);
    }

    /* Started by AICoder, pid:yfb68ee3c3i5037140fa0ac890d1394ee142f409 */
    private void aggregateToDayTable() {
        // 获取当前日期（使用方法变量确保一致性）
        String today = LocalDate.now().format(DATE_FORMATTER);
        String yesterday = LocalDate.now().minusDays(1).format(DATE_FORMATTER);

        // 并行获取产品分类ID和现有记录ID（根据实际数据量决定是否启用并行）
        List<String> allProductCategoryIds = productService.getProductSubcategoryId();
        Set<String> existingIds = new HashSet<>(assetFaqDayMapper.selectByDate(today));

        // 使用Set进行差集计算提升性能
        Set<String> needInsertIds = allProductCategoryIds.stream()
                .filter(p -> !existingIds.contains(p))
                .collect(Collectors.toSet());

        if (needInsertIds.isEmpty()) {
            return; // 提前终止无操作的情况
        }

        // 并行查询FAQ数量（适合大数据量场景）
        Map<String, Integer> countMap = allProductCategoryIds.parallelStream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        productCategoryId -> faqMapper.selectCountByResourceId(productCategoryId)
                ));
        //前一天的数据用于计算更新数
        List<AssetFaqDayPo> yesterdayData = assetFaqDayMapper.selectByTime(yesterday);
        Map<String, String> dataMap = yesterdayData.stream().collect(Collectors.toMap(AssetFaqDayPo::getProductCategoryId, AssetFaqDayPo::getAllNum));
        // 批量构建PO对象
        List<AssetFaqDayPo> poList = needInsertIds.stream()
                .map(productCategoryId -> {
                    int count = assetFaqUpdMapper.selectCount(productCategoryId, today);
                    AssetFaqDayPo dayPo = new AssetFaqDayPo();
                    dayPo.setId(UUID.randomUUID().toString());
                    dayPo.setDay(today);
                    dayPo.setProductCategoryId(productCategoryId);
                    Integer allNum = countMap.get(productCategoryId);
                    dayPo.setAllNum(String.valueOf(allNum));
                    dayPo.setUpdateNum(String.valueOf(count));
                    String num = dataMap.get(productCategoryId);
                    if (num != null){
                        dayPo.setChangeNum(String.valueOf(allNum - Integer.parseInt(num)));
                    }else {
                        dayPo.setChangeNum(String.valueOf(allNum));
                    }
                    dayPo.setCreateTime(DateTimeUtils.getCurrentTime());
                    return dayPo;
                })
                .collect(Collectors.toList());

        // 执行批量插入
        assetFaqDayMapper.batchInsert(poList);
    }
    /* Ended by AICoder, pid:yfb68ee3c3i5037140fa0ac890d1394ee142f409 */

    private void aggregateToWeekTable(LocalDate date) {

        try {
            int year = date.getYear();
            int weekOfYear = date.get(WeekFields.of(Locale.getDefault()).weekOfYear());
            String weekStr = String.format("%04d%02d", year, weekOfYear);

            log.info("Aggregating to week table for week: {}", weekStr);

            // 计算该周的开始和结束日期
            LocalDate startOfWeek = date.with(WeekFields.of(Locale.getDefault()).dayOfWeek(), 1);
            LocalDate endOfWeek = startOfWeek.plusDays(6);

            String startDayStr = startOfWeek.format(DATE_FORMATTER);
            String endDayStr = endOfWeek.format(DATE_FORMATTER);

            //各产品小类的更新数据
            List<AssetFaqDayPo> statFaqObjList = assetFaqDayMapper.aggregateByDateRange(startDayStr, endDayStr);

            List<AssetFaqWeekPo> weekPoList = assetFaqWeekMapper.selectByTime(weekStr);
            Map<String, AssetFaqWeekPo> weekPoMap = weekPoList.stream().collect(Collectors.toMap(AssetFaqWeekPo::getProductCategoryId, Function.identity()));
            List<AssetFaqWeekPo> toInsert = new ArrayList<>();
            for (AssetFaqDayPo po : statFaqObjList) {
                processWeekAggregation(po, weekStr, weekPoMap, toInsert);
            }
            if (!toInsert.isEmpty()) {
                assetFaqWeekMapper.batchInsert(toInsert);
            }
        } catch (Exception e) {
            log.error("Job [aggregateToWeekTable] failed.", e);
        }
    }

    private void processWeekAggregation(AssetFaqDayPo po, String weekStr, Map<String, AssetFaqWeekPo> weekPoMap,
                                         List<AssetFaqWeekPo> toInsert) {
        AssetFaqWeekPo faqWeekPo = weekPoMap.get(po.getProductCategoryId());
        if (faqWeekPo != null){
            updateWeekPo(po, faqWeekPo);
        }else {
            toInsert.add(createWeekPo(po, weekStr));
        }
    }

    private AssetFaqWeekPo createWeekPo(AssetFaqDayPo po, String weekStr) {
        AssetFaqWeekPo faqWeekPo = new AssetFaqWeekPo();
        faqWeekPo.setId(UUID.randomUUID().toString());
        faqWeekPo.setDay(weekStr);
        faqWeekPo.setAllNum(po.getAllNum());
        faqWeekPo.setUpdateNum(po.getUpdateNum() == null ? "0" : po.getUpdateNum());
        faqWeekPo.setChangeNum(po.getChangeNum() == null ? "0" : po.getChangeNum());
        faqWeekPo.setProductCategoryId(po.getProductCategoryId());
        faqWeekPo.setCreateTime(DateTimeUtils.getCurrentTime());
        return faqWeekPo;
    }

    private void updateWeekPo(AssetFaqDayPo po, AssetFaqWeekPo faqWeekPo) {
        faqWeekPo.setAllNum(po.getAllNum());
        faqWeekPo.setUpdateNum(po.getUpdateNum() == null ? "0" : po.getUpdateNum());
        faqWeekPo.setChangeNum(po.getChangeNum() == null ? "0" : po.getChangeNum());
        assetFaqWeekMapper.update(faqWeekPo);
    }
    /* Ended by AICoder, pid:i82f7c98147c1ba1417e08b611c3af42fba969c6 */

    /* Started by AICoder, pid:11fa7ndd78g2c3114c5b0a4260f2782bcf890cfe */
    private void aggregateToMonthTable(LocalDate date) {
        try {
            // 计算月份标识（格式：YYYYMM，如202506表示2025年6月）
            String monthStr = date.format(DateTimeFormatter.ofPattern("yyyyMM"));

            log.info("Aggregating to month table for month: {}", monthStr);

            // 计算该月的开始和结束日期
            LocalDate startOfMonth = date.withDayOfMonth(1);
            LocalDate endOfMonth = date.withDayOfMonth(date.lengthOfMonth());

            String startDayStr = startOfMonth.format(DATE_FORMATTER);
            String endDayStr = endOfMonth.format(DATE_FORMATTER);

            List<AssetFaqDayPo> list = assetFaqDayMapper.aggregateByDateRange(startDayStr, endDayStr);

            List<AssetFaqMonthPo> monthPoList =  assetFaqMonthMapper.selectByTime(monthStr);
            Map<String, AssetFaqMonthPo> monthPoMap = monthPoList.stream().collect(Collectors
                    .toMap(AssetFaqMonthPo::getProductCategoryId, Function.identity()));

            List<AssetFaqMonthPo> toInsert = new ArrayList<>();
            // 处理汇聚数据
            for (AssetFaqDayPo data : list) {
                processMonthAggregation(data, monthStr, monthPoMap, toInsert);
            }
            // 执行批量操作
            if (!toInsert.isEmpty()) {
                assetFaqMonthMapper.batchInsert(toInsert);
            }
            log.info("Month table aggregation completed for month: {}", monthStr);
        } catch (Exception e) {
            log.error("Job [aggregateToMonthTable] failed.", e);
        }
    }
    /* Ended by AICoder, pid:11fa7ndd78g2c3114c5b0a4260f2782bcf890cfe */

    /* Started by AICoder, pid:f276522040we4e51411a0bdf90199934d56124c6 */
    private void processMonthAggregation(AssetFaqDayPo data, String monthStr, Map<String, AssetFaqMonthPo> monthPoMap,
                                           List<AssetFaqMonthPo> toInsert) {
        AssetFaqMonthPo monthPo = monthPoMap.get(data.getProductCategoryId());
        if (monthPo != null){
            updateMonthPo(data, monthPo);
        }else {
            toInsert.add(createMonthPo(data, monthStr));
        }
    }

    private AssetFaqMonthPo createMonthPo(AssetFaqDayPo data, String monthStr) {
        AssetFaqMonthPo monthPo = new AssetFaqMonthPo();
        monthPo.setId(UUID.randomUUID().toString());
        monthPo.setProductCategoryId(data.getProductCategoryId());
        monthPo.setDay(monthStr);
        monthPo.setAllNum(data.getAllNum());
        monthPo.setUpdateNum(data.getUpdateNum() == null ? "0" : data.getUpdateNum());
        monthPo.setChangeNum(data.getChangeNum() == null ? "0" : data.getChangeNum());
        monthPo.setCreateTime(DateTimeUtils.getCurrentTime());
        return monthPo;
    }

    private void updateMonthPo(AssetFaqDayPo data, AssetFaqMonthPo monthPo) {
        monthPo.setAllNum(data.getAllNum());
        monthPo.setUpdateNum(data.getUpdateNum() == null ? "0" : data.getUpdateNum());
        monthPo.setChangeNum(data.getChangeNum() == null ? "0" : data.getChangeNum());
        assetFaqMonthMapper.update(monthPo);
    }
    /* Ended by AICoder, pid:f276522040we4e51411a0bdf90199934d56124c6 */

    /* Started by AICoder, pid:109aa508cb4acc51455a0bc2901fdf7bd7d7b226 */

    private void aggregateToYearTable(LocalDate date) {
        try {
            String yearStr = String.valueOf(date.getYear());
            LocalDate startOfYear = date.withDayOfYear(1);
            LocalDate endOfYear = date.withDayOfYear(date.lengthOfYear());

            // 使用预定义的日期格式器
            String startDayStr = startOfYear.format(DATE_FORMATTER);
            String endDayStr = endOfYear.format(DATE_FORMATTER);

            List<AssetFaqDayPo> dailyDataList = assetFaqDayMapper.aggregateByDateRange(startDayStr, endDayStr);

            if (!dailyDataList.isEmpty()) {
                List<AssetFaqYearPo> existingYears = assetFaqYearMapper.selectByTime(yearStr);
                Map<String, AssetFaqYearPo> yearPoMap = existingYears.stream()
                        .collect(Collectors.toMap(AssetFaqYearPo::getProductCategoryId, Function.identity()));

                // 分离需要更新和插入的数据
                List<AssetFaqYearPo> toInsert = new ArrayList<>();
                for (AssetFaqDayPo data : dailyDataList) {
                    processYearAggregation(data, yearStr, yearPoMap, toInsert);
                }

                if (!toInsert.isEmpty()) {
                    assetFaqYearMapper.batchInsert(toInsert);
                }
            }
        } catch (Exception e) {
            log.error("aggregateToYearTable error", e);
        }
    }

    private void processYearAggregation(AssetFaqDayPo data, String yearStr,
                                        Map<String, AssetFaqYearPo> yearPoMap,
                                        List<AssetFaqYearPo> toInsert) {
        AssetFaqYearPo yearPo = yearPoMap.get(data.getProductCategoryId());

        if (yearPo != null) {
            updateYearPo(data, yearPo);
        } else {
            AssetFaqYearPo newYearPo = createNewYearPo(data, yearStr);
            toInsert.add(newYearPo);
        }
    }

    private AssetFaqYearPo createNewYearPo(AssetFaqDayPo data, String yearStr) {
        AssetFaqYearPo yearPo = new AssetFaqYearPo();
        yearPo.setId(UUID.randomUUID().toString());
        yearPo.setProductCategoryId(data.getProductCategoryId());
        yearPo.setDay(yearStr);
        yearPo.setAllNum(data.getAllNum());
        yearPo.setUpdateNum(data.getUpdateNum() == null ? "0" : data.getUpdateNum());
        yearPo.setChangeNum(data.getChangeNum() == null ? "0" : data.getChangeNum());
        yearPo.setCreateTime(DateTimeUtils.getCurrentTime());
        return yearPo;
    }

    private void updateYearPo(AssetFaqDayPo data, AssetFaqYearPo yearPo) {
        yearPo.setAllNum(data.getAllNum());
        yearPo.setUpdateNum(data.getUpdateNum() == null ? "0" : data.getUpdateNum());
        yearPo.setChangeNum(data.getChangeNum() == null ? "0" : data.getChangeNum());
        assetFaqYearMapper.update(yearPo);
    }
    /* Ended by AICoder, pid:109aa508cb4acc51455a0bc2901fdf7bd7d7b226 */


}
