package com.zte.uedm.dcdigital.infrastructure.repository.po;

/* Started by AICoder, pid:y57a5j4c7ewab501425d0a83a085a16c3b02f968 */
import lombok.Data;

import java.time.LocalDateTime;

/**
 * MsgDeliveryPo 类表示消息交付记录的持久化对象。
 *
 * <AUTHOR>
 */
@Data
public class MsgDeliveryPo {

    /**
     * deliveryId 属性表示交付记录的唯一标识符。
     */
    private String deliveryId;

    /**
     * logId 属性表示关联的消息日志ID。
     */
    private String logId;

    /**
     * userId 属性表示接收消息的用户ID。
     */
    private String userId;

    /**
     * sendType 属性表示发送类型（例如：1 - 站内通知，2 - 邮件等）。
     */
    private Integer sendType;

    /**
     * contact 属性表示接收者的联系方式（如邮箱地址、手机号等）。
     */
    private String contact;

    /**
     * msgStatus 属性表示消息的状态（例如：0 - 未处理，1 - 已处理，2 - 失败等）。
     */
    private Integer msgStatus;

    /**
     * errorMsg 属性表示消息发送失败时的错误信息。
     */
    private String errorMsg;

    /**
     * sendTime 属性表示消息的实际发送时间。
     */
    private LocalDateTime sendTime;

    /**
     * createTime 属性表示记录的创建时间。
     */
    private LocalDateTime createTime;

    /**
     * updateTime 属性表示记录的最后更新时间。
     */
    private LocalDateTime updateTime;
}
/* Ended by AICoder, pid:y57a5j4c7ewab501425d0a83a085a16c3b02f968 */
