/* Started by AICoder, pid:124c8487abb5635149180b7ff0501f68c1e74e80 */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.common.bean.system.RoleVo;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.UserRoleResourceEntity;
import com.zte.uedm.dcdigital.domain.common.valueobj.ResourceUserRoleObj;
import com.zte.uedm.dcdigital.infrastructure.repository.po.UserPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.UserRoleResourcePo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户角色资源映射器接口，继承自BaseMapper以利用MyBatis Plus的通用CRUD方法。
 */
@Mapper
public interface UserRoleResourceMapper extends BaseMapper<UserRoleResourcePo> {

    /**
     * 根据用户ID查询所有关联的角色资源。
     *
     * @param userId 用户ID
     * @return 角色资源列表
     */
    List<UserRoleResourcePo> selectAllByUserId(@Param("userId") String userId);

    /**
     * 根据角色ID查询所有关联的用户资源。
     *
     * @param roleId 角色ID
     * @return 用户资源列表
     */
    List<UserRoleResourcePo> selectAllByRoleId(@Param("roleId") String roleId);

    /**
     * 根据资源ID查询所有关联的用户角色。
     *
     * @param resourceId 资源ID
     * @return 用户角色列表
     */
    List<UserRoleResourcePo> selectAllByResourceId(@Param("resourceId") String resourceId);

    /**
     * 批量插入用户角色资源记录。
     *
     * @param list 用户角色资源列表
     */
    void insertBatch(List<UserRoleResourcePo> list);

    /**
     * 根据资源ID删除所有关联的用户角色资源记录。
     *
     * @param resourceId 资源ID
     */
    void deleteByResourceId(@Param("resourceId") String resourceId);

    /**
     * 根据用户ID、角色ID和资源ID查询单个用户角色资源记录。
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @param resourceId 资源ID
     * @return 单个用户角色资源记录
     */
    UserRoleResourcePo selectByUserIdAndRoleIdAndResourceId(
            @Param("userId") String userId,
            @Param("roleId") String roleId,
            @Param("resourceId") String resourceId
    );

    List<String> selectEntityIdsByUserIdAndType(@Param("userId") String userId, @Param("type") Integer type);
    @MapKey("id")
    List<Map<String, Object>> selectEntitiesByType(@Param("type") Integer type);

    List<UserPo> selectUserByRoleCodeAndResourceId(@Param("roleCode") String roleCode, @Param("resourceId") String resourceId);

    List<RoleVo> selectRoleByUserIdAndResourceId(@Param("userId") String userId, @Param("resourceId") String resourceId);

    List<ResourceUserRoleObj> getResourceByIdsAndRoleCode(@Param("resourceIds") List<String> resourceIds, @Param("roleCode") String roleCode);

    List<ResourceUserRoleObj> getResourceById(@Param("id") String id);


    void deleteByResourceIdAndRoleId(@Param("resourceId")  String resourceId,@Param("roleIds") List<String> roleIds);

    /* Started by AICoder, pid:uf82c9f5ae08da2147ba08f2a04d5b043e2722bb */
    /**
     * 根据用户ID查询该用户关联的所有地区ID。
     *
     * @param userId 用户的唯一标识符。
     * @param roleCode 角色编码。
     * @return 一个包含所有关联地区ID的列表。
     */
    List<String> selectAreaIdByUserId(@Param("userId") String userId,String roleCode);
    /**
     * 根据资源id(这里的资源id指的是auth_resource表里面的entity_id,也就是目前的项目id/产品小类id/地区id)和角色code获取对应用户id
     * @param entityId 资源id
     * @param roleCode 角色code
     * @return Map key为资源id,value为用户id
     * */
    List<String> getUserIdByEntityIdAndRoleCode(@Param("entityId")String entityId,@Param("roleCode") String roleCode);
    /**
     * 根据资源id(这里的资源id指的是auth_resource表里面的entity_id,也就是目前的项目id/产品小类id/地区id)和角色code以及用户id获取用户角色是否已拥有该资源权限
     * @param entityId 资源id
     * @param roleId 角色id
     * @param userId 用户id
     * @return
     * */
    List<UserRoleResourceEntity> getUserRoleResourceDataByUserRoleResourceEntity(@Param("entityId")String entityId,@Param("roleId") String roleId,@Param("userId") String userId);

    List<UserRoleResourceEntity> getUserRoleByUserIdAndRoleCode(String roleId, String userId);

    List<ResourceUserRoleObj> queryByResourceAndRole(@Param("entityId") String entityId, @Param("roleCodes") List<String> roleCodes);

    /* Ended by AICoder, pid:uf82c9f5ae08da2147ba08f2a04d5b043e2722bb */
}

/* Ended by AICoder, pid:124c8487abb5635149180b7ff0501f68c1e74e80 */