package com.zte.uedm.dcdigital.interfaces.web.controller;

/* Started by AICoder, pid:y4e796faabsdd6f1410e09c5f036a34251653487 */
import com.zte.uedm.dcdigital.application.brand.executor.UserRoleResourceCommandService;
import com.zte.uedm.dcdigital.application.system.executor.UserQueryService;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * UserController 类用于处理用户相关的 REST API。
 * 该类包含查询用户的方法。
 */
@Path("uportal/user")
@Api(value = "user")
@Controller
@Slf4j
public class UserController {

    @Autowired
    private UserQueryService userQueryService;

    @Autowired
    private UserRoleResourceCommandService userRoleResourceCommandService;

    /**
     * 根据搜索文本获取用户列表。
     *
     * @param searchText 搜索文本
     * @return 包含用户列表的 BaseResult 对象
     */
    @GET
    @Path("/getUserBySearchText")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "根据搜索文本获取用户列表", notes = "根据提供的搜索文本查询用户列表", httpMethod = "GET")
    public BaseResult<Object> getUserBySearchText(@QueryParam("searchText") String searchText) {
        List<UserVo> userVos = userQueryService.queryUserBySearchText(searchText);
        return BaseResult.success(userVos);
    }
}
/* Ended by AICoder, pid:y4e796faabsdd6f1410e09c5f036a34251653487 */