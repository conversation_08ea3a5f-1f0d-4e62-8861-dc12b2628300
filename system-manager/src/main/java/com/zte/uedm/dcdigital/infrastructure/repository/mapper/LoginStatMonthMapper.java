/* Started by AICoder, pid:qf7e5573fbgd9bd149590af160ae7e495c05dbfc */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatMonthPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 登录月统计表 Mapper 接口
 */
@Mapper
public interface LoginStatMonthMapper {

    /**
     * 添加月统计记录
     *
     * @param po 月统计实体
     * @return 影响行数
     */
    int insertLoginStatMonth(LoginStatMonthPo po);

    /**
     * 批量添加月统计记录
     *
     * @param list 月统计实体
     * @return 影响行数
     */
    int batchInsertLoginStatMonth(@Param("list") List<LoginStatMonthPo> list);

    /**
     * 批量删除月统计记录
     *
     * @param ids ID数组
     * @return 影响行数
     */
    int deleteLoginStatMonthByIds(String[] ids);

    /**
     * 查询月统计列表
     *
     * @param po 查询条件
     * @return 月统计列表
     */
    List<LoginStatMonthPo> selectLoginStatMonthList(LoginStatMonthPo po);

    /**
     * 根据ID查询月统计
     *
     * @param id 主键ID
     * @return 月统计实体
     */
    LoginStatMonthPo selectLoginStatMonthById(String id);

    /**
     * 批量更新月统计记录
     *
     * @param poList 月统计实体
     * @return 影响行数
     */
    void batchUpdateLoginStatMonth(List<LoginStatMonthPo> poList);

    /**
     *  根据月份删除月表数据
     * */
    void deleteByMonthNumber(Integer monthNumber);
}

/* Ended by AICoder, pid:qf7e5573fbgd9bd149590af160ae7e495c05dbfc */