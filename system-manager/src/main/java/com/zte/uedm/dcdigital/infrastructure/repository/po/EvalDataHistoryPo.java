/* Started by AICoder, pid:16d74q68c1q56ac14d3f0a83401afd55d5061dcc */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@TableName("eval_data_history")
public class EvalDataHistoryPo {
    // 主键ID
    @TableId(value = "id")
    private String id;

    // 功能点ID
    @TableField("function_id")
    private String functionId;

    // 用户ID/工号
    @TableField("user_id")
    private String userId;

    // 用户名称
    @TableField("user_name")
    private String userName;

    /**
     * 操作类型
     * 0: 赞
     * 1: 踩
     */
    @TableField("operation_type")
    private Integer operationType;

    // 评价内容
    @TableField("eval_content")
    private String evalContent;

    // 创建人
    @TableField("create_by")
    private String createBy;

    // 创建时间
    @TableField("create_time")
    private String createTime;

    // 查询条件字段（非数据库字段）
    @TableField(exist = false)
    private String beginTime;

    @TableField(exist = false)
    private String endTime;
}

/* Ended by AICoder, pid:16d74q68c1q56ac14d3f0a83401afd55d5061dcc */