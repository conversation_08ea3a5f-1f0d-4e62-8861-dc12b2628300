package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 物料资产下级统计响应VO
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class MaterialAssetJuniorVo {

    /**
     * 下级统计数据列表
     */
    private List<MaterialJuniorData> juniorList;

    /**
     * 汇总行数据
     */
    private MaterialJuniorData summary;

    /**
     * 总数（分页用）
     */
    private Integer total;

    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MaterialJuniorData {

        /**
         * 产品小类名称（格式：产品大类名称/产品小类名称）
         */
        private String productCategoryName;

        /**
         * 指定时间节点的物料总数
         */
        private Long allNum;

        /**
         * 已上架物料数
         */
        private Long upNum;

        /**
         * 已上架物料变化数
         */
        private Long upChangeNum;

        /**
         * 已上架变化标志：0-不变，1-上升，2-下降
         */
        private Integer upChangeFlag;

        /**
         * 未上架物料数
         */
        private Long downNum;

        /**
         * 未上架物料变化数
         */
        private Long downChangeNum;

        /**
         * 未上架变化标志：0-不变，1-上升，2-下降
         */
        private Integer downChangeFlag;

        /**
         * 更新物料数
         */
        private Long updateNum;

        /**
         * 产品小类ID（内部使用，不返回给前端）
         */
        private String productCategoryId;
    }
}
