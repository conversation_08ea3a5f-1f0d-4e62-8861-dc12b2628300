/* Started by AICoder, pid:s57677914d8ccdc14d500a2e9099641ee706717d */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 部门成员
 */
@Getter
@Setter
@ToString
public class DeptUserVo {
    //部门用户关联ID
    private String relationId;
    //用户工号
    private String accountId;

    //用户名称
    private String name;

    //用户名称+工号
    private String userIdName;

    //部门/科室路径
    private String orgNamePath;
    private String deptId;
}

/* Ended by AICoder, pid:s57677914d8ccdc14d500a2e9099641ee706717d */