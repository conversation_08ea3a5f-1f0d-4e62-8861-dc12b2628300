package com.zte.uedm.dcdigital.application.system.executor.impl;

/* Started by AICoder, pid:x92988d2d521b71146c50b7e3008377b66a3a760 */
import com.zte.uedm.dcdigital.application.system.converter.ResourceConverter;
import com.zte.uedm.dcdigital.common.bean.system.ResourceDto;
import com.zte.uedm.dcdigital.common.bean.system.ResourceEntityDto;
import com.zte.uedm.dcdigital.common.bean.system.UserRoleDto;
import com.zte.uedm.dcdigital.domain.service.UserRoleResourceService;
import com.zte.uedm.dcdigital.interfaces.web.dto.UserRoleResourceDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ResourceCommandServiceImplTest {

    @InjectMocks
    private ResourceCommandServiceImpl resourceCommandService;

    @Mock
    private UserRoleResourceService userRoleResourceService;

    @Mock
    private ResourceConverter converter;

    private ResourceDto resourceDto;
    private UserRoleResourceDto userRoleResourceDto;

    @Before
    public void setUp() {
        resourceDto = mock(ResourceDto.class);
        userRoleResourceDto = mock(UserRoleResourceDto.class);
//        when(resourceDto.getResourceEntityDto()).thenReturn(mock(ResourceEntityDto.class));
//        when(resourceDto.getResourceEntityDto().getEntityId()).thenReturn("1");
    }

    // 测试创建资源的方法
    @Test
    public void given_ResourceDto_when_CreateResource_then_InsertBatchCalled() {
        resourceCommandService.createResource(resourceDto);
        verify(userRoleResourceService, times(1)).insertBatch(anyList(),anyList(),any());
    }

    // 测试更新资源的方法
    @Test
    public void given_ResourceDto_when_UpdateResource_then_DeleteAndCreateCalled() {
        ResourceDto resourceDto1 = new ResourceDto();
        ResourceEntityDto resourceEntityDto = new ResourceEntityDto();
        resourceEntityDto.setEntityId("1");
        resourceDto1.setResourceEntityDto(resourceEntityDto);
        UserRoleDto userRoleDto = new UserRoleDto();
        userRoleDto.setId("1");
        userRoleDto.setName("john");
        userRoleDto.setEmail("<EMAIL>");
        userRoleDto.setEmployeeId("1111");
        userRoleDto.setPhoneNumber("123");
        resourceDto1.setUserRoleDtoList(Arrays.asList(userRoleDto));
        resourceCommandService.updateResource(resourceDto1);
        verify(userRoleResourceService, times(1)).updateResource(Mockito.any());
    }

    // 测试删除资源的方法
    @Test
    public void given_EntityId_when_DeleteResource_then_DeleteByEntityIdCalled() {
        resourceCommandService.deleteResource("1");
        verify(userRoleResourceService, times(1)).deleteByEntityId("1");
    }

}

/* Ended by AICoder, pid:x92988d2d521b71146c50b7e3008377b66a3a760 */