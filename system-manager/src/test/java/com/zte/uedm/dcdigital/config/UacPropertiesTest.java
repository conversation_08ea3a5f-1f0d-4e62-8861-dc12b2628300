package com.zte.uedm.dcdigital.config;

import com.zte.uedm.dcdigital.common.util.PojoTestUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
class UacPropertiesTest {

    @Test
    @SneakyThrows
    void test() {
        UacProperties uacProperties = new UacProperties();
        log.info("uac properties: {}", uacProperties);
        Assertions.assertDoesNotThrow(() -> PojoTestUtil.TestForPojo(UacProperties.class));
    }

}