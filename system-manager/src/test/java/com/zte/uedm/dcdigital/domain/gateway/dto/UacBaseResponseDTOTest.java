package com.zte.uedm.dcdigital.domain.gateway.dto;/* Started by AICoder, pid:f433eqb93czb3be143bb09dd10ce5d6c9af728fc */
import com.zte.uedm.dcdigital.domain.gateway.dto.UacBaseResponseDTO;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class UacBaseResponseDTOTest {

    private UacBaseResponseDTO<String> uacBaseResponseDTO;

    @Before
    public void setUp() {
        uacBaseResponseDTO = new UacBaseResponseDTO<>();
    }

    @Test
    public void given_initialState_when_setCode_then_codeIsSet() {
        // Given: Initial state of the object
        // When: Setting the code
        uacBaseResponseDTO.setCode("0000");
        // Then: Verify the code is set correctly
        assertEquals("0000", uacBaseResponseDTO.getCode());
    }

    @Test
    public void given_initialState_when_setMessage_then_messageIsSet() {
        // Given: Initial state of the object
        // When: Setting the message
        uacBaseResponseDTO.setMessage("Success");
        // Then: Verify the message is set correctly
        assertEquals("Success", uacBaseResponseDTO.getMessage());
    }

    @Test
    public void given_initialState_when_setData_then_dataIsSet() {
        // Given: Initial state of the object
        // When: Setting the data
        uacBaseResponseDTO.setData("Some Data");
        // Then: Verify the data is set correctly
        assertEquals("Some Data", uacBaseResponseDTO.getData());
    }

    @Test
    public void given_nullValues_when_setFields_then_fieldsAreNull() {
        // Given: Initial state of the object
        // When: Setting fields to null
        uacBaseResponseDTO.setCode(null);
        uacBaseResponseDTO.setMessage(null);
        uacBaseResponseDTO.setData(null);
        // Then: Verify all fields are null
        assertNull(uacBaseResponseDTO.getCode());
        assertNull(uacBaseResponseDTO.getMessage());
        assertNull(uacBaseResponseDTO.getData());
    }

    @Test
    public void given_emptyString_when_setFields_then_fieldsAreEmpty() {
        // Given: Initial state of the object
        // When: Setting fields to empty strings
        uacBaseResponseDTO.setCode("");
        uacBaseResponseDTO.setMessage("");
        uacBaseResponseDTO.setData("");
        // Then: Verify all fields are empty strings
        assertEquals("", uacBaseResponseDTO.getCode());
        assertEquals("", uacBaseResponseDTO.getMessage());
        assertEquals("", uacBaseResponseDTO.getData());
    }
}

/* Ended by AICoder, pid:f433eqb93czb3be143bb09dd10ce5d6c9af728fc */