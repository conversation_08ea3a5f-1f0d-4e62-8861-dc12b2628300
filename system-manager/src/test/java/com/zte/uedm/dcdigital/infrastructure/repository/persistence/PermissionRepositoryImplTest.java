package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

/* Started by AICoder, pid:0ac176d29124b0b141940af1f1eb315efae1257b */
import com.zte.uedm.dcdigital.domain.aggregate.model.PermissionEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.PermissionConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.PermissionMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.PermissionPo;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;


@RunWith(MockitoJUnitRunner.class)
@PrepareForTest({PermissionConverter.class, CollectionUtils.class})
public class PermissionRepositoryImplTest {

    @InjectMocks
    private PermissionRepositoryImpl permissionRepository;

    @Mock
    private PermissionMapper permissionMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void given_Id_when_selectMenuById_then_returnPermissionEntity() {
        // Given
        String id = "1";
        PermissionPo mockPermissionPo = new PermissionPo();
        Mockito.when(permissionMapper.selectById(id)).thenReturn(mockPermissionPo);
        PermissionEntity expectedEntity = new PermissionEntity();

        // When
        PermissionEntity result = permissionRepository.selectMenuById(id);

        // Then
        assertNotNull(result);
    }

    @Test
    public void given_NullId_when_selectMenuById_then_returnNull() {
        // Given
        String id = "nonexistent";
        Mockito.when(permissionMapper.selectById(id)).thenReturn(null);

        // When
        PermissionEntity result = permissionRepository.selectMenuById(id);

        // Then
        assertNull(result);
    }

    @Test
    public void given_Ids_when_selectByIds_then_returnPermissionEntities() {
        // Given
        List<String> ids = Arrays.asList("1", "2");
        List<PermissionPo> mockPermissionPos = Arrays.asList(new PermissionPo(), new PermissionPo());
        Mockito.when(permissionMapper.selectBatchIds(ids)).thenReturn(mockPermissionPos);
        List<PermissionEntity> expectedEntities = Arrays.asList(new PermissionEntity(), new PermissionEntity());
        // When
        List<PermissionEntity> result = permissionRepository.selectByIds(ids);

        // Then
        assertNotNull(result);
    }

    @Test
    public void given_Ids_when_selectByIds_then_returnPermissionEntities1() {
        // Given
        List<PermissionPo> mockPermissionPos = Arrays.asList(new PermissionPo(), new PermissionPo());
        Mockito.when(permissionMapper.selectPermissionByEmployeeId("ids")).thenReturn(mockPermissionPos);
        List<PermissionEntity> expectedEntities = Arrays.asList(new PermissionEntity(), new PermissionEntity());
        // When
        List<PermissionEntity> result = permissionRepository.selectPermissionByEmployeeId("ids");

        // Then
        assertNotNull(result);
    }

    @Test
    public void given_EmptyIds_when_selectByIds_then_returnEmptyList() {
        // Given
        List<String> ids = Collections.emptyList();
        Mockito.when(permissionMapper.selectBatchIds(ids)).thenReturn(Collections.emptyList());

        // When
        List<PermissionEntity> result = permissionRepository.selectByIds(ids);

        // Then
        assertNotNull(result);
    }

    @Test
    public void given_UserId_when_selectByUserId_then_returnPermissionEntities() {
        // Given
        String userId = "user1";
        List<PermissionPo> mockPermissionPos = Arrays.asList(new PermissionPo(), new PermissionPo());
        Mockito.when(permissionMapper.selectByUserId(userId)).thenReturn(mockPermissionPos);
        List<PermissionEntity> expectedEntities = Arrays.asList(new PermissionEntity(), new PermissionEntity());

        // When
        List<PermissionEntity> result = permissionRepository.selectByUserId(userId);

        // Then
        assertNotNull(result);
    }

    @Test
    public void given_UserIdAndResourceId_when_selectByUserIdAndResourceId_then_returnPermissionEntities() {
        // Given
        String userId = "user1";
        String resourceId = "resource1";
        List<PermissionPo> mockPermissionPos = Arrays.asList(new PermissionPo(), new PermissionPo());
        Mockito.when(permissionMapper.selectByUserIdAndResourceId(userId, resourceId)).thenReturn(mockPermissionPos);
        List<PermissionEntity> expectedEntities = Arrays.asList(new PermissionEntity(), new PermissionEntity());

        // When
        List<PermissionEntity> result = permissionRepository.selectByUserIdAndResourceId(userId, resourceId);

        // Then
        assertNotNull(result);
    }

}
/* Ended by AICoder, pid:0ac176d29124b0b141940af1f1eb315efae1257b */
