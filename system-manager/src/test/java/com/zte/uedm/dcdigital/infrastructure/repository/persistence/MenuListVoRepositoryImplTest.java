package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

/* Started by AICoder, pid:v9b37y365ef0bff14aac0b1d81a71e1ecd41eb5b */

import com.zte.uedm.dcdigital.domain.aggregate.model.MenuEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.MenuMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.MenuPo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

public class MenuListVoRepositoryImplTest {

    @Mock
    private MenuMapper menuMapper;

    @InjectMocks
    private MenuRepositoryImpl menuRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testSelectMenuById_InvalidId() {
        String invalidId = "";
        MenuEntity result = menuRepository.selectMenuById(invalidId);
        assertNull(result);
    }

    @Test
    void testSelectMenuById_NullResult() {
        String validId = "validId";
        when(menuMapper.selectById(validId)).thenReturn(null);
        MenuEntity result = menuRepository.selectMenuById(validId);
        assertNull(result, "Expected null when no record found");
    }

    @Test
    void testSelectMenuById_ValidId() {
        String validId = "validId";
        MenuPo menuPo = new MenuPo();
        menuPo.setId(validId);
        when(menuMapper.selectById(validId)).thenReturn(menuPo);


        MenuEntity result = menuRepository.selectMenuById(validId);
        assertNotNull(result, "Expected non-null MenuEntity for valid ID");
        assertEquals(validId, result.getId(), "Expected IDs to match");
    }

    @Test
    void testSelectByIds_EmptyList() {
        List<String> ids = Collections.emptyList();
        List<MenuEntity> result = menuRepository.selectByIds(ids);
        assertTrue(result.isEmpty(), "Expected empty list for empty input");
    }

    @Test
    void testSelectByIds_NonEmptyList() {
        List<String> ids = Arrays.asList("id1", "id2");
        List<MenuPo> menuPos = Arrays.asList(new MenuPo(), new MenuPo());
        when(menuMapper.selectBatchIds(ids)).thenReturn(menuPos);

        List<MenuEntity> result = menuRepository.selectByIds(ids);
        assertFalse(result.isEmpty(), "Expected non-empty list for non-empty input");
        assertEquals(2, result.size(), "Expected list size to match");
    }

    @Test
    void testSelectByUserId() {
        String userId = "userId";
        List<MenuPo> menuPos = Arrays.asList(new MenuPo(), new MenuPo());
        when(menuMapper.selectByUserId(userId)).thenReturn(menuPos);
        List<MenuEntity> result = menuRepository.selectByUserId(userId);
        assertFalse(result.isEmpty(), "Expected non-empty list for valid user ID");
        assertEquals(2, result.size(), "Expected list size to match");
    }

    @Test
    void testSelectByUserIdAndResourceId() {
        String userId = "userId";
        String resourceId = "resourceId";
        List<MenuPo> menuPos = Arrays.asList(new MenuPo(), new MenuPo());
        when(menuMapper.selectByUserIdAndResourceId(userId, resourceId)).thenReturn(menuPos);

        List<MenuEntity> result = menuRepository.selectByUserIdAndResourceId(userId, resourceId);
        assertFalse(result.isEmpty(), "Expected non-empty list for valid user ID and resource ID");
        assertEquals(2, result.size(), "Expected list size to match");
    }
}

/* Ended by AICoder, pid:v9b37y365ef0bff14aac0b1d81a71e1ecd41eb5b */
