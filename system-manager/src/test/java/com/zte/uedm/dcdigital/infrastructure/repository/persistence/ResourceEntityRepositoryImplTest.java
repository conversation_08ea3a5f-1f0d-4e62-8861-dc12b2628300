package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

/* Started by AICoder, pid:mdbbam96971eb6b143940962b1d58c07a4627fcb */
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.ResourceEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ResourceEntityMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ResourceEntityPo;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ResourceEntityRepositoryImplTest {

    @InjectMocks
    private ResourceEntityRepositoryImpl resourceEntityRepository;

    @Mock
    private ResourceEntityMapper resourceEntityMapper;

    @Before
    public void setUp() {
        // Setup code if needed
    }

    // 测试 selectById 方法
    @Test
    public void given_ResourceEntityExists_when_SelectById_then_ReturnResourceEntity() {
        String id = "1";
        ResourceEntityPo po = new ResourceEntityPo();
        po.setId(id);

        Mockito.when(resourceEntityMapper.selectById(id)).thenReturn(po);

        ResourceEntity result = resourceEntityRepository.selectById(id);
        assertNotNull(result);
        assertEquals(id, result.getId());
    }

    @Test
    public void given_NoResourceEntity_when_SelectById_then_ReturnNull() {
        String id = "nonexistent";

        Mockito.when(resourceEntityMapper.selectById(id)).thenReturn(null);

        ResourceEntity result = resourceEntityRepository.selectById(id);
        assertNull(result);
    }

    // 测试 insert 方法
    @Test
    public void given_ResourceEntity_when_Insert_then_InsertIntoDatabase() {
        ResourceEntity entity = new ResourceEntity();
        entity.setId("1");

        ResourceEntityPo po = new ResourceEntityPo();
        po.setId("1");
        Assertions.assertDoesNotThrow(() -> resourceEntityRepository.insert(entity));
    }

    // 测试 selectByEntityId 方法
    @Test
    public void given_ResourceEntityExists_when_SelectByEntityId_then_ReturnResourceEntity() {
        String entityId = "1";
        ResourceEntityPo po = new ResourceEntityPo();
        po.setEntityId(entityId);

        Mockito.when(resourceEntityMapper.selectByEntityId(entityId)).thenReturn(po);

        ResourceEntity result = resourceEntityRepository.selectByEntityId(entityId);
        assertNotNull(result);
        assertEquals(entityId, result.getEntityId());
    }

    @Test
    public void given_NoResourceEntity_when_SelectByEntityId_then_ReturnNull() {
        String entityId = "nonexistent";

        Mockito.when(resourceEntityMapper.selectByEntityId(entityId)).thenReturn(null);

        ResourceEntity result = resourceEntityRepository.selectByEntityId(entityId);
        assertNull(result);
    }

    // 测试 deleteByEntityId 方法
    @Test
    public void given_EntityId_when_DeleteByEntityId_then_DeleteFromDatabase() {
        String entityId = "1";

        resourceEntityRepository.deleteByEntityId(entityId);

        Mockito.verify(resourceEntityMapper).deleteByEntityId(entityId);
    }
}

/* Ended by AICoder, pid:mdbbam96971eb6b143940962b1d58c07a4627fcb */
