package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

/* Started by AICoder, pid:ice563f98453a7f1492b0aeda023bd76c9f5037c */
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.UserEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.UserPoConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.UserMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.UserPo;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserRepositoryImplTest {

    @InjectMocks
    private UserRepositoryImpl userRepository;

    @Mock
    private UserMapper userMapper;

    @Mock
    private UserPoConverter userPoConverter;

    private UserEntity userEntity;
    private UserPo userPo;

    @Before
    public void setUp() {
        userEntity = new UserEntity();
        userPo = new UserPo();
    }

    @Test
    public void given_UserEntity_when_CreateUser_then_InsertCalled() {
        // Given a UserEntity
        // When creating the user
        Assertions.assertDoesNotThrow(() -> userRepository.createUser(userEntity));
    }

    @Test
    public void given_UserId_when_SelectUserByIdAndUserExists_then_ReturnUserEntity() {
        // Given a userId and a mock for selectById returning a UserPo
        String userId = "testId";
        Mockito.when(userMapper.selectById(userId)).thenReturn(userPo);

        // When selecting the user by id
        UserEntity result = userRepository.selectUserById(userId);

        // Then the result should be the converted UserEntity
        assertNotNull(result);
    }

    @Test
    public void given_UserId_when_SelectUserByIdAndUserDoesNotExist_then_ReturnNull() {
        // Given a userId and a mock for selectById returning null
        String userId = "nonexistentId";
        Mockito.when(userMapper.selectById(userId)).thenReturn(null);

        // When selecting the user by id
        UserEntity result = userRepository.selectUserById(userId);

        // Then the result should be null
        assertNull(result);
    }

    /* Started by AICoder, pid:20e06u8467uce1914f88087890699f4381d20eaf */
    @Test
    public void given_userIds_when_selectUserByIds_then_returnCorrectEntities() {
        // 预置条件：准备一些用户ID和对应的PO对象
        List<String> userIds = Arrays.asList("1", "2");
        UserPo userPo1 = new UserPo();
        userPo1.setId("1");
        UserPo userPo2 = new UserPo();
        userPo2.setId("2");
        List<UserPo> userPos = Arrays.asList(userPo1, userPo2);

        // 模拟userMapper的行为
        when(userMapper.selectBatchIds(userIds)).thenReturn(userPos);

        // 执行方法
        List<UserEntity> result = userRepository.selectUserByIds(userIds);

        // 验证结果
        assertEquals(2, result.size());
        assertEquals("1", result.get(0).getId());
        assertEquals("2", result.get(1).getId());

        // 验证userMapper是否被正确调用
        verify(userMapper, times(1)).selectBatchIds(userIds);
    }

    @Test
    public void given_emptyUserIds_when_selectUserByIds_then_returnEmptyList() {
        // 预置条件：空的用户ID列表
        List<String> userIds = Collections.emptyList();

        // 模拟userMapper的行为
        when(userMapper.selectBatchIds(userIds)).thenReturn(Collections.emptyList());

        // 执行方法
        List<UserEntity> result = userRepository.selectUserByIds(userIds);

        // 验证结果
        assertEquals(0, result.size());

        // 验证userMapper是否被正确调用
        verify(userMapper, times(1)).selectBatchIds(userIds);
    }
    /* Ended by AICoder, pid:20e06u8467uce1914f88087890699f4381d20eaf */

    @Test
    public void given_searchText_when_selectUserBySearchText_then_returnCorrectEntities() {
        // 预置条件：准备一些用户ID和对应的PO对象
        String searchText = "john 11";
        UserPo userPo1 = new UserPo();
        userPo1.setId("1");
        UserPo userPo2 = new UserPo();
        userPo2.setId("2");
        List<UserPo> userPos = Arrays.asList(userPo1, userPo2);

        // 模拟userMapper的行为
        when(userMapper.selectBySearchText(searchText)).thenReturn(userPos);

        // 执行方法
        List<UserEntity> result = userRepository.selectUserBySearchText(searchText);

        // 验证结果
        assertEquals(2, result.size());
        assertEquals("1", result.get(0).getId());
        assertEquals("2", result.get(1).getId());

        // 验证userMapper是否被正确调用
        verify(userMapper, times(1)).selectBySearchText(searchText);
    }
}

/* Ended by AICoder, pid:ice563f98453a7f1492b0aeda023bd76c9f5037c */
