package com.zte.uedm.dcdigital.interfaces.web.controller;/* Started by AICoder, pid:lcd9e4d55fd35881415208ac9187ca8ab8b72827 */
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.gateway.UacApiService;
import com.zte.uedm.dcdigital.domain.gateway.dto.UacVerifyTokenResponseDTO;
import com.zte.uedm.dcdigital.interfaces.web.dto.UacLoginUserDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.UacTokenInfoDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.UacUserInfoDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.UacUserInfoQueryDto;
import com.zte.uedm.dcdigital.sdk.system.AuthConstant;
import com.zte.uedm.dcdigital.domain.utils.AuthUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UacAuthUportalControllerTest {

    @InjectMocks
    private UacAuthUportalController uacAuthUportalController;

    @Mock
    private UacApiService uacApiService;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Before
    public void setUp() {
        // 初始化代码（如果需要）
    }

    /**
     * 测试根据授权码获取访问令牌。
     */
    @Test
    public void given_codeAndRedirectUri_when_getAccessTokenByCode_then_returnBaseResult() throws BusinessException {
        when(request.getHeader(AuthConstant.HEADER_DC_CODE)).thenReturn("testCode");
        when(request.getHeader(AuthConstant.HEADER_REDIRECT_URI)).thenReturn("testRedirectUri");

        UacTokenInfoDto tokenInfo = new UacTokenInfoDto();
        when(uacApiService.getAccessTokenByCode(anyString(), anyString(), any(HttpServletResponse.class))).thenReturn(tokenInfo);

        BaseResult<Object> result = uacAuthUportalController.getAccessTokenByCode(request, response);
        assertEquals(BaseResult.success(tokenInfo).getCode(), result.getCode());

        verify(uacApiService, times(1)).getAccessTokenByCode("testCode", "testRedirectUri", response);
    }

    /**
     * 测试无效参数时抛出BusinessException。
     */
    @Test(expected = BusinessException.class)
    public void given_invalidParameters_when_getAccessTokenByCode_then_throwBusinessException() {
        when(request.getHeader(AuthConstant.HEADER_DC_CODE)).thenReturn(null);
        when(request.getHeader(AuthConstant.HEADER_REDIRECT_URI)).thenReturn(null);

        uacAuthUportalController.getAccessTokenByCode(request, response);
    }

    /**
     * 测试根据访问令牌获取用户信息。
     */
    @Test
    public void given_accessToken_when_getUserInfo_then_returnBaseResult() throws BusinessException {
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_ACCESS_TOKEN)).thenReturn("testToken");

        UacLoginUserDto userInfo = new UacLoginUserDto();
        when(uacApiService.getUacUserInfo(anyString(), anyString(), anyString())).thenReturn(userInfo);

        BaseResult<UacLoginUserDto> result = uacAuthUportalController.getUserInfo(request);
        assertEquals(BaseResult.success(userInfo).getCode(), result.getCode());

        verify(uacApiService, times(1)).getUacUserInfo("testToken", "", "");
    }

    /**
     * 测试无效令牌时抛出BusinessException。
     */
    @Test(expected = BusinessException.class)
    public void given_invalidToken_when_getUserInfo_then_throwBusinessException() {
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_ACCESS_TOKEN)).thenReturn(null);

        uacAuthUportalController.getUserInfo(request);
    }

    /* Started by AICoder, pid:bd030b3f3fn0c6614cb00b11001132347de329bc */
    /**
     * 测试令牌校验接口。
     */
    @Test
    public void given_validToken_when_verify_then_returnBaseResult() throws Exception {
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_TOKEN)).thenReturn("testToken");
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_ACCOUNT_ID)).thenReturn("testAccountId");
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_REFRESH_TOKEN)).thenReturn("testRefreshToken");

        // 使用反射调用私有方法
        Method getClientIpAddressMethod = UacAuthUportalController.class.getDeclaredMethod("getClientIpAddress", HttpServletRequest.class);
        getClientIpAddressMethod.setAccessible(true);

        when(getClientIpAddressMethod.invoke(uacAuthUportalController, request)).thenReturn("127.0.0.1");

        UacVerifyTokenResponseDTO verifyResult = new UacVerifyTokenResponseDTO();
        when(uacApiService.verifyToken(anyString(), anyString(), anyString(), anyString())).thenReturn(verifyResult);

        BaseResult<Object> result = uacAuthUportalController.verify(request);
        assertEquals(BaseResult.success(verifyResult).getCode(), result.getCode());
        verify(uacApiService, times(1)).verifyToken("testAccountId", "testToken", "127.0.0.1", "testRefreshToken");
    }

    /* Ended by AICoder, pid:bd030b3f3fn0c6614cb00b11001132347de329bc */

    /**
     * 测试无效令牌时抛出BusinessException。
     */
    @Test(expected = BusinessException.class)
    public void given_invalidToken_when_verify_then_throwBusinessException() {
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_TOKEN)).thenReturn(null);
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_ACCOUNT_ID)).thenReturn(null);

        uacAuthUportalController.verify(request);
    }

    /**
     * 测试退出登录。
     */
    @Test
    public void test_uacLogout() {
        doNothing().when(uacApiService).logout();

        BaseResult<Object> result = uacAuthUportalController.uacLogout();
        assertEquals(BaseResult.success().getCode(), result.getCode());

        verify(uacApiService, times(1)).logout();
    }

    /**
     * 测试模糊查询UAC用户信息。
     */
    @Test
    public void given_queryCondition_when_searchUacUser_then_returnBaseResult() throws BusinessException {
        UacUserInfoQueryDto queryDto = new UacUserInfoQueryDto();
        queryDto.setKey("testKey");

        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_TOKEN)).thenReturn("testToken");
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_ACCOUNT_ID)).thenReturn("testAccountId");

        List<UacUserInfoDto> userInfoList = Collections.singletonList(new UacUserInfoDto());
        when(uacApiService.searchUser(anyList(), anyString(), anyString())).thenReturn(userInfoList);

        BaseResult<List<UacUserInfoDto>> result = uacAuthUportalController.searchUacUser(queryDto, request);
        assertEquals(BaseResult.success(userInfoList).getCode(), result.getCode());

        verify(uacApiService, times(1)).searchUser(Collections.singletonList("testKey"), "testToken", "testAccountId");
    }

    /**
     * 测试空查询条件返回空列表。
     */
    @Test
    public void given_emptyQueryCondition_when_searchUacUser_then_returnEmptyList() {
        UacUserInfoQueryDto queryDto = new UacUserInfoQueryDto();
        queryDto.setKey("");
        // 模拟 AuthUtil.getAuthValueFromRequest 返回有效的 token 和 accountId
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_TOKEN)).thenReturn("validToken");
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_ACCOUNT_ID)).thenReturn("validAccountId");
        BaseResult<List<UacUserInfoDto>> result = uacAuthUportalController.searchUacUser(queryDto, request);
        assertEquals(BaseResult.success(Collections.emptyList()).getCode(), result.getCode());
    }

    /**
     * 测试无效令牌时抛出BusinessException。
     */
    @Test(expected = BusinessException.class)
    public void given_invalidToken_when_searchUacUser_then_throwBusinessException() {
        UacUserInfoQueryDto queryDto = new UacUserInfoQueryDto();
        queryDto.setKey("testKey");

        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_TOKEN)).thenReturn(null);
        when(AuthUtil.getAuthValueFromRequest(request, AuthConstant.COOKIE_UAC_ACCOUNT_ID)).thenReturn(null);

        uacAuthUportalController.searchUacUser(queryDto, request);
    }
}

/* Ended by AICoder, pid:lcd9e4d55fd35881415208ac9187ca8ab8b72827 */