package com.zte.uedm.dcdigital.interfaces.web.dto;/* Started by AICoder, pid:p3ffdae919pda80149080904a00fd059ed607b22 */
import com.zte.uedm.dcdigital.interfaces.web.dto.UacTokenInfoDto;
import org.junit.Before;
import org.junit.Test;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.*;

public class UacTokenInfoDtoTest {

    private UacTokenInfoDto dto;

    @Before
    public void setUp() {
        dto = new UacTokenInfoDto();
        dto.setAccessToken("test_access_token");
        dto.setTokenType("Bearer");
        dto.setExpiresIn(3600);
        dto.setRefreshToken("test_refresh_token");
        dto.setIdToken("test_id_token");
        dto.setUacToken("test_uac_token");
        dto.setScope(Arrays.asList("read", "write"));
    }

    @Test
    public void testGettersAndSetters() {
        assertEquals("test_access_token", dto.getAccessToken());
        assertEquals("Bearer", dto.getTokenType());
        assertEquals("test_refresh_token", dto.getRefreshToken());
        assertEquals("test_id_token", dto.getIdToken());
        assertEquals("test_uac_token", dto.getUacToken());
        assertEquals(Arrays.asList("read", "write"), dto.getScope());
    }

    @Test
    public void testToString() {
        String result = dto.toString();
        assertNotNull(result);
        assertNotEquals("", result);

        // Check if the string contains the expected values
        assertTrue(result.contains("accessToken=test_access_token"));
        assertTrue(result.contains("tokenType=Bearer"));
        assertTrue(result.contains("expiresIn=3600"));
        assertTrue(result.contains("refreshToken=test_refresh_token"));
        assertTrue(result.contains("idToken=test_id_token"));
        assertTrue(result.contains("uacToken=test_uac_token"));
        assertTrue(result.contains("scope=[read, write]"));
    }
}

/* Ended by AICoder, pid:p3ffdae919pda80149080904a00fd059ed607b22 */