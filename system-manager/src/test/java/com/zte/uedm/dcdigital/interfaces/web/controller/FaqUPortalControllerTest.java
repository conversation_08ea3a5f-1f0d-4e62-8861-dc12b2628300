package com.zte.uedm.dcdigital.interfaces.web.controller;

/* Started by AICoder, pid:218fdjef468afc0149fb084f21dfcd485a95cf53 */
import com.zte.uedm.dcdigital.application.faq.executor.FaqCommandService;
import com.zte.uedm.dcdigital.application.faq.executor.FaqQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.dto.FaqAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.FaqQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.FaqUpdateDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.FaqVo;
import com.github.pagehelper.PageInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class FaqUPortalControllerTest {

    @InjectMocks
    private FaqUPortalController faqUPortalController;

    @Mock
    private FaqCommandService faqCommandService;

    @Mock
    private FaqQueryService faqQueryService;

    private FaqVo mockFaqVo;
    private PageVO<FaqVo> mockPageInfo;
    private FaqAddDto mockFaqAddDto;
    private FaqQueryDto mockFaqQueryDto;
    private FaqUpdateDto mockFaqUpdateDto;

    @Before
    public void setUp() {
        // 初始化模拟对象
        mockFaqVo = new FaqVo();
        mockPageInfo = new PageVO<>(2, Arrays.asList(mockFaqVo, mockFaqVo));
        mockFaqAddDto = new FaqAddDto();
        mockFaqQueryDto = new FaqQueryDto();
        mockFaqUpdateDto = new FaqUpdateDto();
    }

    @Test
    public void given_FaqId_when_getFaq_then_returnFaqVo() {
        // 预置条件：定义一个FAQ ID
        String faqId = "1";

        // 模拟查询服务的行为
        when(faqQueryService.getById(faqId)).thenReturn(mockFaqVo);

        // 执行：调用getFaq方法
        BaseResult<Object> result = faqUPortalController.getFaq(faqId);

        // 验证：验证查询服务的getById方法是否被调用了一次，并且参数是"1"
        verify(faqQueryService, times(1)).getById(faqId);

        // 断言：验证返回的结果是否与预期一致
        assertEquals(BaseResult.success(mockFaqVo).getCode(), result.getCode());
    }

    @Test
    public void given_QueryCondition_when_queryFaq_then_returnListOfFaqVo() {
        // 预置条件：创建一个FaqQueryDto实例

        // 模拟查询服务的行为
        when(faqQueryService.queryByCondition(any(FaqQueryDto.class))).thenReturn(mockPageInfo);

        // 执行：调用queryFaq方法
        BaseResult<Object> result = faqUPortalController.queryFaq(mockFaqQueryDto);

        // 验证：验证查询服务的queryByCondition方法是否被调用了一次，并且参数是mockFaqQueryDto
        verify(faqQueryService, times(1)).queryByCondition(mockFaqQueryDto);

        // 断言：验证返回的结果列表是否与预期一致
        assertEquals(BaseResult.success(mockPageInfo).getCode(), result.getCode());
    }

    @Test
    public void given_EmptyQueryCondition_when_queryFaq_then_returnEmptyList() {
        // 预置条件：创建一个空的FaqQueryDto实例
        FaqQueryDto emptyQueryDto = new FaqQueryDto();

        // 模拟查询服务的行为
        when(faqQueryService.queryByCondition(emptyQueryDto)).thenReturn(new PageVO<>(0, Collections.emptyList()));

        // 执行：调用queryFaq方法
        BaseResult<Object> result = faqUPortalController.queryFaq(emptyQueryDto);

        // 验证：验证查询服务的queryByCondition方法是否被调用了一次，并且参数是emptyQueryDto
        verify(faqQueryService, times(1)).queryByCondition(emptyQueryDto);

        // 断言：验证返回的结果列表是否为空
        assertEquals(BaseResult.success(new PageInfo<>(Collections.emptyList())).getCode(), result.getCode());
    }

    @Test
    public void given_FaqAddDto_when_insertFaq_then_callCreateFaqOnCommandService() {
        // 预置条件：创建一个FaqAddDto实例

        // 执行：调用insertFaq方法
        BaseResult<Object> result = faqUPortalController.insertFaq(mockFaqAddDto);

        // 验证：验证命令服务的createFaq方法是否被调用了一次，并且参数是mockFaqAddDto
        verify(faqCommandService, times(1)).createFaq(mockFaqAddDto);

        // 断言：验证返回的结果是否为成功
        assertEquals(BaseResult.success().getCode(), result.getCode());
    }

    @Test
    public void given_FaqUpdateDto_when_updateFaq_then_callUpdateFaqOnCommandService() {
        // 预置条件：创建一个FaqUpdateDto实例

        // 执行：调用updateFaq方法
        BaseResult<Object> result = faqUPortalController.updateFaq(mockFaqUpdateDto);

        // 验证：验证命令服务的updateFaq方法是否被调用了一次，并且参数是mockFaqUpdateDto
        verify(faqCommandService, times(1)).updateFaq(mockFaqUpdateDto);

        // 断言：验证返回的结果是否为成功
        assertEquals(BaseResult.success().getCode(), result.getCode());
    }

    @Test
    public void given_FaqId_when_deleteFaq_then_callDeleteFaqOnCommandService() {
        // 预置条件：定义一个FAQ ID
        String faqId = "1";

        // 执行：调用deleteFaq方法
        BaseResult<Object> result = faqUPortalController.deleteFaq(faqId);

        // 验证：验证命令服务的deleteFaq方法是否被调用了一次，并且参数是"1"
        verify(faqCommandService, times(1)).deleteFaq(faqId);

        // 断言：验证返回的结果是否为成功
        assertEquals(BaseResult.success().getCode(), result.getCode());
    }
}

/* Ended by AICoder, pid:218fdjef468afc0149fb084f21dfcd485a95cf53 */
