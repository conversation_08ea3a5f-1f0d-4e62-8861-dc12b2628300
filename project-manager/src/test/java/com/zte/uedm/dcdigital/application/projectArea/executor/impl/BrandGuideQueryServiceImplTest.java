package com.zte.uedm.dcdigital.application.projectArea.executor.impl;

import com.zte.uedm.dcdigital.domain.service.BrandGuideDomainService;
import com.zte.uedm.dcdigital.interfaces.web.vo.BrandGuideProductVo;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;

public class BrandGuideQueryServiceImplTest {
    /* Started by AICoder, pid:dd59agab54d0cf0147140bbfd03a1f732e809a2d */
    @InjectMocks
    private BrandGuideQueryServiceImpl brandGuideQueryService;

    @Mock
    private BrandGuideDomainService brandGuideDomainService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void testQueryByTimeRangeWithResults() {
        String startTime = "2023-01-01 00:00:00";
        String endTime = "2023-01-01 23:59:59";

        BrandGuideProductVo product1 = new BrandGuideProductVo();
        BrandGuideProductVo product2 = new BrandGuideProductVo();

        List<BrandGuideProductVo> expectedList = Arrays.asList(product1, product2);

        Mockito.when(brandGuideDomainService.queryByTimeFrame(startTime, endTime)).thenReturn(expectedList);

        List<BrandGuideProductVo> result = brandGuideQueryService.queryByTimeRange(startTime, endTime);

        assertEquals(expectedList, result);
    }

    @Test
    public void testQueryByTimeRangeWithEmptyList() {
        String startTime = "2023-01-01 00:00:00";
        String endTime = "2023-01-01 23:59:59";

        Mockito.when(brandGuideDomainService.queryByTimeFrame(startTime, endTime)).thenReturn(Collections.emptyList());

        List<BrandGuideProductVo> result = brandGuideQueryService.queryByTimeRange(startTime, endTime);

        assertEquals(Collections.emptyList(), result);
    }
    /* Ended by AICoder, pid:dd59agab54d0cf0147140bbfd03a1f732e809a2d */
}