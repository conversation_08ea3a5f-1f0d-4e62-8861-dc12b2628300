package com.zte.uedm.dcdigital.interfaces.web.controller;

import static org.junit.jupiter.api.Assertions.*;

import com.zte.uedm.dcdigital.application.dashboard.executor.ProjectDashboardQueryService;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectScoreChartDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.TimeRangeDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProductScoreLineChartVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProjectProductsVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProjectScoreResultVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class ProjectDashboardUportalControllerTest {
    @InjectMocks
    private ProjectDashboardUportalController projectDashboardUportalController;
    @Mock
    private ProjectDashboardQueryService projectDashboardQueryService;

    @BeforeEach
    void setUp() {
    }

    @Test
    void projectsScore() {
        BaseResult<ProjectScoreResultVo> baseResult = projectDashboardUportalController.projectsScore(new TimeRangeDto());
        Assertions.assertTrue(baseResult.isSuccess());
    }

    @Test
    void guideProductCategories() {
        BaseResult<ProjectProductsVo> baseResult = projectDashboardUportalController.guideProductCategories("projectId");
        Assertions.assertTrue(baseResult.isSuccess());
    }

    @Test
    void lineChart() {
        ProjectScoreChartDto chartDto = new ProjectScoreChartDto();
        chartDto.setProjectId("project1");
        chartDto.setCategoryIds(Collections.singletonList("product-category1"));
        BaseResult<List<ProductScoreLineChartVo>> baseResult = projectDashboardUportalController.lineChart(chartDto);
        Assertions.assertTrue(baseResult.isSuccess());
    }

    @Test
    void lineChart_paramError() {
        BaseResult<List<ProductScoreLineChartVo>> baseResult = projectDashboardUportalController.lineChart(new ProjectScoreChartDto());
        Assertions.assertEquals(StatusCode.PARAM_ERROR.getCode(), baseResult.getCode());
    }
}