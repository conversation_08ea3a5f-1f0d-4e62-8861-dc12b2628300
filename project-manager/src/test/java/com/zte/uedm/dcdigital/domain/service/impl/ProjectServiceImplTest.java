package com.zte.uedm.dcdigital.domain.service.impl;

/* Started by AICoder, pid:pdd0ca688a2ee53144530af9c241d56c422150c1 */
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

import java.util.*;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.document.DocumentInfoVo;
import com.zte.uedm.dcdigital.common.bean.enums.DocumentTypeEnums;
import com.zte.uedm.dcdigital.common.bean.system.ResourceVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.aggregate.model.LaunchBiddingEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectAreaObj;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.LaunchBiddingRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ProjectRepository;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.enums.ProjectStageEnum;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProjectStatusCode;
import com.zte.uedm.dcdigital.domain.repository.BrandGuideRepository;
import com.zte.uedm.dcdigital.domain.service.ProjectAreaDomainService;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BrandGuidePo;
import com.zte.uedm.dcdigital.interfaces.web.dto.LaunchBidDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectEditDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.sdk.system.dto.UacUserInfoDto;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import com.zte.uedm.dcdigital.security.util.PermissionUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ProjectServiceImplTest {

    @InjectMocks
    private ProjectServiceImpl projectService;

    @Mock
    private ProjectRepository projectRepository;

    @Mock
    private SystemService systemService;

    @Mock
    private DocumentService documentService;

    @Mock
    private PermissionUtil permissionUtil;

    @Mock
    private AuthService authService;

    @Mock
    private ProjectAreaDomainService projectAreaDomainService;

    @Mock
    private BrandGuideRepository  brandGuideRepository;

    @Mock
    private LaunchBiddingRepository launchBiddingRepository;

    @Before
    public void setUp() {
        // Setup code if needed
        initializeTestData();
    }

    @Test
    public void testGetProjectStageList() {
        List<StageVo> stageVoList = projectService.getProjectStageList();
        assertEquals(4, stageVoList.size()); // Assuming there are 3 stages in ProjectStageEnum
    }

    @Test
    public void testSelectProjectCustomers() {
        when(projectRepository.selectCustomers("testName")).thenReturn(Collections.emptyList());
        List<CustomerVo> customers = projectService.selectProjectCustomers("testName");
        assertEquals(0, customers.size());
    }

    @Test
    public void testSelectProject() {
        String userId = "1";
        when(authService.getUserId()).thenReturn(userId);
        when(systemService.getAreaIdsByUserId(userId,null)).thenReturn(Collections.singletonList("area1"));
        when(systemService.getEntityIdsByUserId(userId, GlobalConstants.PROJECT_TYPE)).thenReturn(Collections.singletonList("project1"));

        ProjectQueryDto queryDto = new ProjectQueryDto();
        PageVO<ProjectVo> pageVO = new PageVO<>();
        when(projectRepository.selectProject(queryDto)).thenReturn(pageVO);

        PageVO<ProjectVo> result = projectService.selectProject(queryDto);
        assertEquals(pageVO, result);
    }

    @Test
    public void testGetDetail_ProjectNotFound() {
        when(projectRepository.getDetail("1")).thenReturn(null);
        try {
            projectService.getDetail("1");
        } catch (BusinessException e) {
            assertEquals(StatusCode.DATA_NOT_FOUND.getCode(), e.getCode());
        }
    }

    /* Started by AICoder, pid:3e829g773bu7af014bbe09f2a077da70e692f196 */
    @Test
    public void testGetDetail_ValidProject() {
        ProjectEntity projectEntity = new ProjectEntity();
        projectEntity.setId("1");
        projectEntity.setName("Test Project");
        projectEntity.setAreaId("area1");
        projectEntity.setProjectStage(1);
        projectEntity.setCustomer("Customer A");
        projectEntity.setNotes("Notes");

        when(projectRepository.getDetail("1")).thenReturn(projectEntity);
        ResourceVo resourceVo = new ResourceVo();
        Map<String, List<UserVo>> userRoleMap = new HashMap<>();
        UserVo userVo = new UserVo();
        userVo.setId("1");
        userVo.setEmployeeId("1666");
        userVo.setName("777");
        List<UserVo> userVoList = new ArrayList<>();
        userVoList.add(userVo);
        userRoleMap.put("scheme-se", userVoList);
        userRoleMap.put("product-se", userVoList);
        userRoleMap.put("deliver-td", userVoList);
        resourceVo.setUserRoleMap(userRoleMap);
        when(systemService.getResourceById("1")).thenReturn(resourceVo);
        when(documentService.queryByAscriptionId("1")).thenReturn(Collections.emptyList());
        ProjectAreaVo projectArea = new ProjectAreaVo();
        projectArea.setAreaName("area1");
        projectArea.setPathName("root/四川省/成都市/青羊区");
        when(projectAreaDomainService.getProjectArea(Mockito.anyString())).thenReturn(projectArea);
        Mockito.doNothing().when(permissionUtil).checkPermission(Mockito.anyString(), Mockito.any());

        ProjectDetailVo detailVo = projectService.getDetail("1");

        assertEquals("1", detailVo.getId());
        assertEquals("Test Project", detailVo.getName());
        assertEquals("area1", detailVo.getAreaId());
        assertEquals("1", detailVo.getProjectStage().toString());
        assertEquals("Customer A", detailVo.getCustomer());
        assertEquals("Notes", detailVo.getNotes());
    }
    /* Ended by AICoder, pid:3e829g773bu7af014bbe09f2a077da70e692f196 */
    @Test
    public void testSelectAreaTree() {
        String userId = "1";
        when(authService.getUserId()).thenReturn(userId);
        when(systemService.getAreaIdsByUserId(userId,null)).thenReturn(Collections.singletonList("area1"));
        when(systemService.getEntityIdsByUserId(userId, GlobalConstants.PROJECT_TYPE)).thenReturn(Collections.singletonList("project1"));

        ProjectEntity projectEntity = new ProjectEntity();
        projectEntity.setAreaId("area1");
        when(projectRepository.selectProjectByIds(Collections.singletonList("project1"))).thenReturn(Collections.singletonList(projectEntity));

        ProjectAreaObj areaObj = new ProjectAreaObj();
        areaObj.setId("area1");
        areaObj.setAreaLevel(1);
        areaObj.setPathName("root/area1");
        when(projectAreaDomainService.selectAreaListBy(any(), any())).thenReturn(Collections.singletonList(areaObj));


        List<AreaTreeVo> result = projectService.selectAreaTree("testName");
        assertEquals(1, result.size());
        assertEquals("area1", result.get(0).getId());
    }

    @Test
    public void testAddProject_ProjectExists() {
        ProjectAddDto addDto = new ProjectAddDto();
        addDto.setName("Test Project");
        addDto.setAreaId("area1");

        when(projectRepository.selectByNameAndAreaId("Test Project", "area1")).thenReturn(Collections.singletonList(new ProjectEntity()));

        try {
            projectService.addProject(addDto);
        } catch (BusinessException e) {
            assertEquals(ProjectStatusCode.PROJECT_EXIST.getCode(), e.getCode());
        }
    }

    @Test
    public void testAddProject_Success() {
        ProjectAddDto addDto = new ProjectAddDto();
        addDto.setName("Test Project");
        addDto.setAreaId("area1");
        addDto.setCustomerName("1111");
        UacUserInfoDto userInfoDto = new UacUserInfoDto();
        userInfoDto.setId("1");
        userInfoDto.setName("Test User");

        when(projectRepository.selectByNameAndAreaId("Test Project", "area1")).thenReturn(Collections.emptyList());
        when(authService.getCurUacUser()).thenReturn(userInfoDto);
        UserVo userVo = new UserVo();
        userVo.setName("Test User");
        when(systemService.getUserinfoById(any())).thenReturn(userVo);
        when(projectRepository.saveOrUpdate(any(), eq(false))).thenReturn(1);

        Boolean result = projectService.addProject(addDto);
        assertEquals(true, result);
    }

    @Test
    public void testEditProject_ProjectNotFound() {
        ProjectEditDto editDto = new ProjectEditDto();
        editDto.setId("1");

        when(projectRepository.selectProjectById("1")).thenReturn(null);
        Mockito.doNothing().when(permissionUtil).checkPermission(Mockito.anyString(), Mockito.any());

        try {
            projectService.editProject(editDto);
        } catch (BusinessException e) {
            assertEquals(ProjectStatusCode.PROJECT_NOT_EXIST.getCode(), e.getCode());
        }
    }

    @Test
    public void testEditProject_ProjectExists() {
        ProjectEditDto editDto = new ProjectEditDto();
        editDto.setId("1");
        editDto.setName("Test Project");
        editDto.setAreaId("area1");

        ProjectEntity existingEntity = new ProjectEntity();
        existingEntity.setId("2");
        when(projectRepository.selectProjectById("1")).thenReturn(existingEntity);


        when(projectRepository.selectByNameAndAreaId("Test Project", "area1")).thenReturn(Collections.singletonList(existingEntity));
        Mockito.doNothing().when(permissionUtil).checkPermission(Mockito.anyString(), Mockito.any());

        try {
            projectService.editProject(editDto);
        } catch (BusinessException e) {
            assertEquals(ProjectStatusCode.PROJECT_EXIST.getCode(), e.getCode());
        }
    }

    @Test
    public void testEditProject_Success() {
        ProjectEditDto editDto = new ProjectEditDto();
        editDto.setId("1");
        editDto.setName("Test Project");
        editDto.setAreaId("area1");
        editDto.setProjectStage(1);
        editDto.setCustomerName("1111");

        ProjectEntity existingEntity = new ProjectEntity();
        existingEntity.setId("1");
        existingEntity.setProjectStage(1);
        when(projectRepository.selectProjectById("1")).thenReturn(existingEntity);

        UacUserInfoDto userInfoDto = new UacUserInfoDto();
        userInfoDto.setId("1");
        userInfoDto.setName("Test User");
        when(authService.getCurUacUser()).thenReturn(userInfoDto);

        when(projectRepository.selectByNameAndAreaId("Test Project", "area1")).thenReturn(null);
        when(projectRepository.saveOrUpdate(any(), eq(true))).thenReturn(1);
        Mockito.doNothing().when(permissionUtil).checkPermission(Mockito.anyString(), Mockito.any());

        Boolean result = projectService.editProject(editDto);
        assertEquals(true, result);
    }
    @Test
    public void testEditProject_fail() {
        ProjectEditDto editDto = new ProjectEditDto();
        editDto.setId("1");
        editDto.setName("Test Project");
        editDto.setAreaId("area1");
        editDto.setProjectStage(1);
        editDto.setCustomerName("1111");
        ProjectEntity existingEntity = new ProjectEntity();
        existingEntity.setId("1");
        existingEntity.setProjectStage(2);
        when(projectRepository.selectProjectById("1")).thenReturn(existingEntity);
        try {
            projectService.editProject(editDto);
        } catch (BusinessException e) {
            assertEquals(ProjectStatusCode.INVALID_PROJECT_STAGE_MODIFY.getCode(), e.getCode());
        }
        try {
            existingEntity.setProjectStage(2);
            editDto.setProjectStage(3);
            projectService.editProject(editDto);
        } catch (BusinessException e) {
            assertEquals(ProjectStatusCode.FORBIDDEN_MODIFY_STAGE.getCode(), e.getCode());
        }
        try {
            editDto.setProjectStage(3);
            existingEntity.setProjectStage(1);
            projectService.editProject(editDto);
        } catch (BusinessException e) {
            assertEquals(ProjectStatusCode.INVALID_CHANGE_STAGE.getCode(), e.getCode());
        }
    }

    @Test
    public void testDeleteProject_ProjectNotFound() {
        when(projectRepository.selectProjectById("1")).thenReturn(null);

        try {
            projectService.deleteProject("1");
        } catch (BusinessException e) {
            assertEquals(ProjectStatusCode.PROJECT_NOT_EXIST.getCode(), e.getCode());
        }
    }

    @Test
    public void testDeleteProject_Success() {
        ProjectEntity projectEntity = new ProjectEntity();
        projectEntity.setId("1");
        projectEntity.setProjectStage(1);

        when(projectRepository.selectProjectById("1")).thenReturn(projectEntity);
        List<BrandGuidePo> list = new ArrayList<>();
        when(brandGuideRepository.getBrandGuideByProjectId("1")).thenReturn(list);
        when(projectRepository.deleteProject("1")).thenReturn(1);
        Mockito.doNothing().when(permissionUtil).checkPermission(Mockito.anyString(), Mockito.any());

        Boolean result = projectService.deleteProject("1");
        assertEquals(true, result);
    }

    @Test
    public void testDeleteProject_Fail() {
        ProjectEntity projectEntity = new ProjectEntity();
        projectEntity.setId("1");
        projectEntity.setProjectStage(3);

        when(projectRepository.selectProjectById("1")).thenReturn(projectEntity);
        List<BrandGuidePo> list = new ArrayList<>();
        when(brandGuideRepository.getBrandGuideByProjectId("1")).thenReturn(list);
        try {
            projectService.deleteProject("1");
        } catch (BusinessException e) {
            assertEquals(ProjectStatusCode.PROJECT_BE_LAUNCH_BIDDING.getCode(), e.getCode());
        }

    }
    @Test
    public void testDeleteProject_brand_Fail() {
        ProjectEntity projectEntity = new ProjectEntity();
        projectEntity.setId("1");
        projectEntity.setProjectStage(3);

        when(projectRepository.selectProjectById("1")).thenReturn(projectEntity);
        BrandGuidePo brandGuidePo = new BrandGuidePo();
        when(brandGuideRepository.getBrandGuideByProjectId("1")).thenReturn(Collections.singletonList(brandGuidePo));
        try {
            projectService.deleteProject("1");
        } catch (BusinessException e) {
            assertEquals(ProjectStatusCode.PROJECT_CITED_BRAND.getCode(), e.getCode());
        }

    }
    @Test
    public void testGetAreaProjectTree() {
        String userId = "1";
        when(authService.getUserId()).thenReturn(userId);
        when(systemService.getAreaIdsByUserId(userId,null)).thenReturn(Collections.singletonList("area1"));
        when(systemService.getEntityIdsByUserId(userId, GlobalConstants.PROJECT_TYPE)).thenReturn(Collections.singletonList("project1"));

        ProjectEntity projectEntity = new ProjectEntity();
        projectEntity.setAreaId("area1");
        when(projectRepository.selectProjectByIds(Collections.singletonList("project1"))).thenReturn(Collections.singletonList(projectEntity));



        ProjectAreaObj matchedArea = new ProjectAreaObj();
        matchedArea.setId("area1");
        matchedArea.setParentId("root");




        AreaProjectTreeVo areaProjectTreeVo = new AreaProjectTreeVo();
        areaProjectTreeVo.setId("area1");

        List<AreaProjectTreeVo> result = projectService.getAreaProjectTree("keyword");
        assertEquals(0, result.size());
    }
    /* Started by AICoder, pid:03353hf9fbkb42d14ce90874b12f42595622fbe1 */
    private ProjectEntity projectEntity;
    private LaunchBidDto launchBidDto;
    private LaunchBiddingEntity launchBiddingEntity;
    private LaunchBiddingVo launchBiddingVo;
    private void initializeTestData() {
        projectEntity = new ProjectEntity();
        projectEntity.setId("1");
        projectEntity.setName("Test Project");
        projectEntity.setProjectStage(ProjectStageEnum.BEFORE_BIDDING.getKey());

        launchBidDto = new LaunchBidDto();
        launchBidDto.setId("1");

        launchBiddingEntity = new LaunchBiddingEntity();
        launchBiddingEntity.setId("1");

        launchBiddingVo = new LaunchBiddingVo();
        launchBiddingVo.setId("1");
        launchBiddingVo.setName("Test Project");
        launchBiddingVo.setProjectStage(ProjectStageEnum.BEFORE_BIDDING.getKey());
        launchBiddingVo.setProjectStageName("Preparation Stage");
    }

    @Test(expected = BusinessException.class)
    public void testGetLaunchTenderById_ProjectNotExists() {
        when(projectRepository.selectProjectById("1")).thenReturn(null);
        projectService.queryBiddingInformationById("1");
    }

    @Test
    public void testGetLaunchTenderById_ProjectExists() {
        when(projectRepository.selectProjectById("1")).thenReturn(projectEntity);
        when(launchBiddingRepository.queryById("1")).thenReturn(launchBiddingEntity);

        LaunchBiddingVo result = projectService.queryBiddingInformationById("1");

        assertNotNull(result);
        assertEquals("1", result.getId());
        assertEquals("Test Project", result.getName());
        assertEquals(ProjectStageEnum.BEFORE_BIDDING.getKey(), result.getProjectStage());
    }

    @Test(expected = BusinessException.class)
    public void testLaunchOrUpdateBidding_ProjectNotExists() {
        when(projectRepository.selectProjectById("1")).thenReturn(null);
        projectService.launchOrUpdateBidding(launchBidDto);
    }


    @Test
    public void testLaunchOrUpdateBidding_New() {
        when(projectRepository.selectProjectById("1")).thenReturn(projectEntity);
        when(authService.getUserId()).thenReturn("user1");
        when(launchBiddingRepository.queryById("1")).thenReturn(null);

        doNothing().when(launchBiddingRepository).saveOrUpdate(any(LaunchBiddingEntity.class), eq(false));
        launchBidDto.setProjectStage(1);
        launchBidDto.setDocumentIdList(Arrays.asList("1", "2"));
        projectService.launchOrUpdateBidding(launchBidDto);

        verify(launchBiddingRepository, times(1)).saveOrUpdate(any(LaunchBiddingEntity.class), eq(false));
        verify(projectRepository, times(1)).saveOrUpdate(any(ProjectEntity.class), eq(true));
    }

    @Test
    public void testLaunchOrUpdateBidding_Update() {
        when(projectRepository.selectProjectById("1")).thenReturn(projectEntity);
        when(authService.getUserId()).thenReturn("user1");
        when(launchBiddingRepository.queryById("1")).thenReturn(launchBiddingEntity);

        doNothing().when(launchBiddingRepository).saveOrUpdate(any(LaunchBiddingEntity.class), eq(true));
        projectEntity.setProjectStage(3);
        launchBidDto.setDocumentIdList(Arrays.asList("1", "2"));
        projectService.launchOrUpdateBidding(launchBidDto);

        verify(launchBiddingRepository, times(1)).saveOrUpdate(any(LaunchBiddingEntity.class), eq(true));
    }

    private List<DocumentInfoVo> getDocumentList() {
        DocumentInfoVo doc = new DocumentInfoVo();
        doc.setDocumentCategoryName(DocumentTypeEnums.BIDDING_DOCUMENT.getName());
        return Collections.singletonList(doc);
    }
    /* Ended by AICoder, pid:03353hf9fbkb42d14ce90874b12f42595622fbe1 */

    @Test
    public void updateLaunchBidById() {

        when(authService.getUserId()).thenReturn("user1");
        when(launchBiddingRepository.queryById("1")).thenReturn(launchBiddingEntity);

        doNothing().when(launchBiddingRepository).saveOrUpdate(any(LaunchBiddingEntity.class), eq(true));
        projectEntity.setProjectStage(3);
        launchBidDto.setDocumentIdList(Arrays.asList("1", "2"));
        launchBidDto.setClarifySubmissionTime("2025-12-12");
        launchBidDto.setConfigureManifestLockTime("2025-12-12");
        launchBidDto.setBiddingDocumentsFinalizationTime("2025-12-12");
        projectService.updateLaunchBidById(launchBidDto);

        verify(launchBiddingRepository, times(1)).saveOrUpdate(any(LaunchBiddingEntity.class), eq(true));
    }
}
/* Ended by AICoder, pid:pdd0ca688a2ee53144530af9c241d56c422150c1 */