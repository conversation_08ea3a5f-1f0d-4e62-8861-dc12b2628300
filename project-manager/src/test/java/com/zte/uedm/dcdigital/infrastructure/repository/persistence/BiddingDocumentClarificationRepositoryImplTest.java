package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

/* Started by AICoder, pid:72d30iaff7eeb0f148840b59a1d1fd0d50f90d4f */
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.aggregate.model.BiddingDocumentClarificationObj;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.BiddingDocumentClarificationConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BiddingDocumentClarificationMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BiddingDocumentClarificationPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationQueryDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BiddingDocumentClarificationRepositoryImplTest {

    @Mock
    private BiddingDocumentClarificationMapper biddingDocumentClarificationMapper;

    @InjectMocks
    private BiddingDocumentClarificationRepositoryImpl repository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testQueryDetailById() {
        String id = "1";
        BiddingDocumentClarificationPo po = new BiddingDocumentClarificationPo();
        when(biddingDocumentClarificationMapper.selectById(id)).thenReturn(po);

        BiddingDocumentClarificationObj result = repository.queryDetailById(id);
        assertNotNull(result);
        verify(biddingDocumentClarificationMapper, times(1)).selectById(id);
    }

    @Test
    void testQueryListByCondition() {
        BiddingDocumentClarificationQueryDto queryDto = new BiddingDocumentClarificationQueryDto();
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);

        when(biddingDocumentClarificationMapper.queryListByCondition(any())).thenReturn(Collections.singletonList(new BiddingDocumentClarificationPo()));

        PageVO<BiddingDocumentClarificationObj> result = repository.queryListByCondition(queryDto);
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        verify(biddingDocumentClarificationMapper, times(1)).queryListByCondition(any());
    }

    @Test
    void testAdd() {
        BiddingDocumentClarificationObj obj = new BiddingDocumentClarificationObj();

        repository.add(obj);

        verify(biddingDocumentClarificationMapper, times(1)).insert(Mockito.any());
    }

    @Test
    void testUpdate() {
        BiddingDocumentClarificationObj obj = new BiddingDocumentClarificationObj();
        repository.update(obj);
        verify(biddingDocumentClarificationMapper, times(1)).updateById(Mockito.any());
    }

    @Test
    void testDeleteById() {
        String id = "1";
        repository.deleteById(id);
        verify(biddingDocumentClarificationMapper, times(1)).deleteById(id);
    }

    @Test
    void testQueryByProjectId() {
        String projectId = "1";
        List<BiddingDocumentClarificationPo> pos = Arrays.asList(new BiddingDocumentClarificationPo(), new BiddingDocumentClarificationPo());

        when(biddingDocumentClarificationMapper.queryByProjectId(projectId)).thenReturn(pos);

        List<BiddingDocumentClarificationObj> result = repository.queryByProjectId(projectId);
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(biddingDocumentClarificationMapper, times(1)).queryByProjectId(projectId);
    }

    @Test
    void testQueryByCondition() {
        BiddingDocumentClarificationQueryDto queryDto = new BiddingDocumentClarificationQueryDto();
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);

        when(biddingDocumentClarificationMapper.queryListByCondition(any())).thenReturn(Collections.singletonList(new BiddingDocumentClarificationPo()));

        List<BiddingDocumentClarificationObj> result = repository.queryByCondition(queryDto);
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(biddingDocumentClarificationMapper, times(1)).queryListByCondition(any());
    }
}

/* Ended by AICoder, pid:72d30iaff7eeb0f148840b59a1d1fd0d50f90d4f */