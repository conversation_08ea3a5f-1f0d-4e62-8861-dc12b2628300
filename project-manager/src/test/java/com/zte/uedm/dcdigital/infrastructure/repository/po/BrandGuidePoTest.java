package com.zte.uedm.dcdigital.infrastructure.repository.po;/* Started by AICoder, pid:rda56l061cp3c7d145160b7451b5eb259b656eb3 */
import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;

public class BrandGuidePoTest {

    private BrandGuidePo brandGuidePo;

    @Before
    public void setUp() {
        brandGuidePo = new BrandGuidePo();
    }

    /**
     * 测试设置和获取主键。
     */
    @Test
    public void given_Id_when_SetAndGet_then_ReturnSameId() {
        String testId = "testId";
        brandGuidePo.setId(testId);
        assertEquals(testId, brandGuidePo.getId());
    }

    /**
     * 测试设置和获取状态。
     */
    @Test
    public void given_Status_when_SetAndGet_then_ReturnSameStatus() {
        Integer testStatus = 1;
        brandGuidePo.setStatus(testStatus);
        assertEquals(testStatus, brandGuidePo.getStatus());
    }

    /**
     * 测试设置和获取项目ID。
     */
    @Test
    public void given_ProjectId_when_SetAndGet_then_ReturnSameProjectId() {
        String testProjectId = "testProjectId";
        brandGuidePo.setProjectId(testProjectId);
        assertEquals(testProjectId, brandGuidePo.getProjectId());
    }

    /**
     * 测试设置和获取引导阶段。
     */
    @Test
    public void given_Stage_when_SetAndGet_then_ReturnSameStage() {
        Integer testStage = 2;
        brandGuidePo.setStage(testStage);
        assertEquals(testStage, brandGuidePo.getStage());
    }

    /**
     * 测试设置和获取版本号。
     */
    @Test
    public void given_Version_when_SetAndGet_then_ReturnSameVersion() {
        String testVersion = "V1.0";
        brandGuidePo.setVersion(testVersion);
        assertEquals(testVersion, brandGuidePo.getVersion());
    }

    /**
     * 测试设置和获取记录时间。
     */
    @Test
    public void given_RecordTime_when_SetAndGet_then_ReturnSameRecordTime() {
        String testRecordTime = "2023-10-10 12:00:00";
        brandGuidePo.setRecordTime(testRecordTime);
        assertEquals(testRecordTime, brandGuidePo.getRecordTime());
    }

    /**
     * 测试设置和获取记录人。
     */
    @Test
    public void given_RecordUser_when_SetAndGet_then_ReturnSameRecordUser() {
        String testRecordUser = "testRecordUser";
        brandGuidePo.setRecordUser(testRecordUser);
        assertEquals(testRecordUser, brandGuidePo.getRecordUser());
    }

    /**
     * 测试设置和获取创建人。
     */
    @Test
    public void given_CreateBy_when_SetAndGet_then_ReturnSameCreateBy() {
        String testCreateBy = "testCreateBy";
        brandGuidePo.setCreateBy(testCreateBy);
        assertEquals(testCreateBy, brandGuidePo.getCreateBy());
    }

    /**
     * 测试设置和获取创建时间。
     */
    @Test
    public void given_CreateTime_when_SetAndGet_then_ReturnSameCreateTime() {
        String testCreateTime = "2023-10-10 12:00:00";
        brandGuidePo.setCreateTime(testCreateTime);
        assertEquals(testCreateTime, brandGuidePo.getCreateTime());
    }

    /**
     * 测试设置和获取更新人。
     */
    @Test
    public void given_UpdateBy_when_SetAndGet_then_ReturnSameUpdateBy() {
        String testUpdateBy = "testUpdateBy";
        brandGuidePo.setUpdateBy(testUpdateBy);
        assertEquals(testUpdateBy, brandGuidePo.getUpdateBy());
    }

    /**
     * 测试设置和获取更新时间。
     */
    @Test
    public void given_UpdateTime_when_SetAndGet_then_ReturnSameUpdateTime() {
        String testUpdateTime = "2023-10-10 12:00:00";
        brandGuidePo.setUpdateTime(testUpdateTime);
        assertEquals(testUpdateTime, brandGuidePo.getUpdateTime());
    }
}

/* Ended by AICoder, pid:rda56l061cp3c7d145160b7451b5eb259b656eb3 */