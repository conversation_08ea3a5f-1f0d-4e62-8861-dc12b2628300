package com.zte.uedm.dcdigital.interfaces.web.controller;
/* Started by AICoder, pid:13fa41f911n22071435b097c70b9428354b00b00 */
import com.zte.uedm.dcdigital.application.projectArea.executor.BrandGuideQueryService;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.dto.BrandGuideBillboardQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BrandGuideProductVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Path("/uportal/notice-board")
@Controller
@Slf4j
public class BrandGuideBillboardUportalController {

    @Autowired
    private BrandGuideQueryService brandGuideQueryService;


    @POST
    @Path("/category/query")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "品牌引导产品看板查询", notes = "品牌引导产品看板查询", httpMethod = "POST")
    public BaseResult<List<BrandGuideProductVo>> queryWithTimeRange(BrandGuideBillboardQueryDto billboardQueryDto) {

        List<BrandGuideProductVo> result;
        if (billboardQueryDto == null || StringUtils.isAllBlank(billboardQueryDto.getStartTime(), billboardQueryDto.getEndTime())) {
           // 设置默认时间范围：当前时间为结束时间，开始时间默认为前一年
           LocalDateTime end = LocalDateTime.now();
           LocalDateTime start = end.minusYears(1);
           String endTime = end.format(DateTimeUtils.DATE_TIME_FORMATTER);
           String startTime = start.format(DateTimeUtils.DATE_TIME_FORMATTER);
           result = brandGuideQueryService.queryByTimeRange(startTime,endTime);
       } else {
           billboardQueryDto.validate();
           // 验证时间范围是否合理
            result = brandGuideQueryService.queryByTimeRange(billboardQueryDto.getStartTime(),billboardQueryDto.getEndTime());
       }
        return BaseResult.success(result);
    }
    /* Ended by AICoder, pid:13fa41f911n22071435b097c70b9428354b00b00 */
}
