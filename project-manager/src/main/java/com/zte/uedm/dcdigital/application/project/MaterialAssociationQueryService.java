/* Started by AICoder, pid:29bd9c669b24e7b14e1d0bdd907c1b15786956d5 */
package com.zte.uedm.dcdigital.application.project;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.interfaces.web.dto.AssociatedMaterialQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAssociatedMaterialQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.AssociatedMaterialVo;
import java.util.List;

public interface MaterialAssociationQueryService {

    List<IdNameBean> queryAssociatedMaterialsSimple(String billId);

    PageVO<AssociatedMaterialVo> queryPagingAssociatedMaterials(AssociatedMaterialQueryDto queryDto);

    List<AssociatedMaterialVo> noPageQueryPagingAssociatedMaterials(AssociatedMaterialQueryDto queryDto);

    PageVO<AssociatedMaterialVo> queryAssociatedMaterials(ProjectAssociatedMaterialQueryDto queryDto);
}

/* Ended by AICoder, pid:29bd9c669b24e7b14e1d0bdd907c1b15786956d5 */