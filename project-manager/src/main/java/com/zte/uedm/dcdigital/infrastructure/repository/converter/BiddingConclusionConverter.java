/* Started by AICoder, pid:l2ca6r48b77a67c148570b4960210e2d44941178 */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.domain.aggregate.model.BiddingConclusionEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BiddingConclusionPo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingConclusionVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface BiddingConclusionConverter {

    // 使用常量 INSTANCE 以便通过 Mappers 工厂获取单例实例
    BiddingConclusionConverter INSTANCE = Mappers.getMapper(BiddingConclusionConverter.class);

    @Mappings({})
    BiddingConclusionEntity poToEntity(BiddingConclusionPo biddingConclusionPo);

    @Mappings({})
    List<BiddingConclusionEntity> poListToEntityList(List<BiddingConclusionPo> biddingConclusionPoList);

    @Mappings({})
    List<BiddingConclusionVo> poListToVoList(List<BiddingConclusionPo> biddingConclusionPoList);

    @Mappings({})
    BiddingConclusionPo entityToPo(BiddingConclusionEntity biddingConclusionEntity);

    @Mappings({})
    BiddingConclusionVo entityToVo(BiddingConclusionEntity biddingConclusionEntity);
}

/* Ended by AICoder, pid:l2ca6r48b77a67c148570b4960210e2d44941178 */