/* Started by AICoder, pid:k1cb0907e0x203f14a860826f06851322d5601dc */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.dcdigital.domain.aggregate.model.LaunchBiddingEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.LaunchBiddingRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.LaunchBiddingConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.LaunchBiddingMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.LaunchBiddingPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * LaunchBiddingRepositoryImpl 类实现了 LaunchBiddingRepository 接口，负责处理与招标实体相关的持久化操作。
 * 它使用 MyBatis Plus 的 ServiceImpl 基类来简化对数据库的操作，并通过 LaunchBiddingMapper 进行数据访问。
 * 该类还利用 LaunchBiddingConvert 工具类在实体对象和持久化对象之间进行转换。
 */
@Slf4j
@Repository
public class LaunchBiddingRepositoryImpl extends ServiceImpl<LaunchBiddingMapper, LaunchBiddingPo> implements LaunchBiddingRepository {

    @Override
    public LaunchBiddingEntity queryById(String id) {
        LaunchBiddingPo launchBiddingPo = baseMapper.selectById(id);
        return LaunchBiddingConvert.INSTANCE.poToEntity(launchBiddingPo);
    }

    @Override
    public void saveOrUpdate(LaunchBiddingEntity launchBiddingEntity, boolean isUpdate) {
        LaunchBiddingPo launchBiddingPo = LaunchBiddingConvert.INSTANCE.entityToPo(launchBiddingEntity);
        if (isUpdate) {
            this.updateById(launchBiddingPo);
        } else {
            this.save(launchBiddingPo);
        }
    }
}
/* Ended by AICoder, pid:k1cb0907e0x203f14a860826f06851322d5601dc */