/* Started by AICoder, pid:z71ecacb68a384d14a810b630006d05ac4e1051d */
package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.biddingconclusion.BiddingConclusionCommandService;
import com.zte.uedm.dcdigital.application.biddingconclusion.BiddingConclusionQueryService;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProjectStatusCode;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingConclusionDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingConclusionDropDownVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingConclusionVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

@Path("/uportal/bidding-conclusion")
@Controller
@Slf4j
public class BiddingConclusionController {

    @Autowired
    private BiddingConclusionQueryService queryService;

    @Autowired
    private BiddingConclusionCommandService commandService;

    @POST
    @Path("/query-drop-down")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取投标结论下拉框数据", notes = "获取投标结论下拉框数据")
    public BaseResult<BiddingConclusionDropDownVo> queryDropDown() {
        BiddingConclusionDropDownVo result = queryService.queryDropDown();
        return BaseResult.success(result);
    }

    @POST
    @Path("/first-fill")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据商机id查询投标结论是否首次填写", notes = "根据商机id查询投标结论是否首次填写")
    public BaseResult<Boolean> queryCheckStatus(BiddingConclusionDto conclusionDto) {
        if (StringUtils.isBlank(conclusionDto.getProjectId())) {
            log.error("first-fill projectId is null");
            throw new BusinessException(ProjectStatusCode.PROJECT_NOT_EXIST);
        }
        Boolean result = queryService.getFirstFill(conclusionDto.getProjectId());
        return BaseResult.success(result);
    }

    @POST
    @Path("/detail")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据商机Id查询投标结论详情", notes = "根据商机Id查询投标结论详情")
    public BaseResult<BiddingConclusionVo> detail(BiddingConclusionDto conclusionDto) {
        if (StringUtils.isBlank(conclusionDto.getProjectId())) {
            log.error("first-fill projectId is null");
            throw new BusinessException(ProjectStatusCode.PROJECT_NOT_EXIST);
        }
        BiddingConclusionVo conclusionVo=queryService.getDetail(conclusionDto.getProjectId());
        return BaseResult.success(conclusionVo);
    }

    @POST
    @Path("/add-or-update")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "新增或更新投标结论", notes = "新增或更新投标结论")
    public BaseResult<Object> addOrUpdate(BiddingConclusionDto conclusionDto) {
        commandService.addOrUpdateBiddingConclusion(conclusionDto);
        return BaseResult.success();
    }
}

/* Ended by AICoder, pid:z71ecacb68a384d14a810b630006d05ac4e1051d */