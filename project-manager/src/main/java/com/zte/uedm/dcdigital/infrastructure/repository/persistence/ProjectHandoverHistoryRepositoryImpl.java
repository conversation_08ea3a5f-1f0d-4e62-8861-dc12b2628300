/* Started by AICoder, pid:f8cc8157b7u1518146a5090920107f21abe7bacc */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectHandoverHistoryEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ProjectHandoverHistoryRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ProjectHandoverHistoryConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProjectHandoverHistoryMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectHandoverHistoryPo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProjectHandoverHistoryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Slf4j
@Repository
public class ProjectHandoverHistoryRepositoryImpl implements ProjectHandoverHistoryRepository {

    @Autowired
    private ProjectHandoverHistoryMapper projectHandoverHistoryMapper;

    /**
     * 增加售前售后交接历史信息
     */
    @Override
    public int addProjectHandoverHistory(ProjectHandoverHistoryEntity historyEntity) {
        ProjectHandoverHistoryPo historyPo = ProjectHandoverHistoryConverter.INSTANCE.entityToPo(historyEntity);
        return projectHandoverHistoryMapper.insert(historyPo);
    }

    @Override
    public List<ProjectHandoverHistoryVo> queryProjectHandoverHistoryByItemId(String ItemId) {
        LambdaQueryWrapper<ProjectHandoverHistoryPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectHandoverHistoryPo::getItemId, ItemId);
        //根据创建时间逆序排序
        queryWrapper.orderByDesc(ProjectHandoverHistoryPo::getCreateTime);
        List<ProjectHandoverHistoryPo> projectHandoverHistoryPos = projectHandoverHistoryMapper.selectList(queryWrapper);
        return ProjectHandoverHistoryConverter.INSTANCE.poToVoList(projectHandoverHistoryPos);
    }
}

/* Ended by AICoder, pid:f8cc8157b7u1518146a5090920107f21abe7bacc */