package com.zte.uedm.dcdigital.application.projectArea.executor.impl;

import com.zte.uedm.dcdigital.application.projectArea.executor.BrandGuideCommandService;
import com.zte.uedm.dcdigital.common.bean.project.ShortBrandUpdateDto;
import com.zte.uedm.dcdigital.domain.service.BrandGuideDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.BrandGuideAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BrandGuideDataAddDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class BrandGuideCommandServiceImpl implements BrandGuideCommandService {
    @Autowired
    private BrandGuideDomainService brandGuideDomainService;

    @Override
    public void addBrandGuideData(BrandGuideDataAddDto addDto) {
        //新增
        brandGuideDomainService.addBrandGuideData(addDto);
    }

    @Override
    public void boardAddGuideBrand(BrandGuideAddDto guideAddDto) {
        brandGuideDomainService.boardAddGuideBrand(guideAddDto);
    }

    @Override
    public void updateGuidedShortBrand(ShortBrandUpdateDto shortBrandUpdateDto) {
        brandGuideDomainService.updateGuidedShortBrand(shortBrandUpdateDto);
    }
}
