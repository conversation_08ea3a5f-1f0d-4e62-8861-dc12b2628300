/* Started by AICoder, pid:ceef6i141etb2021429d0bc7d0652d7f2b936863 */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class ItemQueryDto {

    /**
     * 地区ID
     */
    private String areaId;

    /**
     * 地区集合
     */
    private List<String> areaIds;

    /**
     * 商机ID集合
     */
    private List<String> projectIds;

    /**
     * 项目ID集合
     */
    private List<String> itemIds;

    /**
     * 项目名称、客户名称、方案经理
     */
    private String keywords;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 项目阶段
     */
    private Integer projectStage;

    /**
     * 项目阶段名称
     */
    private String projectStageName;

    /**
     * 方案经理
     */
    private String schemeSE;
    /**
     * 地区
     */
    private String areaPathName;


    /**
     * 交付TD
     */
    private String deliverTD;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;
}

/* Ended by AICoder, pid:ceef6i141etb2021429d0bc7d0652d7f2b936863 */