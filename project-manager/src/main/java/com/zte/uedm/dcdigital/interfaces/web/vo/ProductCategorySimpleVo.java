package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 产品小类简单信息VO
 * 用于返回产品小类的ID和路径名称
 *
 * <AUTHOR> Assistant
 */
@Getter
@Setter
@ToString
public class ProductCategorySimpleVo {

    /**
     * 产品小类ID
     */
    private String id;

    /**
     * 产品小类路径名称
     */
    private String pathName;

    public ProductCategorySimpleVo() {
    }

    public ProductCategorySimpleVo(String id, String pathName) {
        this.id = id;
        this.pathName = pathName;
    }
}
