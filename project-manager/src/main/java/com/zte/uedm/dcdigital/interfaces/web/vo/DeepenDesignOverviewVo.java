package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 项目概览-工程深化设计信息VO
 */
@Getter
@Setter
@ToString
public class DeepenDesignOverviewVo {

    /**
     * 工程深化状态
     */
    private String deepenDesignStatus;

    /**
     * 工程深化进度，取值范围：0-100
     */
    private Integer engineeringDeepenProgress;

    /**
     * 工程深化启动时间，格式：YYYY-MM-DD
     */
    private String startTime;

    /**
     * 产品提资--要求完成时间，格式：YYYY-MM-DD
     */
    private String requestInformationCompletionTime;

    /**
     * 产品提资--实际完成时间，格式：YYYY-MM-DD
     */
    private String actualInformationCompletionTime;

    /**
     * 首批备料清单输出时间，格式：YYYY-MM-DD
     */
    private String firstBatchMaterialPreparationOutputTime;

    /**
     * 长周期物料清单输出时间，格式：YYYY-MM-DD
     */
    private String longPeriodMaterialListOutputTime;

    /**
     * 整体备料清单锁定时间，格式：YYYY-MM-DD
     */
    private String overallMaterialPreparationLockTime;
}
