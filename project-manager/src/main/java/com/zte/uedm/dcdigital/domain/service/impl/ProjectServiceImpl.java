package com.zte.uedm.dcdigital.domain.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.document.UpdateBiddingDocumentsDto;
import com.zte.uedm.dcdigital.common.bean.enums.system.PermissionEnum;
import com.zte.uedm.dcdigital.common.bean.enums.system.RoleCodeEnum;
import com.zte.uedm.dcdigital.common.bean.process.ApprovalCommonVo;
import com.zte.uedm.dcdigital.common.bean.project.ProjectDetailInfoVo;
import com.zte.uedm.dcdigital.common.bean.project.ProjectProductSupportsVo;
import com.zte.uedm.dcdigital.common.bean.project.ProjectUserDto;
import com.zte.uedm.dcdigital.common.bean.system.*;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.aggregate.model.*;
import com.zte.uedm.dcdigital.domain.aggregate.repository.*;
import com.zte.uedm.dcdigital.domain.common.enums.ProjectStageEnum;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProjectStatusCode;
import com.zte.uedm.dcdigital.domain.common.valueobj.ProjectObj;
import com.zte.uedm.dcdigital.domain.repository.BillOfQuantityRepository;
import com.zte.uedm.dcdigital.domain.repository.BrandGuideRepository;
import com.zte.uedm.dcdigital.domain.repository.OpportunitySupportRepository;
import com.zte.uedm.dcdigital.domain.service.BiddingConclusionDomainService;
import com.zte.uedm.dcdigital.domain.service.ProjectAreaDomainService;
import com.zte.uedm.dcdigital.domain.service.ProjectService;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ProjectConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BrandGuidePo;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.common.bean.project.LaunchBiddingInnerVo;
import com.zte.uedm.dcdigital.sdk.process.service.ProcessService;
import com.zte.uedm.dcdigital.sdk.system.dto.UacUserInfoDto;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import com.zte.uedm.dcdigital.security.util.PermissionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;

import javax.validation.constraints.NotNull;
import java.text.Collator;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/* Started by AICoder, pid:k057bp3499d40181408e0a5046d5673716b60e4e */
/**
 * ProjectServiceImpl 类实现了 ProjectService 接口，提供了项目相关的业务逻辑。
 */
@Service
@Slf4j
public class ProjectServiceImpl implements ProjectService {

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private SystemService systemService;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private ProcessService processService;

    @Autowired
    private AuthService authService;

    @Autowired
    private ProjectAreaDomainService projectAreaDomainService;

    @Autowired
    private BrandGuideRepository brandGuideRepository;

    @Autowired
    private PermissionUtil permissionUtil;

    @Autowired
    private LaunchBiddingRepository launchBiddingRepository;

    @Autowired
    private BillOfQuantityRepository billOfQuantityRepository;
    @Autowired
    private OpportunitySupportRepository opportunitySupportRepository;
    @Autowired
    private BiddingConclusionDomainService conclusionDomainService;

    @Autowired
    private BiddingConclusionRepository biddingConclusionRepository;

    @Autowired
    private ProjectHandoverRepository handoverRepository;

    @Autowired
    private ItemInfoRepository itemInfoRepository;

    /**
     * 获取所有项目的阶段列表。
     *
     * @return 包含所有阶段的列表。
     */
    @Override
    public List<StageVo> getProjectStageList() {
        List<StageVo> stageVoList = Stream.of(ProjectStageEnum.values())
                .map(stage -> {
                    StageVo stageVo = new StageVo();
                    stageVo.setKey(stage.getKey());
                    stageVo.setValue(stage.getValue());
                    return stageVo;
                }).collect(Collectors.toList());
        return stageVoList;
    }

    /**
     * 根据名称查询项目客户。
     *
     * @param name 客户名称。
     * @return 包含匹配客户的列表。
     */
    @Override
    public List<CustomerVo> selectProjectCustomers(String name) {
        return projectRepository.selectCustomers(name);
    }

    /**
     * 根据查询条件分页查询项目。
     *
     * @param queryDto 查询条件。
     * @return 包含分页信息和项目列表的 PageVO 对象。
     */
    @Override
    public PageVO<ProjectVo> selectProject(ProjectQueryDto queryDto) {
        String userId = authService.getUserId();
        List<String> areaIds = systemService.getAreaIdsByUserId(userId,null);
        log.debug("areaIds = {}", areaIds);
        List<String> allProjectIds = systemService.getEntityIdsByUserId(userId, GlobalConstants.PROJECT_TYPE);
        if(CollectionUtils.isEmpty(allProjectIds) && CollectionUtils.isEmpty(areaIds)){
            return new PageVO<>();
        }
        if(StringUtils.isNotEmpty(queryDto.getAreaId())){
            List<String> allAreaIds = projectAreaDomainService.getAllAreaIds(queryDto.getAreaId());
            log.debug("allAreaIds = {}", allAreaIds);
            List<String> stringList = areaIds.stream().filter(allAreaIds::contains).collect(Collectors.toList());
            queryDto.setAreaIds(stringList);
            // 查询当前地区下有哪些项目
            List<String> projectIds = projectRepository.selectProjectByAreaIds(allAreaIds);
            List<String> haveProjectIds = allProjectIds.stream().filter(projectIds::contains).collect(Collectors.toList());
            queryDto.setProjectIds(haveProjectIds);
        }else{
            queryDto.setAreaIds(areaIds);
            queryDto.setProjectIds(allProjectIds);
        }
        log.debug("queryDto:{}",queryDto);
        return projectRepository.selectProject(queryDto);
    }

    @Override
    public List<ProjectVo> selectProject() {
        String userId = authService.getUserId();
        List<String> areaIds = systemService.getAreaIdsByUserId(userId,null);
        List<String> allProjectIds = systemService.getEntityIdsByUserId(userId, GlobalConstants.PROJECT_TYPE);
        if(CollectionUtils.isEmpty(allProjectIds) && CollectionUtils.isEmpty(areaIds)){
            return new ArrayList<>();
        }
        return projectRepository.selectProject(allProjectIds, areaIds);
    }

    /**
     * 根据项目ID获取项目的详细信息。
     *
     * @param id 项目ID。
     * @return 项目的详细信息。
     */
    @Override
    public ProjectDetailVo getDetail(String id) {
        ProjectDetailVo detailVo = new ProjectDetailVo();
        ProjectEntity projectEntity = projectRepository.getDetail(id);
        if (projectEntity == null) {
            log.error("project does not exist.");
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        detailVo.setId(projectEntity.getId());
        detailVo.setName(projectEntity.getName());
        detailVo.setAreaId(projectEntity.getAreaId());
        detailVo.setProjectStage(projectEntity.getProjectStage());
        detailVo.setProjectStageName(ProjectStageEnum.getValueByKey(projectEntity.getProjectStage()));
        detailVo.setCustomer(projectEntity.getCustomer());
        detailVo.setNotes(projectEntity.getNotes());
        ProjectAreaVo projectArea = projectAreaDomainService.getProjectArea(projectEntity.getAreaId());
        log.debug("projectArea={}",projectArea);
        if(projectArea!=null){
            detailVo.setAreaName(projectArea.getAreaName());
            String pathName = projectArea.getPathName();
            if(StringUtils.isNotBlank(pathName) && pathName.startsWith(GlobalConstants.DEFAULT_PATH)){
                //去掉root/
                pathName = pathName.substring(GlobalConstants.DEFAULT_PATH.length());
            }
            detailVo.setAreaPathName(pathName);
        }
        ResourceVo resourceVo = systemService.getResourceById(id);
        log.debug("systemService getResourceById resourceVo:{}",resourceVo);
        if (isResourceVoValid(resourceVo)){
            Map<String,List<UserDto>> roleMap = new HashMap<>();
            Map<String, List<UserVo>> userRoleMap = resourceVo.getUserRoleMap();
            for (Map.Entry<String, List<UserVo>> entry : userRoleMap.entrySet()) {
                String roleCode = entry.getKey();
                List<UserDto> managerVoList = entry.getValue().stream()
                        .map(this::getManagerVo)
                        .collect(Collectors.toList());
                roleMap.put(roleCode, managerVoList);
            }
            // 方案SE
            detailVo.setSchemeSE(roleMap.get(RoleCodeEnum.SCHEME_SE.getCode()));
            //产品SE
            detailVo.setProductSolutionEngineer(roleMap.get(RoleCodeEnum.PRODUCT_SE.getCode()));
            // 交付TD
            detailVo.setDeliverTD(roleMap.get(RoleCodeEnum.DELIVER_TD.getCode()));
            // 成本总监
            detailVo.setCostDirector(roleMap.get(RoleCodeEnum.COST_DIRECTOR.getCode()));
            // 商务总监
            detailVo.setBusinessDirector(roleMap.get(RoleCodeEnum.BUSINESS_DIRECTOR.getCode()));
            // 物流总监
            detailVo.setLogisticsDirector(roleMap.get(RoleCodeEnum.LOGISTICS_DIRECTOR.getCode()));
        }
        // 查询关联文档分页 单独查询
        return detailVo;
    }

    /**
     * 根据区域名称选择区域树。
     *
     * @param areaName 区域名称。
     * @return 包含区域树的列表。
     */
    @Override
    public List<AreaTreeVo> selectAreaTree(String areaName) {
        List<String> allAreaIds = new ArrayList<>();
        String userId = authService.getUserId();
        // 查询有权限的地区
        List<String> areaIds = systemService.getAreaIdsByUserId(userId,null);
        log.debug("areaIds={}",areaIds);
        // 查询查看地区
        List<String> projectIds = systemService.getEntityIdsByUserId(userId, GlobalConstants.PROJECT_TYPE);
        if(!projectIds.isEmpty()){
            List<ProjectEntity> projectEntities = projectRepository.selectProjectByIds(projectIds);
            List<String> viewAreaIds = Optional.ofNullable(projectEntities)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(ProjectEntity::getAreaId)
                    .collect(Collectors.toList());
            allAreaIds.addAll(viewAreaIds);
        }
        allAreaIds.addAll(areaIds);
        if(allAreaIds.isEmpty()){
            return Collections.emptyList();
        }
        log.debug("areaIds={}",allAreaIds);
        List<ProjectAreaObj> areaObjs = projectAreaDomainService.selectAreaListBy(allAreaIds, areaName);
        log.debug("projectAreaDomainService selectAreaListBy areaObjs:{}",areaObjs);
        List<AreaTreeVo> areaTreeVos = ProjectConvert.INSTANCE.objListToVo(areaObjs);
        for (AreaTreeVo treeVo : areaTreeVos) {
            treeVo.setPathName(treeVo.getPathName().replace(GlobalConstants.DEFAULT_PATH,""));
            treeVo.setAddCheck(treeVo.getAreaLevel().equals(GlobalConstants.TWO));
        }
        return buildTree(areaTreeVos);
    }

    /**
     * 添加新项目。
     *
     * @param addDto 新项目的信息。
     * @return 操作结果。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean addProject(ProjectAddDto addDto) {
        List<ProjectEntity> projectEntities = projectRepository.selectByNameAndAreaId(addDto.getName().trim(), addDto.getAreaId());
        if(!projectEntities.isEmpty()){
            throw new BusinessException(ProjectStatusCode.PROJECT_EXIST);
        }
        ProjectEntity projectEntity = new ProjectEntity();
        projectEntity.setCustomer(addDto.getCustomerName().trim());
        projectEntity.setNotes(addDto.getNotes());
        projectEntity.setName(addDto.getName().trim());
        projectEntity.setAreaId(addDto.getAreaId());
        projectEntity.setProjectStage(addDto.getProjectStage());
        createParam(projectEntity, true);
        //方案se
        projectEntity.setSchemeSe(concatenateNames(addDto.getSchemeSE()));
        //交付td
        projectEntity.setDeliverTd(concatenateNames(addDto.getDeliverTD()));
        //成本总监
        projectEntity.setCostDirector(concatenateNames(addDto.getCostDirector()));
        //商务总监
        projectEntity.setBusinessDirector(concatenateNames(addDto.getBusinessDirector()));
        //物流总监
        projectEntity.setLogisticsDirector(concatenateNames(addDto.getLogisticsDirector()));
        log.debug("projectEntity={}",projectEntity);
        int i = projectRepository.saveOrUpdate(projectEntity, false);
        if(i > GlobalConstants.ZERO){
            updateRelatedUser(addDto, projectEntity.getId(),false);
        }
        return true;
    }

    /**
     * 编辑现有项目。
     *
     * @param updateDto 更新项目的信息。
     * @return 操作结果。
     */
    @Override
    public Boolean editProject(ProjectEditDto updateDto) {
        List<String> checkPermissionIds = new ArrayList<>();
        checkPermissionIds.add(updateDto.getId());
        checkPermissionIds.add(updateDto.getAreaId());
        permissionUtil.checkPermission(checkPermissionIds,PermissionEnum.PROJECT_PROJECT_EDIT);
        ProjectEntity projectEntity = projectRepository.selectProjectById(updateDto.getId());
        if (projectEntity == null) {
            throw new BusinessException(ProjectStatusCode.PROJECT_NOT_EXIST);
        }
        List<ProjectEntity> projectEntities = projectRepository.selectByNameAndAreaId(updateDto.getName().trim(), updateDto.getAreaId());
        List<ProjectEntity> entityList = Optional.ofNullable(projectEntities)
                .orElse(Collections.emptyList())
                .stream()
                .filter(group -> !group.getId().equals(updateDto.getId()))
                .collect(Collectors.toList());
        if(!entityList.isEmpty()){
            throw new BusinessException(ProjectStatusCode.PROJECT_EXIST);
        }
        projectEntity.setCustomer(updateDto.getCustomerName().trim());
        projectEntity.setNotes(updateDto.getNotes());
        projectEntity.setName(updateDto.getName().trim());
        projectEntity.setAreaId(updateDto.getAreaId());
        checkProjectStage(projectEntity.getProjectStage(),updateDto.getProjectStage());
        projectEntity.setProjectStage(updateDto.getProjectStage());
        createParam(projectEntity, false);
        //方案se
        projectEntity.setSchemeSe(concatenateNames(updateDto.getSchemeSE()));
        //交付td
        projectEntity.setDeliverTd(concatenateNames(updateDto.getDeliverTD()));
        //成本总监
        projectEntity.setCostDirector(concatenateNames(updateDto.getCostDirector()));
        //商务总监
        projectEntity.setBusinessDirector(concatenateNames(updateDto.getBusinessDirector()));
        //物流总监
        projectEntity.setLogisticsDirector(concatenateNames(updateDto.getLogisticsDirector()));
        int i = projectRepository.saveOrUpdate(projectEntity, true);
        if(i > GlobalConstants.ZERO){
            updateRelatedUser(updateDto, projectEntity.getId(),true);
        }
        return true;
    }

    private void checkProjectStage(Integer currentStage,Integer newStage){
        if (currentStage.equals(newStage)) {
            //项目阶段没有变化
            return;
        }
        //默认 商机阶段
        //TODO 修改时阶段只可向前不可向后（顺序依次为：1商机阶段、2标前阶段、3投标阶段、4交付阶段）
        if (newStage < currentStage) {
            throw new BusinessException(ProjectStatusCode.INVALID_PROJECT_STAGE_MODIFY);
        }
        //TODO 且只可在商机阶段、标前阶段进行阶段修改；修改，当前只能是商机阶段
        if (!ProjectStageEnum.BUSINESS_OPPORTUNITY.getKey().equals(currentStage)) {
            throw new BusinessException(ProjectStatusCode.FORBIDDEN_MODIFY_STAGE);
        }
        //TODO 只有商机阶段改为标前阶段，标前阶段不可再修改 修改，只能改为标前阶段
        if (!ProjectStageEnum.BEFORE_BIDDING.getKey().equals(newStage)) {
            throw new BusinessException(ProjectStatusCode.INVALID_CHANGE_STAGE);
        }
    }
    /**
     * 删除项目。
     *
     * @param id 项目ID。
     * @return 操作结果。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteProject(String id) {
        ProjectEntity projectEntity = projectRepository.selectProjectById(id);
        if (projectEntity == null) {
            throw new BusinessException(ProjectStatusCode.PROJECT_NOT_EXIST);
        }
        List<String> checkPermissionIds = new ArrayList<>();
        checkPermissionIds.add(projectEntity.getId());
        checkPermissionIds.add(projectEntity.getAreaId());
        permissionUtil.checkPermission(checkPermissionIds,PermissionEnum.PROJECT_PROJECT_DELETE);
        // todo 查询项目有没有被引用，有则不能删除
        List<BrandGuidePo> brandGuides = brandGuideRepository.getBrandGuideByProjectId(id);
        if(!brandGuides.isEmpty()){
            throw new BusinessException(ProjectStatusCode.PROJECT_CITED_BRAND);
        }
        //TODO 启动投标后不允许删除，即投标阶段、交付阶段都不能删除
        if(projectEntity.getProjectStage().equals(ProjectStageEnum.BIDDING_STAGE.getKey()) || projectEntity.getProjectStage().equals(ProjectStageEnum.DELIVER_STAGE.getKey())){
            throw new BusinessException(ProjectStatusCode.PROJECT_BE_LAUNCH_BIDDING);
        }
        int result = projectRepository.deleteProject(id);
        if(result == GlobalConstants.ZERO){
            throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
        }
        systemService.deleteResource(id);
        return result > GlobalConstants.ZERO;
    }

    /**
     * 根据关键字获取区域项目树。
     *
     * @param keyword 关键字。
     * @return 包含区域项目树的列表。
     */
    @Override
    public List<AreaProjectTreeVo> getAreaProjectTree(String keyword) {
        List<String> allAreaIds = new ArrayList<>();
        String userId = authService.getUserId();
        // 查询管理地区
        List<String> areaIds = systemService.getAreaIdsByUserId(userId,null);
        // 查询查看地区
        List<String> projectIds = systemService.getEntityIdsByUserId(userId, GlobalConstants.PROJECT_TYPE);
        if(!projectIds.isEmpty()){
            List<ProjectEntity> projectEntities = projectRepository.selectProjectByIds(projectIds);
            List<String> viewAreaIds = Optional.ofNullable(projectEntities)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(ProjectEntity::getAreaId)
                    .collect(Collectors.toList());
            allAreaIds.addAll(viewAreaIds);
        }
        allAreaIds.addAll(areaIds);
        if(allAreaIds.isEmpty()){
            return Collections.emptyList();
        }
        log.debug("allAreaIds = {}", allAreaIds);
        // 查询有权限所有地区
        List<ProjectAreaObj> allAreas = projectAreaDomainService.selectAreaListBy(allAreaIds, null);
        log.debug("alleras = {}",allAreas);
        // 查询满足查询条件的项目
        List<ProjectEntity> matchedProjects = projectRepository.selectProjectByKeyword(allAreaIds,keyword);
        log.debug("matchedProjects = {}",matchedProjects);
        return areaProjectBuildTree(allAreas, matchedProjects, StringUtils.isNotEmpty(keyword));
    }

    /* Started by AICoder, pid:tb109i081am25a114e3f09ecb0ef4a85486527e0 */
    @Override
    public LaunchBiddingVo queryBiddingInformationById(String id) {
        //区分编辑查询和新增查询
        ProjectEntity projectEntity = projectRepository.selectProjectById(id);
        if(projectEntity == null){
            throw new BusinessException(ProjectStatusCode.PROJECT_NOT_EXIST);
        }
        LaunchBiddingVo launchBiddingVo = new LaunchBiddingVo();
        Map<String, Boolean> result = launchBiddingVo.getResult();
        launchBiddingVo.setId(projectEntity.getId());
        launchBiddingVo.setName(projectEntity.getName());
        launchBiddingVo.setProjectStage(projectEntity.getProjectStage());
        launchBiddingVo.setProjectStageName(ProjectStageEnum.getValueByKey(projectEntity.getProjectStage()));
        LaunchBiddingEntity launchBiddingEntity = launchBiddingRepository.queryById(id);
        List<ApprovalCommonVo> approvals = processService.getApprovalByProjectId(id);
        approvals.sort((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()));
        List<ApprovalCommonVo> clearList = approvals.stream().filter(a -> a.getApprovalType() == 5 && a.getStatus() == 8).collect(Collectors.toList());
        List<ApprovalCommonVo> lockList = approvals.stream().filter(a -> a.getApprovalType() == 6 && a.getStatus() == 8).collect(Collectors.toList());
        List<ApprovalCommonVo> docList = approvals.stream().filter(a -> a.getApprovalType() == 7 && a.getStatus() == 8).collect(Collectors.toList());
        if(launchBiddingEntity != null){
            launchBiddingVo.setBidIssuingTime(launchBiddingEntity.getBidIssuingTime());
            launchBiddingVo.setClarifySubmissionTime(launchBiddingEntity.getClarifySubmissionTime());
            String clarifySubmissionCompleteTime = StringUtils.isNotBlank(launchBiddingEntity.getClarifySubmissionCompleteTime()) ? launchBiddingEntity.getClarifySubmissionCompleteTime() : GlobalConstants.NOT_YET_COMPLETED;
            launchBiddingVo.setClarifySubmissionCompleteTime(clarifySubmissionCompleteTime);
            launchBiddingVo.setConfigureManifestLockTime(launchBiddingEntity.getConfigureManifestLockTime());
            String configureManifestLockTimeCompleteTime = StringUtils.isNotBlank(launchBiddingEntity.getConfigureManifestLockTimeCompleteTime()) ? launchBiddingEntity.getConfigureManifestLockTimeCompleteTime() : GlobalConstants.NOT_YET_COMPLETED;
            launchBiddingVo.setConfigureManifestLockTimeCompleteTime(configureManifestLockTimeCompleteTime);
            launchBiddingVo.setBiddingDocumentsFinalizationTime(launchBiddingEntity.getBiddingDocumentsFinalizationTime());
            String biddingDocumentsFinalizationCompletionTime = StringUtils.isNotBlank(launchBiddingEntity.getBiddingDocumentsFinalizationCompletionTime()) ? launchBiddingEntity.getBiddingDocumentsFinalizationCompletionTime() : GlobalConstants.NOT_YET_COMPLETED;
            launchBiddingVo.setBiddingDocumentsFinalizationCompletionTime(biddingDocumentsFinalizationCompletionTime);
            launchBiddingVo.setBidSubmissionTime(launchBiddingEntity.getBidSubmissionTime());
            BillOfQuantityQueryDto quantityQueryDto = new BillOfQuantityQueryDto();
            quantityQueryDto.setId(id);
            quantityQueryDto.setPageNum(1);
            quantityQueryDto.setPageSize(1000);
            List<BillOfQuantityVo> billOfQuantityVos = billOfQuantityRepository.queryByCondition(quantityQueryDto);
            if (billOfQuantityVos != null && !billOfQuantityVos.isEmpty()) {
                launchBiddingVo.setDecompressCompleteTime(billOfQuantityVos.get(0).getCreateTime());
                if (billOfQuantityVos.size() > 1) {
                    launchBiddingVo.setDecompressUpdateTime(billOfQuantityVos.get(billOfQuantityVos.size() - 1).getUpdateTime());
                }
            }
            launchBiddingVo.setClarifySubmissionCompleteTime(getCompleteTime(clearList));
            launchBiddingVo.setConfigureManifestLockTimeCompleteTime(getCompleteTime(lockList));
            launchBiddingVo.setBiddingDocumentsFinalizationCompletionTime(getCompleteTime(docList));
            if (launchBiddingVo.getProjectStage() != 1) {
                result.put("BIDDING", true);
                if (checkTime(launchBiddingVo.getDecompressCompleteTime())) {
                    result.put("DECOMPOSITION", true);
                }
                if (checkTime(launchBiddingVo.getClarifySubmissionCompleteTime())) {
                    result.put("CLARIFICATION", true);
                }
                if (checkTime(launchBiddingVo.getConfigureManifestLockTimeCompleteTime())) {
                    result.put("CONFIGURATION_LOCK", true);
                }
                if (checkTime(launchBiddingVo.getBiddingDocumentsFinalizationCompletionTime())) {
                    result.put("BIDDING_DOC_FINALIZE", true);
                }
                //交标阶段
                if (launchBiddingVo.getProjectStage() == 4) {
                    result.put("BIDDING_SUBMISSION", true);
                }
                //投标结论确认的阶段样式状态
                setBiddingConclusionState(id,result,launchBiddingVo);
            }
        }
        launchBiddingVo.setResult(result);
        return launchBiddingVo;
    }
    private void setBiddingConclusionState(String projectId, Map<String, Boolean> result, LaunchBiddingVo launchBiddingVo) {
        BiddingConclusionEntity biddingConclusionEntity = biddingConclusionRepository.selectBiddingConclusionByProjectId(projectId);
        if (biddingConclusionEntity != null && biddingConclusionEntity.getFillTimes() >= GlobalConstants.ONE) {
            // 填写过投标结论确认
            result.put("CONFIRMATION_BIDDING_CONCLUSION", true);
            // 确保 BIDDING_SUBMISSION 一定为 true
            result.put("BIDDING_SUBMISSION", true);

            // 获取最新的投标结论时间和最新的交接信息时间
            String conclusionTime = biddingConclusionEntity.getUpdateTime();
            String handoverTime = null;
            ItemInfoEntity itemInfoEntity = itemInfoRepository.selectItemInfoByProjectId(projectId);
            if (itemInfoEntity != null) {
                ProjectHandoverEntity projectHandover = handoverRepository.getProjectHandoverInfoByItemId(itemInfoEntity.getId());
                if (projectHandover != null) {
                    handoverTime = projectHandover.getUpdateTime();
                }
            }

            // 比较 conclusionTime 与 handoverTime，取最新时间
            String newestTime = conclusionTime; // 默认使用结论时间
            if (handoverTime != null) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date conclusionDate = sdf.parse(conclusionTime);
                    Date handoverDate = sdf.parse(handoverTime);
                    // 若交接时间更晚，则更新为交接时间
                    if (handoverDate.after(conclusionDate)) {
                        newestTime = handoverTime;
                    }
                } catch (ParseException e) {
                    // 时间解析异常处理（可根据需求记录日志或忽略）
                    //英文日志
                    log.error("setBiddingConclusionState abnormal time parsing");
                    throw new BusinessException(StatusCode.PARAM_ERROR);
                }
            }
            // 设置最新时间到 Vo 对象
            launchBiddingVo.setBidConclusionUpdateTime(newestTime);
        }
    }

    private String getCompleteTime(List<ApprovalCommonVo> list) {
        if (list != null && !list.isEmpty()) {
            return list.get(0).getUpdateTime();
        }
        return GlobalConstants.NOT_YET_COMPLETED;
    }

    private boolean checkTime(String time) {
        return time != null && !time.equals(GlobalConstants.NOT_YET_COMPLETED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void launchOrUpdateBidding(LaunchBidDto launchBidDto) {
        //区分编辑查询和新增查询
        ProjectEntity projectEntity = projectRepository.selectProjectById(launchBidDto.getId());
        if(projectEntity == null){
            throw new BusinessException(ProjectStatusCode.PROJECT_NOT_EXIST);
        }
        String userId = authService.getUserId();
        String currentTime = DateTimeUtils.getCurrentTime();
        //检查项目状态，避免启动时 状态被修改了
        Integer projectStage = projectEntity.getProjectStage();
        launchBidDto.setProjectStage(projectStage);
        LaunchBiddingEntity launchBiddingEntity = launchBiddingRepository.queryById(launchBidDto.getId());
        if (launchBiddingEntity == null){
            //新增
            //开始投标
            launchBiddingEntity = new LaunchBiddingEntity();
            BeanUtils.copyProperties(launchBidDto,launchBiddingEntity);
            launchBiddingEntity.supplementCreatorInfo(userId,currentTime);
            launchBiddingEntity.supplementModifiedInfo(userId,currentTime);
            //更新项目状态到下一个阶段
            projectEntity.setProjectStage(ProjectStageEnum.BIDDING_STAGE.getKey());
            projectRepository.saveOrUpdate(projectEntity,true);
            launchBiddingRepository.saveOrUpdate(launchBiddingEntity,false);
        } else {
            //编辑
            BeanUtils.copyProperties(launchBidDto,launchBiddingEntity);
            launchBiddingEntity.supplementModifiedInfo(userId,currentTime);
            launchBiddingRepository.saveOrUpdate(launchBiddingEntity,true);
        }
        //rpc调用，文档从临时表覆盖到正式表
        UpdateBiddingDocumentsDto updateDto = new UpdateBiddingDocumentsDto();
        updateDto.setProjectId(launchBidDto.getId());
        updateDto.setDocumentIds(new HashSet<>(launchBidDto.getDocumentIdList()));
        updateDto.setQueryTime(launchBidDto.getQueryTime());
        documentService.batchAddDocuments(updateDto);

    }
    /* Ended by AICoder, pid:tb109i081am25a114e3f09ecb0ef4a85486527e0 */
    /**
     * 更新相关用户的角色。
     *
     * @param addDto 项目信息。
     * @param id 项目ID。
     * @param isUpdate 是否为更新操作。
     */
    private void updateRelatedUser(ProjectAddDto addDto, String id, boolean isUpdate) {
        List<UserRoleDto> userRoleDtoList = new ArrayList<>();
        //方案se
        List<UserRoleDto> schemeSeRole = buildUserRoleDto(RoleCodeEnum.SCHEME_SE.getCode(),addDto.getSchemeSE());
        userRoleDtoList.addAll(schemeSeRole);
        addPermission(id,schemeSeRole);
        //交付td
        List<UserRoleDto> deliverTdRole = buildUserRoleDto(RoleCodeEnum.DELIVER_TD.getCode(),addDto.getDeliverTD());
        userRoleDtoList.addAll(deliverTdRole);
        //成本总监
        List<UserRoleDto> costDirectorRole = buildUserRoleDto(RoleCodeEnum.COST_DIRECTOR.getCode(),addDto.getCostDirector());
        userRoleDtoList.addAll(costDirectorRole);
        //商务总监
        List<UserRoleDto> businessDirectorRole = buildUserRoleDto(RoleCodeEnum.BUSINESS_DIRECTOR.getCode(),addDto.getBusinessDirector());
        userRoleDtoList.addAll(businessDirectorRole);
        //物流总监
        List<UserRoleDto> logisticsDirectorRole = buildUserRoleDto(RoleCodeEnum.LOGISTICS_DIRECTOR.getCode(),addDto.getLogisticsDirector());
        userRoleDtoList.addAll(logisticsDirectorRole);
        ResourceDto resourceDto = new ResourceDto();
        resourceDto.setUserRoleDtoList(userRoleDtoList);
        ResourceEntityDto resourceEntityDto = buildResourceEntityDto(id);
        resourceDto.setResourceEntityDto(resourceEntityDto);
        if(isUpdate){
            systemService.updateResource(resourceDto);
        }else{
            systemService.createResource(resourceDto);
        }
    }

    /**
     * 为方案se赋予项目关联产品小类的"标书澄清"增删改按钮权限
     * */
    private void addPermission(String projectId,List<UserRoleDto> userIds){
        log.info("addPermission projectId={},userIds:{}",projectId,userIds);
        if (CollectionUtils.isEmpty(userIds)){
            return;
        }
        //收集用户id
        List<String> userIdList = userIds.stream().map(UserRoleDto::getId).collect(Collectors.toList());
        BillOfQuantityQueryDto queryDto=new BillOfQuantityQueryDto();
        queryDto.setId(projectId);
        //根据项目id获取工程量清单关联的产品小类
        List<BillOfQuantityVo> voList = billOfQuantityRepository.queryByCondition(queryDto);
        if (CollectionUtils.isEmpty(voList)){
            return;
        }
        //收集产品小类id
        List<String> productCategoryIds=voList.stream().map(BillOfQuantityVo::getProductSubcategory).collect(Collectors.toList());
        //组装资源id
        productCategoryIds.add(projectId);
        log.info("productCategoryIds:{}",productCategoryIds);
        //赋权
        for (String userId : userIdList){
            systemService.addPermissionListByUserIdAndResourceId(userId,"", productCategoryIds,GlobalConstants.SCHEME_SE);
        }

    }

    /* Started by AICoder, pid:x073c122e1y90971428608d7807b882e3b84b284 */
    /**
     * 根据项目ID查询项目的详细信息。
     *
     * @param id 项目ID，用于查询具体的项目详情。
     * @return 包含查询结果的 ProjectDetailInfoVo 对象。如果未找到项目，将抛出 {@link BusinessException} 异常。
     * @throws BusinessException 当项目不存在时抛出此异常，错误代码为 {@link StatusCode#DATA_NOT_FOUND}。
     */
    @Override
    public ProjectDetailInfoVo queryProjectDetail(String id) {
        ProjectDetailInfoVo detailVo = new ProjectDetailInfoVo();
        ProjectEntity projectEntity = projectRepository.getDetail(id);
        if (projectEntity == null) {
            log.error("project does not exist.");
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        detailVo.setId(projectEntity.getId());
        detailVo.setName(projectEntity.getName());
        detailVo.setAreaId(projectEntity.getAreaId());
        ProjectAreaVo projectArea = projectAreaDomainService.getProjectArea(projectEntity.getAreaId());
        log.info("projectArea={}",projectArea);
        if(projectArea!=null){
            detailVo.setAreaName(projectArea.getAreaName());
        }
        detailVo.setProjectStage(projectEntity.getProjectStage());
        detailVo.setProjectStageName(ProjectStageEnum.getValueByKey(projectEntity.getProjectStage()));
        detailVo.setCustomer(projectEntity.getCustomer());
        detailVo.setNotes(projectEntity.getNotes());

        ResourceVo resourceVo = systemService.getResourceById(id);
        log.debug("systemService getResourceById resourceVo:{}",resourceVo);
        if (isResourceVoValid(resourceVo)){
            Map<String,List<UserDto>> roleMap = new HashMap<>();
            Map<String, List<UserVo>> userRoleMap = resourceVo.getUserRoleMap();
            for (Map.Entry<String, List<UserVo>> entry : userRoleMap.entrySet()) {
                String roleCode = entry.getKey();
                List<UserDto> managerVoList = entry.getValue().stream()
                        .map(this::getManagerVo)
                        .collect(Collectors.toList());
                roleMap.put(roleCode, managerVoList);
            }
            // 方案SE
            List<UserDto> schemeSeList = roleMap.getOrDefault(
                    RoleCodeEnum.SCHEME_SE.getCode(),
                    Collections.emptyList()
            );
            if (CollectionUtils.isNotEmpty(schemeSeList)) {
                detailVo.setSchemeSE(convertToProjectUserDto(schemeSeList));
            } else {
                detailVo.setSchemeSE(new ArrayList<>());
            }

        }
        return detailVo;
    }

    // convertToProjectUserDto方法实现
    private List<ProjectUserDto> convertToProjectUserDto(List<UserDto> userDtoList) {
        if (CollectionUtils.isEmpty(userDtoList)) {
            return Collections.emptyList(); // 返回空列表而不是null更安全
        }
        // 使用并行流来加速处理
        return userDtoList.parallelStream().map(item -> {
            ProjectUserDto projectUserDto = new ProjectUserDto();
            projectUserDto.setId(item.getId());
            projectUserDto.setName(item.getName());
            projectUserDto.setEmail(item.getEmail());
            projectUserDto.setEmployeeId(item.getEmployeeId());
            projectUserDto.setPhoneNumber(item.getPhoneNumber());
            return projectUserDto;
        }).collect(Collectors.toList());
    }
    /* Ended by AICoder, pid:x073c122e1y90971428608d7807b882e3b84b284 */

    /**
     * 构建用户角色DTO。
     *
     * @param code 角色代码。
     * @param managers 管理员列表。
     * @return 用户角色DTO列表。
     */
    private List<UserRoleDto> buildUserRoleDto(String code, List<UserDto> managers) {
        List<UserRoleDto> userRoleDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(managers)) {
            UserRoleDto userRoleDto = new UserRoleDto();
            userRoleDto.setRoleCode(code);
            userRoleDtoList.add(userRoleDto);
        }else {
            for (UserDto manager : managers) {
                UserRoleDto userRoleDto = new UserRoleDto();
                userRoleDto.setId(manager.getId());
                userRoleDto.setRoleCode(code);
                userRoleDto.setName(manager.getName());
                userRoleDto.setPhoneNumber(manager.getPhoneNumber());
                userRoleDto.setEmail(manager.getEmail());
                userRoleDto.setEmployeeId(manager.getEmployeeId());
                userRoleDtoList.add(userRoleDto);
            }
        }
        return userRoleDtoList;
    }

    /**
     * 构建资源实体DTO。
     *
     * @param id 资源ID。
     * @return 资源实体DTO。
     */
    private ResourceEntityDto buildResourceEntityDto(String id){
        ResourceEntityDto resourceEntityDto = new ResourceEntityDto();
        resourceEntityDto.setEntityId(id);
        resourceEntityDto.setType(GlobalConstants.PROJECT_TYPE);
        return resourceEntityDto;
    }

    /* Started by AICoder, pid:mcba6o30d423b92142fd0afea059f523055002b1 */
    /**
     * 将用户列表连接成字符串。
     *
     * @param userList 用户列表。
     * @return 连接后的字符串。
     */
    private String concatenateNames(List<UserDto> userList) {
        if (userList == null || userList.isEmpty()) {
            return "";
        }
        return userList.stream()
                .map(user -> {
                    // 使用 Optional 简化 null 处理和 trim 操作
                    String name = Optional.ofNullable(user.getName()).map(String::trim).orElse("");
                    String employeeId = Optional.ofNullable(user.getEmployeeId()).map(String::trim).orElse("");
                    return name + employeeId; // 直接拼接字符串
                })
                .filter(combined -> !combined.isEmpty()) // 过滤空结果
                .collect(Collectors.joining(","));
    }
    /* Ended by AICoder, pid:mcba6o30d423b92142fd0afea059f523055002b1 */

    /**
     * 检查资源VO是否有效。
     *
     * @param resourceVo 资源VO。
     * @return 是否有效。
     */
    private boolean isResourceVoValid(ResourceVo resourceVo) {
        return resourceVo != null && resourceVo.getUserRoleMap() != null && !resourceVo.getUserRoleMap().isEmpty();
    }

    /**
     * 构建管理者VO。
     *
     * @param userVo 用户VO。
     * @return 管理者VO。
     */
    private UserDto getManagerVo(UserVo userVo) {
        UserDto managerVo = new UserDto();
        managerVo.setId(userVo.getId());
        managerVo.setName(userVo.getName());
        managerVo.setEmail(userVo.getEmail());
        managerVo.setEmployeeId(userVo.getEmployeeId());
        return managerVo;
    }

    /**
     * 创建项目参数。
     *
     * @param projectEntity 项目实体。
     * @param isCreate 是否为创建操作。
     */
    private void createParam(ProjectEntity projectEntity, boolean isCreate) {
        // 获取当前用户ID和时间
        UacUserInfoDto uacUser = authService.getCurUacUser();
        log.debug("uacUserInfo = {}", uacUser);
        String currentTime = DateTimeUtils.getCurrentTime();

        projectEntity.setUpdateBy(uacUser.getId());
        projectEntity.setUpdateTime(currentTime);

        UserVo userVo = systemService.getUserinfoById(uacUser.getId());

        // 如果是创建操作，额外设置创建相关字段
        if (isCreate) {
            // 设置唯一ID、创建人和创建时间
            projectEntity.setId(UUID.randomUUID().toString());
            projectEntity.setCreateBy(uacUser.getId());
            projectEntity.setCreateName(userVo.getName() + uacUser.getId());
            projectEntity.setCreateTime(currentTime);
        }
    }

    /**
     * 构建区域树。
     *
     * @param areaTreeVoList 区域树VO列表。
     * @return 构建后的区域树列表。
     */
    private List<AreaTreeVo> buildTree(List<AreaTreeVo> areaTreeVoList){
        List<ProjectVo> projectVos = selectProject();
        Map<String, List<ProjectVo>> areaProjectMap = projectVos.stream().collect(Collectors.groupingBy(ProjectVo::getAreaId));
        if (areaTreeVoList == null || areaTreeVoList.isEmpty()) {
            return Collections.emptyList();
        }
        // 1. 创建一个映射，将每个节点的ID映射到其对应的节点对象
        Map<String, AreaTreeVo> idToNodeMap = new HashMap<>();
        for (AreaTreeVo node : areaTreeVoList) {
            idToNodeMap.put(node.getId(), node);
        }
        List<AreaTreeVo> rootNodes = new ArrayList<>();
        for (AreaTreeVo node : areaTreeVoList) {
            String parentId = node.getParentId();
            Integer areaLevel = node.getAreaLevel();
            if (areaLevel.equals(GlobalConstants.ONE)) {
                // 如果节点等级为1，则将其添加到根节点列表中
                rootNodes.add(node);
            }else {
                AreaTreeVo areaTreeVo = idToNodeMap.get(parentId);
                if(areaTreeVo != null){
                    if (areaTreeVo.getChildren() == null) {
                        areaTreeVo.setChildren(new ArrayList<>());
                    }
                    // 将当前节点添加到父节点的子节点列表中
                    areaTreeVo.getChildren().add(node);
                }
                List<ProjectVo> projectVoList = areaProjectMap.get(node.getId());
                if (projectVoList != null) {
                    List<AreaTreeVo> areaTreeOppoVos = buildAreaTreeOppoVos(projectVoList);
                    node.setChildren(areaTreeOppoVos);
                }
            }
        }
        // 返回根节点列表
        rootNodes.sort((o1, o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getAreaName(), o2.getAreaName()));
        rootNodes.forEach(this::sort);
        return rootNodes;
    }

    private void sort(AreaTreeVo node) {
        List<AreaTreeVo> children = node.getChildren();
        if (children != null && !children.isEmpty()) {
            children.sort((o1, o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getAreaName(), o2.getAreaName()));
            for (AreaTreeVo child : children) {
                sort(child);
            }
        }
    }

    private List<AreaTreeVo> buildAreaTreeOppoVos(List<ProjectVo> projectVoList) {
        List<AreaTreeVo> areaTreeOppoVos = new ArrayList<>();
        for (ProjectVo projectVo : projectVoList) {
            AreaTreeVo areaTreeVo = new AreaTreeVo();
            areaTreeVo.setId(projectVo.getId());
            areaTreeVo.setAreaName(projectVo.getName());
            areaTreeVo.setOppo(true);
            areaTreeOppoVos.add(areaTreeVo);
        }
        return areaTreeOppoVos;
    }

    /**
     * 构建区域项目树。
     *
     * @param allAreas 所有区域。
     * @param matchedProjects 匹配的项目。
     * @return 构建后的区域项目树列表。
     */
    private List<AreaProjectTreeVo> areaProjectBuildTree(List<ProjectAreaObj> allAreas, List<ProjectEntity> matchedProjects,Boolean isQuery) {
        // 转换所有地区的对象列表
        List<AreaProjectTreeVo> allAreasVo = ProjectConvert.INSTANCE.objListToProjectVo(allAreas);
        List<ProjectObj> matchedProjectObjs = ProjectConvert.INSTANCE.entityListToProjectObj(matchedProjects);

        // 创建一个Map来存储所有的地区节点，并初始化它们的子节点和项目列表
        Map<String, AreaProjectTreeVo> areaMap = initializeAreaMap(allAreasVo);

        // 构建完整的地区树
        buildAreaTree(allAreasVo, areaMap);

        // 收集所有相关的地区ID（基于匹配的项目）
        Set<String> matchedAreaIds = collectMatchedAreaIds(matchedProjectObjs, areaMap);

        // 构建最终的树形结构
        return buildResultTree(matchedAreaIds, areaMap,isQuery);
    }

    /**
     * 初始化区域Map。
     *
     * @param allAreasVo 所有区域VO列表。
     * @return 初始化后的区域Map。
     */
    // 初始化地区Map
    private Map<String, AreaProjectTreeVo> initializeAreaMap(List<AreaProjectTreeVo> allAreasVo) {
        Map<String, AreaProjectTreeVo> areaMap = new HashMap<>();
        for (AreaProjectTreeVo area : allAreasVo) {
            area.setChildren(new ArrayList<>());
            areaMap.put(area.getId(), area);
        }
        return areaMap;
    }

    /**
     * 构建完整的区域树。
     *
     * @param allAreasVo 所有区域VO列表。
     * @param areaMap 区域Map。
     */
    // 构建完整的地区树
    private void buildAreaTree(List<AreaProjectTreeVo> allAreasVo, Map<String, AreaProjectTreeVo> areaMap) {
        for (AreaProjectTreeVo area : allAreasVo) {
            if (area.getParentId() != null && areaMap.containsKey(area.getParentId())) {
                areaMap.get(area.getParentId()).getChildren().add(area);
            }
        }
    }

    /**
     * 收集所有相关的地区ID。
     *
     * @param matchedProjectObjs 匹配的项目对象列表。
     * @param areaMap 区域Map。
     * @return 收集到的地区ID集合。
     */
    // 收集所有相关的地区ID
    private Set<String> collectMatchedAreaIds(List<ProjectObj> matchedProjectObjs, Map<String, AreaProjectTreeVo> areaMap) {
        log.debug("matchedProjectObjs = {}",matchedProjectObjs);
        Set<String> matchedAreaIds = new HashSet<>();
        // 收集匹配项目的ID及其父级ID
        for (ProjectObj project : matchedProjectObjs) {
            String areaId = project.getAreaId();
            if(areaId != null && areaMap.containsKey(areaId)){
                matchedAreaIds.add(areaId);
                AreaProjectTreeVo projectTreeVo = new AreaProjectTreeVo();
                projectTreeVo.setId(project.getId());
                projectTreeVo.setName(project.getName());
                projectTreeVo.setParentId(project.getAreaId());
                projectTreeVo.setProject(true);
                areaMap.get(project.getAreaId()).getChildren().add(projectTreeVo);
            }
            collectAncestors(areaId, matchedAreaIds, areaMap);
        }

        return matchedAreaIds;
    }


    /**
     * 收集单个节点及其祖先节点的ID。
     *
     * @param areaId 区域ID。
     * @param matchedAreaIds 匹配的地区ID集合。
     * @param areaMap 区域Map。
     */
    // 收集单个节点及其祖先节点的ID
    private void collectAncestors(String areaId, Set<String> matchedAreaIds, Map<String, AreaProjectTreeVo> areaMap) {
        AreaProjectTreeVo current = areaMap.get(areaId);
        while (current != null && !GlobalConstants.ROOT.equals(current.getParentId())) {
            matchedAreaIds.add(current.getParentId());
            current = areaMap.get(current.getParentId());
        }

    }

    /* Started by AICoder, pid:01d6cwa93crb07f1412b0a87c0345030998626bb */
    /**
     * 构建最终的树形结构。
     *
     * @param matchedAreaIds 匹配的地区ID集合。
     * @param areaMap 区域Map。
     * @param isQuery 是否进行查询过滤。
     * @return 最终的树形结构列表。
     */
    private List<AreaProjectTreeVo> buildResultTree(Set<String> matchedAreaIds, Map<String, AreaProjectTreeVo> areaMap, Boolean isQuery) {
        List<AreaProjectTreeVo> result = new ArrayList<>();
        log.debug("matchedAreaIds: {}", matchedAreaIds);
        log.debug("areaMap: {}", areaMap);

        for (String id : matchedAreaIds) {
            AreaProjectTreeVo area = areaMap.get(id);
            if (area != null) {
                if (area.getParentId().equals(GlobalConstants.ROOT)) {
                    // 根据isQuery决定是否过滤无关节点
                    AreaProjectTreeVo filteredArea = isQuery ? filterUnrelatedNodes(area, matchedAreaIds) : area;
                    if (filteredArea != null) {
                        result.add(filteredArea);
                    }
                } else {
                    AreaProjectTreeVo parent = areaMap.get(area.getParentId());
                    if (parent != null) {
                        // 确保子节点没有重复添加
                        if (!parent.getChildren().stream().anyMatch(child -> child.getId().equals(area.getId()))) {
                            parent.getChildren().add(area);
                        }
                    }
                }
            }
        }

        return result;
    }
    /* Ended by AICoder, pid:01d6cwa93crb07f1412b0a87c0345030998626bb */

    /* Started by AICoder, pid:efaffx74edc56e11490d0a1ba0c846381e30b006 */
    /**
     * 过滤掉无关的兄弟节点。
     *
     * @param node 当前节点。
     * @param matchedAreaIds 匹配的地区ID集合。
     * @return 过滤后的节点。
     */
    private AreaProjectTreeVo filterUnrelatedNodes(AreaProjectTreeVo node, Set<String> matchedAreaIds) {
        if (node == null || node.getChildren() == null) {
            return node;
        }
        // 过滤子节点，只保留匹配的地区或项目
        List<AreaProjectTreeVo> filteredChildren = node.getChildren().stream()
                .filter(child -> matchedAreaIds.contains(child.getId()) || child.isProject())
                .map(child -> filterUnrelatedNodes(child, matchedAreaIds))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 更新当前节点的子节点
        node.setChildren(filteredChildren);

        // 如果当前节点没有子节点且不是匹配地区，则移除
        if (filteredChildren.isEmpty() && !matchedAreaIds.contains(node.getId()) && !node.isProject()) {
            return null;
        }

        return node;
    }

    @Override
    public void updateLaunchBidById(LaunchBidDto launchBidDto) {
        LaunchBiddingEntity launchBiddingEntity = launchBiddingRepository.queryById(launchBidDto.getId());
        if (launchBiddingEntity == null) {
            log.warn("The LaunchBidding does not exist, launchBidDto: {}", launchBidDto);
            return;
        }
        String userId = authService.getUserId();
        String currentTime = DateTimeUtils.getCurrentTime();
        launchBiddingEntity.supplementModifiedInfo(userId,currentTime);
        //配置锁单时间
        log.debug("Update the task completion time launchBidDto: {}", launchBidDto);
        if (StringUtils.isNotBlank(launchBidDto.getConfigureManifestLockTime())) {
            launchBiddingEntity.setConfigureManifestLockTimeCompleteTime(launchBidDto.getConfigureManifestLockTime());
        }
        //澄清提交时间
        if (StringUtils.isNotBlank(launchBidDto.getClarifySubmissionTime())) {
            launchBiddingEntity.setClarifySubmissionCompleteTime(launchBidDto.getClarifySubmissionTime());
        }
        //招标文件定稿时间
        if (StringUtils.isNotBlank(launchBidDto.getBiddingDocumentsFinalizationTime())) {
            launchBiddingEntity.setBiddingDocumentsFinalizationCompletionTime(launchBidDto.getBiddingDocumentsFinalizationTime());
        }
        launchBiddingRepository.saveOrUpdate(launchBiddingEntity,true);
    }

    @Override
    public List<String> queryProjectIdByAreaId(String id) {
        List<AreaTreeVo> areaTreeVos = selectAreaTree(null);
        if (CollectionUtils.isEmpty(areaTreeVos)) {
            return Collections.emptyList();
        }
        List<String> ids = new ArrayList<>();
        if (StringUtils.isBlank(id)) {
            areaTreeVos.forEach(p->collectIds(p, ids));
            return ids;
        }
        return getSubTreeIds(id,areaTreeVos);
    }
    private void collectIds(AreaTreeVo node, List<String> ids) {
        // Add current node's id to the list
        if (node.isOppo()) {
            ids.add(node.getId());
        }
        // Recursively add children nodes' ids
        List<AreaTreeVo> children = node.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            for (AreaTreeVo child : children) {
                collectIds(child, ids);
            }
        }

    }
    // 获取指定节点及其子节点ID
    public List<String> getSubTreeIds(String targetId, List<AreaTreeVo> tree) {
        AreaTreeVo targetNode = findNode(tree, targetId);
        if (targetNode == null) {
            return Collections.emptyList();
        }
        List<String> ids = new ArrayList<>();
        collectIds(targetNode, ids);
        return ids;
    }

    // 递归查找节点
    private AreaTreeVo findNode(List<AreaTreeVo> nodes, String targetId) {
        if (CollectionUtils.isEmpty(nodes)) {
            return null;
        }
        for (AreaTreeVo node : nodes) {
            if (node.getId().equals(targetId)) {
                return node;
            }
            AreaTreeVo found = findNode(node.getChildren(), targetId);
            if (found != null) {
                return found;
            }
        }
        return null;
    }
    @Override
    public LaunchBiddingInnerVo queryLaunchBidById(String projectId) {
        LaunchBiddingInnerVo launchBiddingInnerVo=new LaunchBiddingInnerVo();
        LaunchBiddingVo launchBiddingVo=queryBiddingInformationById(projectId);
        log.info("launchBiddingVo = {}", launchBiddingVo);
        BeanUtils.copyProperties(launchBiddingVo,launchBiddingInnerVo);
        log.info("launchBiddingInnerVo={}",launchBiddingInnerVo);
        return launchBiddingInnerVo;
    }

    @Override
    public List<ProjectProductSupportsVo> queryProjectProductSupport(String projectId, List<String> productCategoryIds) {
        log.debug("Search for product support SE for the business opportunity product subcategory, projectId = {}, productCategoryIds = {}", projectId, productCategoryIds);
        if (StringUtils.isBlank(projectId) || CollectionUtils.isEmpty(productCategoryIds)) {
            log.error("Search for product support SE for product subcategory, with empty business opportunity ID or empty product subcategory");
            return Collections.emptyList();
        }
        //根据商机id及产品小类查询商机支持SE信息
        List<OpportunitySupportEntity> supportEntityList = opportunitySupportRepository.querySupportsByProjectAndCategory(projectId,productCategoryIds);
        if (CollectionUtils.isEmpty(supportEntityList)) {
            log.error("Search for product subcategories with product support SE empty");
            return Collections.emptyList();
        }
        log.debug("Search for product support SE for product subcategories:{}",supportEntityList);
        List<String> userIds = supportEntityList.stream().map(OpportunitySupportEntity::getSupportStaffId).distinct().collect(Collectors.toList());
        //获取用户信息
        Map<String, UserVo> userVoMap = systemService.getUserinfoByIds(userIds).stream().collect(Collectors.toMap(UserVo::getId, Function.identity()));
        return supportEntityList.stream().map(p -> convertProjectProductSupportsVo(p.getProductSubId(), userVoMap.get(p.getSupportStaffId()))).collect(Collectors.toList());
    }

    @Override
    public List<LaunchBiddingInnerVo> queryLaunchBidListByIds(List<String> projectIds) {
        List<LaunchBiddingEntity> launchBiddingEntityList = launchBiddingRepository.queryLaunchBidListByIds(projectIds);
        if (CollectionUtils.isEmpty(launchBiddingEntityList)) {
            return Collections.emptyList();
        }

        return launchBiddingEntityList.stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());
    }

    private ProjectProductSupportsVo convertProjectProductSupportsVo(String productSubId,UserVo userVo) {
        ProjectProductSupportsVo supportsVo = new ProjectProductSupportsVo();
        supportsVo.setProductCategoryId(productSubId);
        supportsVo.setSystemEngineer(userVo);
        return supportsVo;
    }
    // 转换方法：Entity -> InnerVo
    private LaunchBiddingInnerVo convertToVo(LaunchBiddingEntity entity) {
        LaunchBiddingInnerVo vo = new LaunchBiddingInnerVo();
        vo.setId(entity.getId());
        vo.setBidIssuingTime(entity.getBidIssuingTime());
        vo.setClarifySubmissionTime(entity.getClarifySubmissionTime());
        vo.setClarifySubmissionCompleteTime(entity.getClarifySubmissionCompleteTime());
        vo.setConfigureManifestLockTime(entity.getConfigureManifestLockTime());
        vo.setConfigureManifestLockTimeCompleteTime(entity.getConfigureManifestLockTimeCompleteTime());
        vo.setBiddingDocumentsFinalizationTime(entity.getBiddingDocumentsFinalizationTime());
        vo.setBiddingDocumentsFinalizationCompletionTime(entity.getBiddingDocumentsFinalizationCompletionTime());
        vo.setBidSubmissionTime(entity.getBidSubmissionTime());

        return vo;
    }
}
/* Ended by AICoder, pid:k057bp3499d40181408e0a5046d5673716b60e4e */
