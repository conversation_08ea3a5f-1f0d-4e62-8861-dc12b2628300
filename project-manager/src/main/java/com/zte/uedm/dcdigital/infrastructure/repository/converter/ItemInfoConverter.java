/* Started by AICoder, pid:08b94r03464ce0914aaf083d905cf529f1b05b92 */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.domain.aggregate.model.ItemInfoEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectDeliveryEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectDeliveryHistoryEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectLegacyIssuesEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ItemInfoPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectDeliveryHistoryPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectDeliveryPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectLegacyIssuesPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ItemInfoConverter {

    // 使用常量 INSTANCE 以便通过 Mappers 工厂获取单例实例
    ItemInfoConverter INSTANCE = Mappers.getMapper(ItemInfoConverter.class);

    @Mappings({})
    ItemInfoEntity poToEntity(ItemInfoPo itemInfoPo);

    @Mappings({})
    ItemInfoPo entityToPo(ItemInfoEntity itemInfoEntity);

    @Mappings({})
    List<ItemInfoEntity> listPoToEntity(List<ItemInfoPo> itemInfoPos);



    @Mappings({})
    ProjectLegacyIssuesEntity legacyIssuesPoToEntity(ProjectLegacyIssuesPo legacyIssuesPo);

    @Mappings({})
    ProjectLegacyIssuesPo legacyIssuesEntityToPo(ProjectLegacyIssuesEntity legacyIssuesEntity);

    @Mappings({})
    List<ProjectLegacyIssuesEntity> legacyIssuesPosToEntity(List<ProjectLegacyIssuesPo> legacyIssuesPos);

    @Mappings({})
    ProjectDeliveryHistoryEntity deliveryHistoryPoToEntity(ProjectDeliveryHistoryPo deliveryHistoryPo);

    @Mappings({})
    ProjectDeliveryHistoryPo deliveryHistoryEntityToPo(ProjectDeliveryHistoryEntity deliveryHistoryEntity );

    @Mappings({})
    List<ProjectDeliveryHistoryEntity> deliveryHistoryPoListToEntity(List<ProjectDeliveryHistoryPo> deliveryHistoryPoList);

    @Mappings({})
    ProjectDeliveryEntity deliveryPoToEntity(ProjectDeliveryPo deliveryPo);

    @Mappings({})
    ProjectDeliveryPo deliveryEntityToPo(ProjectDeliveryEntity deliveryEntity );
}

/* Ended by AICoder, pid:08b94r03464ce0914aaf083d905cf529f1b05b92 */