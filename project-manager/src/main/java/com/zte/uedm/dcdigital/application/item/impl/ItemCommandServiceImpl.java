package com.zte.uedm.dcdigital.application.item.impl;

import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.JsonUtils;
import com.zte.uedm.component.kafka.producer.constants.KafkaTopicOptional;
import com.zte.uedm.component.kafka.producer.service.KafkaSenderService;
import com.zte.uedm.dcdigital.application.item.ItemCommandService;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.service.ItemDomainService;
import com.zte.uedm.dcdigital.domain.utils.OperationLogUtils;
import com.zte.uedm.dcdigital.interfaces.web.dto.LegacyIssuesAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.LegacyIssuesEditDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectDeliveryDto;
import com.zte.uedm.dcdigital.log.domain.bean.OperationLogBean;
import com.zte.uedm.dcdigital.log.domain.bean.OperationResultOptional;
import com.zte.uedm.dcdigital.log.domain.bean.OperationTypeOptional;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationLogRankEnum;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class ItemCommandServiceImpl implements ItemCommandService {


    @Autowired
    private ItemDomainService itemDomainService;

    @Autowired
    private KafkaSenderService kafkaSenderService;

    @Autowired
    private AuthService authService;


    @Override
    public void editDelivery(ProjectDeliveryDto deliveryDto) {
        String result = OperationResultOptional.OPERATION_RESULT_SUCCESS.getId();
        try {
            itemDomainService.editDelivery(deliveryDto);
            senLogMessage(deliveryDto,StringUtils.EMPTY,result,GlobalConstants.UPDATE_DELIVERY,OperationTypeOptional.OPERATION_TYPE_UPDATE);
        } catch (Exception e) {
            result = OperationResultOptional.OPERATION_RESULT_FAIL.getId();
            senLogMessage(deliveryDto,e.getMessage(),result,GlobalConstants.UPDATE_DELIVERY,OperationTypeOptional.OPERATION_TYPE_UPDATE);
            log.error("editDelivery error.",e);
            throw e;
        }
    }

    @Override
    public void addLegacyIssues(LegacyIssuesAddDto issuesAddDto) {
        itemDomainService.addLegacyIssues(issuesAddDto);
    }

    @Override
    public void editLegacyIssues(LegacyIssuesEditDto issuesEditDto) {
        itemDomainService.editLegacyIssues(issuesEditDto);
    }

    @Override
    public void deleteLegacyIssues(String id) {
        itemDomainService.deleteLegacyIssues(id);
    }

    private void senLogMessage(Object logMsg, String errorMessage, String result,String operation,OperationTypeOptional operationType) {
        try{
            String userId = authService.getUserId();
            OperationLogBean operationLogBean = OperationLogUtils.buildOperationLogBean(operation, operationType,
                    userId, result, logMsg, OperationLogRankEnum.IMPORTANT.getId());
            log.info("operation log:{}",operationLogBean);
            if (OperationResultOptional.OPERATION_RESULT_SUCCESS.getId().equals(result)) {
                sendKafkaMessage(operationLogBean);
            } else {
                sendFailLogMessage(operationLogBean, errorMessage);
            }
        } catch (Exception e) {
            log.error("send log message failed", e);
        }
    }

    private void sendFailLogMessage(OperationLogBean operationLogBean, String errorMessage) {
        String[] args = new String[]{errorMessage};
        operationLogBean.setFailReason(I18nUtil.getI18nWithArgs(GlobalConstants.OPERATION_FAIL_LOG, args));
        sendKafkaMessage(operationLogBean);
    }

    private void sendKafkaMessage(OperationLogBean operationLogBean) {
        try {
            log.debug("batch operation, :operationLogBean={}!", operationLogBean);
            kafkaSenderService.send(KafkaTopicOptional.KAFKA_TOPIC_OTCP_LOG_MANAGE, operationLogBean);
        } catch (Exception e) {
            log.error("log Message send failed for ", e);
        }
    }

}
