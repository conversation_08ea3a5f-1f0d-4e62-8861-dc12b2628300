package com.zte.uedm.dcdigital.application.projectArea.executor;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAreaQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProjectAreaVo;

public interface ProjectAreaQueryService {

    ProjectAreaVo getById(String id);

    PageVO<ProjectAreaVo> queryByCondition(ProjectAreaQueryDto queryDto);
}
