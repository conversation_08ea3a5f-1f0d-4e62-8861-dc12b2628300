/* Started by AICoder, pid:t09eft144a87d2414211091980b21747ffc6c8fd */
package com.zte.uedm.dcdigital.application.biddingdocument.executor.impl;

import com.zte.uedm.dcdigital.application.biddingdocument.executor.BiddingDocumentClarificationQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.service.BiddingDocumentClarificationDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingDocumentClarificationDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingDocumentClarificationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 提供了对标书澄清信息的查询服务。
 */
@Service
public class BiddingDocumentClarificationQueryServiceImpl implements BiddingDocumentClarificationQueryService {

    /**
     * 注入 BiddingDocumentClarificationDomainService 服务，用于处理底层业务逻辑。
     */
    @Autowired
    private BiddingDocumentClarificationDomainService biddingDocumentClarificationDomainService;

    /**
     * 根据查询条件查询标书澄清信息列表。
     *
     * @param queryDto 查询条件数据传输对象
     * @return 包含分页信息和结果列表的 PageVO 对象
     */
    @Override
    public PageVO<BiddingDocumentClarificationVo> queryListByCondition(BiddingDocumentClarificationQueryDto queryDto) {
        return biddingDocumentClarificationDomainService.queryListByCondition(queryDto);
    }

    /**
     * 根据 ID 查询单个标书澄清信息的详细信息。
     *
     * @param id 要查询的标书澄清信息的 ID
     * @return 包含详细信息的 BiddingDocumentClarificationDetailVo 对象
     */
    @Override
    public BiddingDocumentClarificationDetailVo queryDetailById(String id) {
        return biddingDocumentClarificationDomainService.queryDetailById(id);
    }
}

/* Ended by AICoder, pid:t09eft144a87d2414211091980b21747ffc6c8fd */