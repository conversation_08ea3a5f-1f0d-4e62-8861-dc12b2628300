package com.zte.uedm.dcdigital.interfaces.web.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@ToString
public class PageQueryDto {
    @NotBlank(message = "The project area name cannot be empty")
    private String id;
    //项目id
    private String itemId;
    private Integer pageNum;
    private Integer pageSize;
    public void verification() throws BusinessException {
        ValidResult validResult = ValidateUtils.validateObj(this);
        // 参数不能为空
        if (validResult.isError()) {
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
        if (pageNum == null || pageSize == null) {
            pageNum = 1;
            pageSize = 10;
        }
    }
}
