package com.zte.uedm.dcdigital.interfaces.web.vo;

/* Started by AICoder, pid:e71f663a791759d1418a0939300b6951d228c830 */
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 标书澄清响应VO类。
 */
@Setter
@Getter
@ToString
public class BiddingDocumentClarificationVo {

    /**
     * 标书澄清ID。
     */
    private String id;

    /**
     * 产品小类ID。
     */
    private String productCategoryId;

    /**
     * 文件名称。
     */
    private String fileName;

    /**
     * 章节号。
     */
    private String chapterNumber;

    /**
     * 所在页码。
     */
    private String pageNumber;

    /**
     * 招标文件原文。
     */
    private String fileContent;

    /**
     * 澄清问题。
     */
    private String clarification;

    /**
     * 创建人id。
     */
    private String createBy;

    /**
     * 创建人姓名。
     */
    private String createUserName;

    /**
     * 创建时间。
     */
    private String createTime;

    /**
     * 更新人id。
     */
    private String updateBy;

    /**
     * 更新人姓名。
     */
    private String updateUserName;

    /**
     * 更新时间。
     */
    private String updateTime;
    /**
     * 编辑、删除按钮展示权限：true:展示，false：不展示。
     */
    private boolean buttonPermission;
}

/* Ended by AICoder, pid:e71f663a791759d1418a0939300b6951d228c830 */

