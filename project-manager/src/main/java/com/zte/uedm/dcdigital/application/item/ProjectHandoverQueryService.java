package com.zte.uedm.dcdigital.application.item;

import com.zte.uedm.dcdigital.interfaces.web.vo.ProjectHandoverVo;

public interface ProjectHandoverQueryService {
    /**
     *  根据商机id查询项目交接信息
     * */
    ProjectHandoverVo getProjectHandoverDetail(String projectId);

    /**
     *  根据项目id查询项目交接信息
     * */
    ProjectHandoverVo queryProjectHandoverDetailByItemId(String id);

    /**
     * 根据项目id查询项目进度信息
     * */
    ProjectHandoverVo queryItemProgressInfo(String id);
}
