/* Started by AICoder, pid:1c7114771bx2bc4145300a6b005d0c5633f1594e */
package com.zte.uedm.dcdigital.application.biddingdocument.executor;

import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductTreeVo;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationEditDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationQueryDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * BiddingDocumentClarificationCommandService 接口定义了与招标文件澄清相关的命令操作。
 */
public interface BiddingDocumentClarificationCommandService {

    /**
     * 添加新的招标文件澄清信息。
     *
     * @param addDto 包含新增澄清信息的数据传输对象
     */
    void add(BiddingDocumentClarificationAddDto addDto);

    /**
     * 编辑现有的招标文件澄清信息。
     *
     * @param editDto 包含编辑后的澄清信息的数据传输对象
     */
    void edit(BiddingDocumentClarificationEditDto editDto);

    /**
     * 根据ID删除招标文件澄清信息。
     *
     * @param id 要删除的澄清信息的唯一标识符
     */
    void deleteById(String id);

    /**
     * 导出指定项目的招标文件澄清信息。
     *
     * @param dto 查询条件
     * @param response  HTTP响应对象，用于返回导出的文件
     */
    void export(BiddingDocumentClarificationQueryDto dto, HttpServletResponse response);

    /**
     * 查询指定项目的相关产品类别信息。
     *
     * @param projectId 项目标识符
     * @return 产品类别信息列表
     */
    List<ProductCategoryInfoVo> queryProductCategory(String projectId,boolean flag);

    List<ProductTreeVo> queryProductCategoryTree(String projectId, boolean flag);
}

/* Ended by AICoder, pid:1c7114771bx2bc4145300a6b005d0c5633f1594e */