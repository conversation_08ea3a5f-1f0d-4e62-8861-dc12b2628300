/* Started by AICoder, pid:o8d8c7d110of01514d3b08895180860ce9657fbf */
package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.projectArea.executor.BrandGuideCommandService;
import com.zte.uedm.dcdigital.application.projectArea.executor.BrandGuideQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.dto.BrandGuideAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BrandGuideDataAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BrandGuideDataQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BrandGuideDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BrandGuideProjectPhaseVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProductCategoryBaseInfoVo;
import com.zte.uedm.dcdigital.log.annotation.DcOperationLog;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import com.zte.uedm.dcdigital.security.annotation.DcPermission;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("/uportal/brand-guide")
@Api(value = "BrandGuideController")
@Controller
@Slf4j
public class BrandGuideUporatlController {

    @Autowired
    private BrandGuideQueryService brandGuideQueryService;

    @Autowired
    private BrandGuideCommandService brandGuideCommandService;

    @POST
    @Path("/get-project-phase")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "项目阶段数据查询", notes = "项目阶段数据查询", httpMethod = "POST")
    public BaseResult<Object> getProjectPhase(BrandGuideDataQueryDto queryDto) {
        List<BrandGuideProjectPhaseVo> voList = brandGuideQueryService.getProjectPhase(queryDto);
        return BaseResult.success(voList);
    }

    @POST
    @Path("/list")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取产品小类、主推品牌列表", notes = "获取产品小类、主推品牌列表", httpMethod = "POST")
    public BaseResult<Object> getProductSubcategoriesAndMainBrands(BrandGuideDataQueryDto queryDto) {
        PageVO<BrandGuideDetailVo> voList = brandGuideQueryService.getProductSubcategoriesAndMainBrands(queryDto);
        return BaseResult.success(voList);
    }

    @POST
    @Path("/get-record")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "开始记录(查询)", notes = "开始记录(查询)", httpMethod = "POST")
    public BaseResult<Object> getRecord(BrandGuideDataQueryDto queryDto) {
        List<BrandGuideDetailVo> voList = brandGuideQueryService.getRecord(queryDto);
        return BaseResult.success(voList);
    }

    @POST
    @Path("/get-boot-details")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "引导详情查询", notes = "引导详情查询", httpMethod = "POST")
    public BaseResult<Object> getBootDetails(BrandGuideDataQueryDto queryDto) {
        BrandGuideDetailVo brandGuideDetailVo = brandGuideQueryService.getBootDetails(queryDto);
        return BaseResult.success(brandGuideDetailVo);
    }

    @POST
    @Path("/list-product-category")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "新选产品小类(查询)", notes = "新选产品小类(查询)", httpMethod = "POST")
    public BaseResult<Object> listProductCategory(List<String> productCategoryIds) {
        List<ProductCategoryBaseInfoVo>  categoryInfoVos=brandGuideQueryService.listProductCategory(productCategoryIds);
        return BaseResult.success(categoryInfoVos);
    }

    @POST
    @Path("/submit")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "提交(暂存)", notes = "提交(暂存)", httpMethod = "POST")
    @DcPermission(value = {"project.brandGuide.submit"},checkResource = true)
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-project-manager",
            operation = "BrandGuideAddDataDtoDescription", targetClass = BrandGuideDataAddDto.class)
    public BaseResult<Object> submit(BrandGuideDataAddDto addDto) {
        brandGuideCommandService.addBrandGuideData(addDto);
        return BaseResult.success();
    }

    @POST
    @Path("/get-history")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "引导历史查询", notes = "引导历史查询", httpMethod = "POST")
    public BaseResult<Object> getHistory(BrandGuideDataQueryDto queryDto) {
        List<BrandGuideProjectPhaseVo> phaseVo=brandGuideQueryService.getHistory(queryDto);
        return BaseResult.success(phaseVo);
    }

    @POST
    @Path("/get-history-detail")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "引导历史明细查询", notes = "引导历史明细查询", httpMethod = "POST")
    public BaseResult<Object> getHistoryDetail(BrandGuideDataQueryDto queryDto) {
        PageVO<BrandGuideDetailVo> guideDetailVos=brandGuideQueryService.getHistoryDetail(queryDto);
        return BaseResult.success(guideDetailVos);
    }


    @POST
    @Path("/add")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "产品需求看板处新增品牌引导记录", notes = "产品需求看板处新增品牌引导记录", httpMethod = "POST")
    @DcPermission(value = {"project.brandGuide.submit"},checkResource = true)
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-project-manager",
            operation = "SingleBrandGuideAddDtoDescription", targetClass = BrandGuideAddDto.class)
    public BaseResult<Object> boardAddGuideBrand(BrandGuideAddDto guideAddDto) {
        guideAddDto.parameterVerification();
        brandGuideCommandService.boardAddGuideBrand(guideAddDto);
        return BaseResult.success();
    }
}

/* Ended by AICoder, pid:o8d8c7d110of01514d3b08895180860ce9657fbf */