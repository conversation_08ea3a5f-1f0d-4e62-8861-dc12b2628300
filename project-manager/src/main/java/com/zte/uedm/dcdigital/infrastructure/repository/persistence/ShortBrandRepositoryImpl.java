package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.dcdigital.domain.repository.ShortBrandRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ShortBrandMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ShortBrandPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class ShortBrandRepositoryImpl extends ServiceImpl<ShortBrandMapper, ShortBrandPo> implements ShortBrandRepository {
    @Override
    public void batchSave(List<ShortBrandPo> brandPoList) {
        this.saveBatch(brandPoList);
    }

    @Override
    public List<ShortBrandPo> queryByDetailIds(List<String> detailIds) {
        if (CollectionUtils.isEmpty(detailIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ShortBrandPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ShortBrandPo::getBrandGuideDetailId, detailIds);
        return baseMapper.selectList(queryWrapper);
    }
}
