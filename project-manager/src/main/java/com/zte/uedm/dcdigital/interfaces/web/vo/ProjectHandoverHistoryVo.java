/* Started by AICoder, pid:4a8bbk05c2dfa11141df0ad3c0e07a5bb7167539 */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ProjectHandoverHistoryVo {

    // 主键
    private String id;

    // 所属交接id
    private String handoverId;

    // 项目id
    private String itemId;

    // 交付TD
    private String deliverTd;

    // 首次交付时间
    private String firstHandoverTime;

    // 二次交付时间
    private String secondHandoverTime;

    // 配置锁定时间
    private String configureLockTime;

    // 交付周期
    private Integer handoverCycle;

    // 交付进度
    private Integer handoverProgress;

    // 交付状态
    private String handoverStatus;

    // 延期原因
    private String delayReason;

    // 创建用户
    private String createBy;

    // 创建时间
    private String createTime;

    // 更新用户
    private String updateBy;

    // 更新时间
    private String updateTime;
}

/* Ended by AICoder, pid:4a8bbk05c2dfa11141df0ad3c0e07a5bb7167539 */