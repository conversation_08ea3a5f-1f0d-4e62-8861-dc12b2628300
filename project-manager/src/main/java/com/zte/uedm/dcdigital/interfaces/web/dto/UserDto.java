package com.zte.uedm.dcdigital.interfaces.web.dto;

/* Started by AICoder, pid:lceae875f7ya0b2140ea0a4c700107350cd45277 */
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class UserDto {

    /**
     * 用户唯一标识
     */
    private String id;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 电子邮件地址。
     */
    private String email;

    /**
     * 工号
     */
    private String employeeId;

    /**
     * 电话号码
     */
    private String phoneNumber;
}
/* Ended by AICoder, pid:lceae875f7ya0b2140ea0a4c700107350cd45277 */
