/* Started by AICoder, pid:80f886f4ed586e31438b0bb4d026387274584813 */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProjectStatusCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * DTO类用于品牌引导公告牌查询，包含开始时间和结束时间的验证逻辑。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Slf4j
public class BrandGuideBillboardQueryDto {

    /**
     * 查询的开始时间。
     */
    private String startTime;

    /**
     * 查询的结束时间。
     */
    private String endTime;

    /**
     * 验证开始时间和结束时间的有效性。
     */
    public void validate() {
        // 确保开始时间和结束时间要么都提供，要么都不提供
        if (StringUtils.isNoneBlank(startTime) != StringUtils.isNoneBlank(endTime)) {
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }

        // 解析并验证时间字符串
        LocalDateTime start = parseAndValidateTime(startTime);
        LocalDateTime end = parseAndValidateTime(endTime);

        // 确保开始时间不晚于结束时间
        if (start.isAfter(end)) {
            log.error("Start time cannot be after end time");
            throw new BusinessException(ProjectStatusCode.TIME_INCORRECT);
        }

        // 确保时间范围不超过一年
        if (end.isAfter(start.plusYears(1))) {
            log.error("Time range cannot be more than one year");
            throw new BusinessException(ProjectStatusCode.TIME_RANGE_ERROR);
        }
    }

    /**
     * 解析并验证给定的时间字符串。
     *
     * @param timeStr 要解析的时间字符串
     * @return 解析后的LocalDateTime对象
     */
    private LocalDateTime parseAndValidateTime(String timeStr) {
        // 假设在调用此方法之前已确保timeStr不为空
        if (!DateTimeUtils.isValidDate(timeStr)) {
            log.error("{} is not a valid date", timeStr);
            throw new BusinessException(ProjectStatusCode.TIME_FORMAT_ERROR);
        }
        return DateTimeUtils.getLocalDateTime(timeStr);
    }
}
/* Ended by AICoder, pid:80f886f4ed586e31438b0bb4d026387274584813 */