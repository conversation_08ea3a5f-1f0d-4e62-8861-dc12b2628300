package com.zte.uedm.dcdigital.application.projectArea.executor;

import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAreaAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAreaUpdateDto;

public interface ProjectAreaCommandService {

    void addProjectArea(ProjectAreaAddDto projectAreaDto);

    void updateProjectArea(ProjectAreaUpdateDto updateDto);

    void deleteProjectArea(String id);
}
