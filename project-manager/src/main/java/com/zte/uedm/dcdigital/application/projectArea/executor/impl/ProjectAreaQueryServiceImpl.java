package com.zte.uedm.dcdigital.application.projectArea.executor.impl;

import com.zte.uedm.dcdigital.application.projectArea.executor.ProjectAreaQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.service.ProjectAreaDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAreaQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProjectAreaVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ProjectAreaQueryServiceImpl implements ProjectAreaQueryService {

    @Autowired
    private ProjectAreaDomainService projectAreaDomainService;
    @Override
    public ProjectAreaVo getById(String id) {
        return projectAreaDomainService.getProjectArea(id);
    }

    @Override
    public PageVO<ProjectAreaVo> queryByCondition(ProjectAreaQueryDto queryDto) {
        return projectAreaDomainService.queryProjectArea(queryDto);
    }
}
