/* Started by AICoder, pid:g9f3d00086ef21c1492708196039213e77f31882 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zte.uedm.dcdigital.domain.aggregate.model.OpportunitySupportEntity;
import com.zte.uedm.dcdigital.domain.repository.OpportunitySupportRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.OpportunitySupportConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.OpportunitySupportMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.OpportunitySupportPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.OpportunitySupportDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class OpportunitySupportRepositoryImpl implements OpportunitySupportRepository {

    @Resource
    private OpportunitySupportMapper opportunitySupportMapper;

    @Override
    public void addOpportunitySupport(OpportunitySupportDto supportDto) {
        OpportunitySupportPo supportPo=OpportunitySupportConvert.INSTANCE.dtoToPo(supportDto);
        opportunitySupportMapper.insert(supportPo);

    }

    @Override
    public void updateOpportunitySupport(OpportunitySupportDto supportDto) {
        OpportunitySupportPo supportPo=OpportunitySupportConvert.INSTANCE.dtoToPo(supportDto);
        opportunitySupportMapper.updateById(supportPo);
    }

    @Override
    public OpportunitySupportEntity getOpportunitySupport(OpportunitySupportDto supportDto) {
        LambdaQueryWrapper<OpportunitySupportPo> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(OpportunitySupportPo::getOpportunityId,supportDto.getOpportunityId());
        queryWrapper.eq(OpportunitySupportPo::getProductSubId,supportDto.getProductSubId());
        OpportunitySupportPo supportPo=opportunitySupportMapper.selectOne(queryWrapper);
        return OpportunitySupportConvert.INSTANCE.poToEntity(supportPo);
    }

    @Override
    public List<OpportunitySupportEntity> querySupportsByProjectAndCategory(String projectId, List<String> productCategoryIds) {
        if (CollectionUtils.isEmpty(productCategoryIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OpportunitySupportPo> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(OpportunitySupportPo::getOpportunityId,projectId);
        queryWrapper.in(OpportunitySupportPo::getProductSubId,productCategoryIds);
        return opportunitySupportMapper.selectList(queryWrapper).stream().map(OpportunitySupportConvert.INSTANCE::poToEntity).collect(Collectors.toList());
    }
}

/* Ended by AICoder, pid:g9f3d00086ef21c1492708196039213e77f31882 */