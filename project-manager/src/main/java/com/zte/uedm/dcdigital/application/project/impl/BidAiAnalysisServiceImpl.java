/* Started by AICoder, pid:048dem66b8a46f814c90092173e48b0c9af3a722 */
package com.zte.uedm.dcdigital.application.project.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.zte.uedm.dcdigital.application.project.BidAiAnalysisService;
import com.zte.uedm.dcdigital.common.bean.brand.ProductBrandInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductTreeVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.enums.BidAiAnalysisHisTypeEnums;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BidAiAnalysisMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BidAiAnalysisFilePo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BidAiAnalysisPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BidAiAnalysisResultPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.BidAiAnalysisResultUpdDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BidAiAnalysisFileVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BidAiAnalysisHisVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BidAiAnalysisResultVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BidAiAnalysisVo;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import com.zte.uedm.dcdigital.sdk.system.rpc.MsgRpc;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BidAiAnalysisServiceImpl implements BidAiAnalysisService {

    @Autowired
    private BidAiAnalysisMapper bidAiAnalysisMapper;

    @Autowired
    private MsgRpc msgRpc;

    @Autowired
    private AuthService authService;

    @Autowired
    private ProductService productService;

    @Autowired
    private SystemService systemService;

    /**
     * 查询标书AI分析历史记录。
     *
     * @param productId 产品ID
     * @return 包含标书AI分析历史记录的列表
     */
    @Override
    public List<BidAiAnalysisHisVo> hisList(String productId) {
        List<BidAiAnalysisPo> list = bidAiAnalysisMapper.getAnalysisList(productId);
        List<BidAiAnalysisHisVo> voList = new ArrayList<>();
        if (list.size() == 0) {
            return voList;
        }
        for (BidAiAnalysisPo b : list) {
            BidAiAnalysisHisVo vo = new BidAiAnalysisHisVo();
            vo.setId(b.getId());
            vo.setHis(b.getHis());
            vo.setHisName(BidAiAnalysisHisTypeEnums.lookFor(b.getHis()).name);
            vo.setMsg(b.getMsg());
            vo.setCreateTime(b.getCreateTime());
            vo.setUserId(b.getUserId());
            vo.setUserName(systemService.getUserinfoById(b.getUserId()).getName() + b.getUserId());
            List<BidAiAnalysisFilePo> filePoList = bidAiAnalysisMapper.getFileList(b.getId());
            List<String> fileName = filePoList.stream().map(BidAiAnalysisFilePo::getFileName).collect(Collectors.toList());
            vo.setFileName(String.join(",", fileName));
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 更新标书AI分析结果。
     *
     * @param dto 包含更新信息的 BidAiAnalysisResultUpdDto 对象
     */
    @Override
    public void updRes(BidAiAnalysisResultUpdDto dto) {
        BidAiAnalysisResultPo po = new BidAiAnalysisResultPo();
        po.setId(dto.getId());
        po.setAdvantage(dto.getAdvantage());
        po.setDisadvantage(dto.getDisadvantage());
        po.setGuidingSuggestions(dto.getGuidingSuggestions());
        po.setUpdateBy(authService.getUserId());
        po.setUpdateTime(DateTimeUtils.getCurrentTime());
        bidAiAnalysisMapper.updRes(po);
    }

    /**
     * 根据ID查询标书AI分析详情。
     *
     * @param id 分析任务ID
     * @return 包含标书AI分析详情的 BidAiAnalysisVo 对象
     */
    @Override
    public BidAiAnalysisVo detail(String id) {
        BidAiAnalysisVo vo = new BidAiAnalysisVo();
        BidAiAnalysisPo po = bidAiAnalysisMapper.getAnalysisById(id);
        vo.setId(po.getId());
        vo.setHis(po.getHis());
        vo.setHisName(BidAiAnalysisHisTypeEnums.lookFor(po.getHis()).name);
        vo.setUserId(po.getUserId());
        vo.setUserName(systemService.getUserinfoById(po.getUserId()).getName() + po.getUserId());
        vo.setCreateTime(po.getCreateTime());
        vo.setMsg(po.getMsg());
        vo.setShortBrand(po.getShortBrand());
        List<BidAiAnalysisFileVo> fileList = new ArrayList<>();
        List<BidAiAnalysisResultVo> analysisList = new ArrayList<>();
        List<BidAiAnalysisFilePo> fileVoList = bidAiAnalysisMapper.getFileList(id);
        if (fileVoList.size() > 0) {
            for (BidAiAnalysisFilePo filePo : fileVoList) {
                BidAiAnalysisFileVo fileVo = new BidAiAnalysisFileVo();
                fileVo.setId(filePo.getId());
                fileVo.setPage(filePo.getPage());
                fileVo.setFileId(filePo.getFileId());
                fileVo.setFileName(filePo.getFileName());
                fileVo.setFileType(filePo.getFileType());
                fileList.add(fileVo);
            }
        }
        vo.setFileList(fileList);
        List<BidAiAnalysisResultPo> resList = bidAiAnalysisMapper.getResList(id);
        if (resList.size() > 0) {
            for (BidAiAnalysisResultPo resultPo : resList) {
                BidAiAnalysisResultVo resultVo = new BidAiAnalysisResultVo();
                resultVo.setId(resultPo.getId());
                resultVo.setProductCategoryId(resultPo.getProductCategoryId());
                resultVo.setBrandId(resultPo.getBrandId());
                resultVo.setAdvantage(resultPo.getAdvantage());
                resultVo.setDisadvantage(resultPo.getDisadvantage());
                resultVo.setGuidingSuggestions(resultPo.getGuidingSuggestions());
                try {
                    ProductCategoryInfoVo productCategoryVo = productService.selectCategoryById(resultPo.getProductCategoryId());
                    resultVo.setProductCategoryName(null == productCategoryVo ? null : productCategoryVo.getPathName());
                } catch (Exception e) {
                    log.info("没有找到对应资源：{}", e.getMessage());
                }
                try {
                    ProductBrandInfoVo productBrandVo = productService.selectBrandById(resultPo.getBrandId());
                    resultVo.setBrandName(null == productBrandVo ? null : productBrandVo.getBrandName());
                } catch (Exception e) {
                    log.info("没有找到对应资源：{}", e.getMessage());
                }
                analysisList.add(resultVo);

            }
        }
        vo.setAnalysisList(analysisList);
        return vo;
    }

    /**
     * 根据产品ID查询标书AI分析详情。
     *
     * @param productId 产品ID
     * @return 包含标书AI分析详情的 BidAiAnalysisVo 对象
     */
    @Override
    public BidAiAnalysisVo detailByProductId(String productId) {
        BidAiAnalysisPo po = bidAiAnalysisMapper.detailByProductId(productId);
        if (null == po) {
            return null;
        }

        return detail(po.getId());
    }

    /**
     * 导出标书AI分析结果。
     *
     * @param id            分析任务ID
     * @param response      HTTP响应对象，用于文件下载
     */
    @Override
    public void export(String id, HttpServletResponse response) {
        BidAiAnalysisVo vo = detail(id);
        List<BidAiAnalysisFileVo> fileList = vo.getFileList();
        List<BidAiAnalysisResultVo> analysisList = vo.getAnalysisList();
        try {
            // 获取模板文件输入流
            ClassPathResource resource = new ClassPathResource("template" + File.separator + "Bid-Ai-Analysis.xlsx");
            log.info("templateStream:{}", JSON.toJSONString(resource));
            InputStream templateStream = resource.getInputStream();

            try {
                String fileNameStr = "Bid-Ai-Analysis" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xls";
                String encodedFileName = URLEncoder.encode(fileNameStr, StandardCharsets.UTF_8.toString());
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);
            } catch (Exception e) {
                log.error("set response header error", e);
                throw new BusinessException(StatusCode.SYSTEM_ERROR);
            }
            try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(templateStream)
                    .registerWriteHandler(setStyle())
                    .excelType(ExcelTypeEnum.XLSX)
                    .autoCloseStream(Boolean.TRUE).build()) {
                WriteSheet writeSheet1 = EasyExcel.writerSheet("分析文档").build();
                excelWriter.write(fileList, writeSheet1);
                if (null != analysisList && analysisList.size() > 0) {
                    WriteSheet writeSheet3 = EasyExcel.writerSheet("分析结果").build();
                    excelWriter.write(analysisList, writeSheet3);
                }
            }
        } catch (IOException e) {
            log.error("export productCoreParamList data is filed", e);
            throw new BusinessException(StatusCode.FAILED);
        }
    }

    /**
     * 根据分析任务ID和产品类别ID获取标书AI分析结果。
     *
     * @param id                分析任务ID
     * @param productCategoryId 产品类别ID
     * @return 包含标书AI分析结果的列表
     */
    @Override
    public List<BidAiAnalysisResultVo> getResByCategoryId(String id, String productCategoryId) {
        List<BidAiAnalysisResultVo> analysisList = new ArrayList<>();
        List<BidAiAnalysisResultPo> resList = bidAiAnalysisMapper.getResList(id);
        if (null == resList || resList.size() == 0) {
            return analysisList;
        }
        if (StringUtils.isNotBlank(productCategoryId)) {
            List<String> list = productService.selectByCategoryId(productCategoryId);
            resList = resList.stream()
                    .filter(res -> list.contains(res.getProductCategoryId()))
                    .collect(Collectors.toList());
        }
        for (BidAiAnalysisResultPo resultPo : resList) {
            BidAiAnalysisResultVo resultVo = new BidAiAnalysisResultVo();
            resultVo.setId(resultPo.getId());
            resultVo.setProductCategoryId(resultPo.getProductCategoryId());
            resultVo.setBrandId(resultPo.getBrandId());
            resultVo.setAdvantage(resultPo.getAdvantage());
            resultVo.setDisadvantage(resultPo.getDisadvantage());
            resultVo.setGuidingSuggestions(resultPo.getGuidingSuggestions());
            try {
                ProductCategoryInfoVo productCategoryVo = productService.selectCategoryById(resultPo.getProductCategoryId());
                resultVo.setProductCategoryName(null == productCategoryVo ? null : productCategoryVo.getPathName());
            } catch (Exception e) {
                log.info("没有找到对应资源：{}", e.getMessage());
            }
            try {
                ProductBrandInfoVo productBrandVo = productService.selectBrandById(resultPo.getBrandId());
                resultVo.setBrandName(null == productBrandVo ? null : productBrandVo.getBrandName());
            } catch (Exception e) {
                log.info("没有找到对应资源：{}", e.getMessage());
            }
            analysisList.add(resultVo);
        }
        return analysisList;
    }

    /**
     * 设置Excel单元格样式。
     *
     * @return 水平单元格样式策略
     */
    private HorizontalCellStyleStrategy setStyle() {
        // 定义样式：自动换行
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setWrapped(true); // 关键：开启自动换行

        WriteFont writeFont = new WriteFont();
        writeFont.setFontName("Microsoft YaHei"); // 字体
        writeFont.setFontHeightInPoints((short) 12); // 字体大小
        contentWriteCellStyle.setWriteFont(writeFont);

        // 注册样式策略（全局生效）
        HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(
                null, // 头样式（默认）
                contentWriteCellStyle // 内容样式（自动换行）
        );

        return styleStrategy;
    }

    @Override
    public  List<ProductTreeVo> queryTreeById(String id){
        List<BidAiAnalysisResultPo> resList = bidAiAnalysisMapper.getResList(id);
        if(null==resList||resList.size()==0){
            return null;
        }
        return productService.getProductSubcategoryTree(resList.stream().map(p->p.getProductCategoryId()).collect(Collectors.toList()));
    }
}
/* Ended by AICoder, pid:048dem66b8a46f814c90092173e48b0c9af3a722 */