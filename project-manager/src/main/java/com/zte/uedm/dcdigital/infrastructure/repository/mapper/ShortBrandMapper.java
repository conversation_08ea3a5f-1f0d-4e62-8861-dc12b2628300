package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ShortBrandPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ShortBrandMapper extends BaseMapper<ShortBrandPo> {
    void deletePatchByGuideDetailId(List<String> brandGuideDetailIds);
}
