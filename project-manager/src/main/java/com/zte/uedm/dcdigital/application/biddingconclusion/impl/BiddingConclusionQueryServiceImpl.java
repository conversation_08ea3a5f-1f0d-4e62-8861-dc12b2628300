/* Started by AICoder, pid:td0e5t4b0cxa7721454a0b1fe0ab8420e2c56464 */
package com.zte.uedm.dcdigital.application.biddingconclusion.impl;

import com.zte.uedm.dcdigital.application.biddingconclusion.BiddingConclusionQueryService;
import com.zte.uedm.dcdigital.domain.service.BiddingConclusionDomainService;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingConclusionDropDownVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingConclusionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BiddingConclusionQueryServiceImpl implements BiddingConclusionQueryService {

    @Autowired
    private BiddingConclusionDomainService domainService;


    @Override
    public BiddingConclusionDropDownVo queryDropDown() {
        return domainService.queryDropDown();
    }

    @Override
    public boolean getFirstFill(String projectId) {
        return domainService.getFirstFill(projectId);
    }

    @Override
    public BiddingConclusionVo getDetail(String projectId) {
        return domainService.getDetail(projectId);
    }
}

/* Ended by AICoder, pid:td0e5t4b0cxa7721454a0b1fe0ab8420e2c56464 */