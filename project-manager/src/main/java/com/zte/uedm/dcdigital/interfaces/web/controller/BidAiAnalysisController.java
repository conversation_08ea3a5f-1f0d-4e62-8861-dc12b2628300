/* Started by AICoder, pid:aa568c95e18b17314f320bf131150240b42384e1 */
package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.project.BidAiAnaService;
import com.zte.uedm.dcdigital.application.project.BidAiAnalysisService;
import com.zte.uedm.dcdigital.common.bean.document.BidAnalysisContentVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductTreeVo;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.domain.service.AIBidService;
import com.zte.uedm.dcdigital.interfaces.web.dto.BidAiAnalysisDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BidAiAnalysisResultUpdDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * BidAiAnalysisController 标书AI分析控制器。
 * 该控制器处理与标书AI分析相关的HTTP请求，包括历史记录查询、结果更新、分析任务启动、详情查询、导出和按类别ID获取结果等。
 */
@Path("uportal/bidAnalysis")
@Api(value = "bidAnalysis")
@Controller
@Slf4j
public class BidAiAnalysisController {

    @Autowired
    private BidAiAnalysisService bidAiAnalysisService;

    @Autowired
    private BidAiAnaService bidAiAnaService;

    /**
     * 查询标书AI分析历史记录。
     *
     * @param productId 产品ID
     * @return 包含历史记录的 BaseResult 对象
     */
    @POST
    @Path("/hisList")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "历史记录", notes = "历史记录", httpMethod = "POST")
    public BaseResult<Object> hisList(@QueryParam("productId") String productId) {
        return BaseResult.success(bidAiAnalysisService.hisList(productId));
    }

    /**
     * 更新标书AI分析结果。
     *
     * @param dto 包含更新信息的 BidAiAnalysisResultUpdDto 对象
     * @return 成功的 BaseResult 对象
     */
    @POST
    @Path("/updRes")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "编辑分析结果", notes = "编辑分析结果", httpMethod = "POST")
    public BaseResult<Object> updRes(BidAiAnalysisResultUpdDto dto) {
        bidAiAnalysisService.updRes(dto);
        return BaseResult.success();
    }

    /**
     * 发起标书AI分析任务。
     *
     * @param dto 包含分析任务信息的 BidAiAnalysisDto 对象
     * @return 成功的 BaseResult 对象
     */
    @POST
    @Path("/analysis")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "发起标书分析", notes = "发起标书分析", httpMethod = "POST")
    public BaseResult<Object> analysis(BidAiAnalysisDto dto) {
        bidAiAnaService.analysis(dto);
        return BaseResult.success();
    }

    /* Started by AICoder, pid:o75ceff67ffb3871464b0b45400694107928de96 */
    @POST
    @Path("/getContentById")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取文件内容", notes = "根据文件ID、页码和文件类型获取文件内容", httpMethod = "POST")
    public BaseResult<Object> getContentById(
            @QueryParam("fileId") String fileId,
            @QueryParam("pages") String pages,
            @QueryParam("fileType") String fileType) {
        /**
         * 根据文件ID、页码和文件类型获取文件内容。
         *
         * @param fileId    文件ID
         * @param pages     页码，表示需要获取的内容页数
         * @param fileType  文件类型
         * @return 包含文件内容的 BaseResult 对象
         */
        return BaseResult.success(bidAiAnaService.getContentById(fileId, pages, fileType));
    }
    /* Ended by AICoder, pid:o75ceff67ffb3871464b0b45400694107928de96 */

    /**
     * 查询标书AI分析详情。
     *
     * @param id 分析任务ID
     * @return 包含分析详情的 BaseResult 对象
     */
    @GET
    @Path("/detail")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "分析详情", notes = "分析详情", httpMethod = "GET")
    public BaseResult<Object> detail(@QueryParam("id") String id) {
        return BaseResult.success(bidAiAnalysisService.detail(id));
    }

    /**
     * 根据产品ID查询最新一次标书AI分析记录。
     *
     * @param productId 产品ID
     * @return 包含分析记录的 BaseResult 对象
     */
    @GET
    @Path("/detailByProductId")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据商机获取最新一次分析记录", notes = "根据商机获取最新一次分析记录", httpMethod = "GET")
    public BaseResult<Object> detailByProductId(@QueryParam("productId") String productId) {
        return BaseResult.success(bidAiAnalysisService.detailByProductId(productId));
    }

    /**
     * 导出标书AI分析结果。
     *
     * @param id            分析任务ID
     * @param response      HTTP响应对象，用于文件下载
     */
    @POST
    @Path("/export")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @ApiOperation(value = "导出", notes = "导出", httpMethod = "POST")
    public void export(@QueryParam("id") String id, @Context HttpServletResponse response) {
        bidAiAnalysisService.export(id, response);
    }

    /**
     * 根据分析任务ID和产品类别ID获取标书AI分析结果。
     *
     * @param id                分析任务ID
     * @param productCategoryId 产品类别ID
     * @return 包含分析结果的 BaseResult 对象
     */
    @GET
    @Path("/getResByCategoryId")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据小类获取结果", notes = "根据小类获取结果", httpMethod = "GET")
    public BaseResult<Object> getResByCategoryId(@QueryParam("id") String id,
                                                 @QueryParam("productCategoryId") String productCategoryId) {
        return BaseResult.success(bidAiAnalysisService.getResByCategoryId(id, productCategoryId));
    }

    @Autowired
    private AIBidService aiBidService;

    @POST
    @Path("/callAiGetCategory")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "ai产品小类", notes = "ai产品小类", httpMethod = "POST")
    public BaseResult<Object> callAiGetCategory( BidAnalysisContentVo bidAnalysisContentVo ) {

        return BaseResult.success(aiBidService.callAiGetCategory(bidAnalysisContentVo.getContent()));
    }

    @POST
    @Path("/callAiGetRes")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "ai标书分析", notes = "ai标书分析", httpMethod = "POST")
    public BaseResult<Object> callAiGetRes( BidAnalysisContentVo bidAnalysisContentVo) {
        return BaseResult.success(aiBidService.callAiGetRes(bidAnalysisContentVo.getContent(),bidAnalysisContentVo));
    }

    @GET
    @Path("/queryTreeById")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据id列表查询", notes = "根据id列表查询", httpMethod = "GET")
    public BaseResult<List<ProductTreeVo>> queryTreeById(@QueryParam("id") String id) {

        return BaseResult.success(bidAiAnalysisService.queryTreeById(id));
    }
}
/* Ended by AICoder, pid:aa568c95e18b17314f320bf131150240b42384e1 */