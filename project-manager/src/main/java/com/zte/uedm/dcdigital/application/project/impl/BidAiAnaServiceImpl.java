/* Started by AICoder, pid:wdd98q9d46f3e9e1492e098fd0a4d53ef2e808f8 */
package com.zte.uedm.dcdigital.application.project.impl;

import com.zte.uedm.dcdigital.application.project.BidAiAnaService;
import com.zte.uedm.dcdigital.common.bean.enums.system.MsgTypeEnums;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.util.RpcUtil;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.enums.BidAiAnalysisHisTypeEnums;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProjectStatusCode;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BidAiAnalysisMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BidAiAnalysisFilePo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BidAiAnalysisPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.BidAiAnalysisDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.BidAiAnalysisFileDto;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.sdk.system.dto.MsgLogDto;
import com.zte.uedm.dcdigital.sdk.system.rpc.MsgRpc;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.IntelligenceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class BidAiAnaServiceImpl implements BidAiAnaService {

    @Autowired
    private BidAiAnalyService bidAiAnalyService;

    @Autowired
    private BidAiAnalysisMapper bidAiAnalysisMapper;

    @Autowired
    private AuthService authService;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private MsgRpc msgRpc;

    @Autowired
    private IntelligenceService intelligenceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void analysis(BidAiAnalysisDto dto) {
        /**
         * 执行标书分析。
         *
         * @param dto 包含分析所需信息的 BidAiAnalysisDto 对象
         */
        List<BidAiAnalysisFileDto> fileList = dto.getFileList();
        if (null == fileList || fileList.size() == 0) {
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }

        /**
         * 异步执行文件分析。
         *
         * @param dto 包含分析所需信息的 BidAiAnalysisDto 对象
         */
        BidAiAnalysisPo bidAiAnalysisPo = new BidAiAnalysisPo();
        String id = UUID.randomUUID().toString();
        String userId = authService.getUserId();
        bidAiAnalysisPo.setId(id);
        bidAiAnalysisPo.setProjectId(dto.getProjectId());
        bidAiAnalysisPo.setUserId(userId);
        bidAiAnalysisPo.setShortBrand(dto.getShortBrand());
        bidAiAnalysisPo.setHis(BidAiAnalysisHisTypeEnums.TWO.type);
        bidAiAnalysisPo.setCreateBy(userId);
        bidAiAnalysisPo.setCreateTime(DateTimeUtils.getCurrentTime());
        bidAiAnalysisMapper.addAnalysis(bidAiAnalysisPo);


        StringBuilder text = new StringBuilder();
        for (BidAiAnalysisFileDto bidAiAnalysisFileDto : fileList) {
            String pages =bidAiAnalysisFileDto.getPage();
            String[] ins = pages.split(",");
            Integer page = Integer.valueOf(ins[ins.length-1]);
            Integer n = documentService.getPageById(bidAiAnalysisFileDto.getFileId());
            if(page>n){
                throw new BusinessException(ProjectStatusCode.FILE_PAGE_ERR);
            }

            BidAiAnalysisFilePo bidAiAnalysisFilePo = new BidAiAnalysisFilePo();
            bidAiAnalysisFilePo.setId(UUID.randomUUID().toString());
            bidAiAnalysisFilePo.setAnalysisId(id);
            bidAiAnalysisFilePo.setFileId(bidAiAnalysisFileDto.getFileId());
            bidAiAnalysisFilePo.setFileType(bidAiAnalysisFileDto.getFileType());
            bidAiAnalysisFilePo.setPage(bidAiAnalysisFileDto.getPage());
            bidAiAnalysisFilePo.setFileName(bidAiAnalysisFileDto.getFileName());
            String content = documentService.getContentById(bidAiAnalysisFileDto.getFileId(),
                    pages, bidAiAnalysisFileDto.getFileType());
            bidAiAnalysisFilePo.setContent(content);
            bidAiAnalysisFilePo.setCreateBy(userId);
            bidAiAnalysisFilePo.setCreateTime(DateTimeUtils.getCurrentTime());
            bidAiAnalysisMapper.addAnalysisFile(bidAiAnalysisFilePo);
            text.append(content);
        }
        if (StringUtils.isBlank(text.toString())) {
            BidAiAnalysisPo po = new BidAiAnalysisPo();
            po.setId(id);
            po.setHis(BidAiAnalysisHisTypeEnums.THREE.type);
            po.setMsg("未获取到文档内容");
            bidAiAnalysisMapper.updAnalysis(po);
            MsgLogDto msgLogDto = new MsgLogDto();
            msgLogDto.setMsgName("标书分析完成：" + dto.getProjectName() + ",分析结果：失败，未获取到文档内容！");
            sendMsg(msgLogDto);
            return;
        }
        try {
            intelligenceService.intelligenceBuryingPoint("2",userId);
        }catch (Exception e){
            log.error("标书分析统计调用失败：{}",e.getMessage());
        }
        bidAiAnalyService.analysis(dto, id, text.toString(),userId);
    }

    public void sendMsg(MsgLogDto msgLogDto) {
        /**
         * 发送消息通知。
         *
         * @param msgLogDto 消息日志传输对象
         */
        msgLogDto.setMsgContent(msgLogDto.getMsgName());
        msgLogDto.setMsgType(Integer.valueOf(MsgTypeEnums.FIVE.type));
        msgLogDto.setNotifyType(GlobalConstants.ONE);
        msgLogDto.setNotifyUsers(authService.getUserId());
        msgLogDto.setLink("");
        RpcUtil.call(msgRpc.add(msgLogDto));
    }

    @Override
    public String getContentById(String fileId, String pages, String fileType) {
        /**
         * 根据文件ID、页码和文件类型获取文件内容。
         *
         * @param fileId    文件ID
         * @param pages     页码，表示需要获取的内容页数
         * @param fileType  文件类型
         * @return 文件内容字符串
         */
        return bidAiAnalyService.getContentById(fileId, pages, fileType);
    }
}
/* Ended by AICoder, pid:wdd98q9d46f3e9e1492e098fd0a4d53ef2e808f8 */