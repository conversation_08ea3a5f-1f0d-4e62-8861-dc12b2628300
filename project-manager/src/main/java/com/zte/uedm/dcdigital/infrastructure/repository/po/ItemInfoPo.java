/* Started by AICoder, pid:pcf32i801307c86149ad0b8880ee855f163322b0 */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@TableName("item_info")
public class ItemInfoPo {

    /**
     * 项目id
     */
    private String id;

    /**
     * 商机id
     */
    private String projectId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建用户
     */
    private String createBy;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 更新用户
     */
    private String updateBy;
}

/* Ended by AICoder, pid:pcf32i801307c86149ad0b8880ee855f163322b0 */