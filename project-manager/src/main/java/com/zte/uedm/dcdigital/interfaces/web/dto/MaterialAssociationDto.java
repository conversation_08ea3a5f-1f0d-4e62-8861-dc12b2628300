/* Started by AICoder, pid:z2e80u7ddd3807e141dc09774094b529bb35af99 */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Getter
@Setter
public class MaterialAssociationDto {

    private String projectId;

    @NotBlank(message = "billId must be not null")
    @LogMark(range = {OperationMethodEnum.ADD})
    private String billId;

    @LogMark(range = {OperationMethodEnum.ADD})
    private List<String> selectedMaterials;

    private List<MaterialAmount> materialAmout;
}

/* Ended by AICoder, pid:z2e80u7ddd3807e141dc09774094b529bb35af99 */