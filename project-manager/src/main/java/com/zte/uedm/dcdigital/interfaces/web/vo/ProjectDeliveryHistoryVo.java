/* Started by AICoder, pid:q615564399fecb51472d081f2045cf5424b5df2b */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ProjectDeliveryHistoryVo {
    /**
     * 主表ID，唯一标识一个项目交付历史记录
     */
    private String id;

    /**
     * 商机或项目ID，标识该交付历史记录所属的商机或项目
     */
    private String projectId;

    /**
     * 整体状态，表示项目的整体健康状况
     */
    private String overallState;
    private String overallStateName;

    /**
     * 交付进度，表示项目的交付完成百分比
     */
    private Integer deliveryProgress;

    /**
     * 项目周进展，描述项目每周的进展情况
     */
    private String weeklyProjectProgress;

    /**
     * 创建人，记录创建该项目交付历史记录的用户
     */
    private String createBy;

    /**
     * 创建时间，记录该项目交付历史记录的创建时间
     */
    private String createTime;

    /**
     * 更新人，记录最后一次更新该项目交付历史记录的用户
     */
    private String updateBy;

    /**
     * 更新时间，记录该项目交付历史记录的最后更新时间
     */
    private String updateTime;
}
/* Ended by AICoder, pid:q615564399fecb51472d081f2045cf5424b5df2b */