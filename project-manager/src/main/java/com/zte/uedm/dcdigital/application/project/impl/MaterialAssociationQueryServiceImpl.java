package com.zte.uedm.dcdigital.application.project.impl;

import com.zte.uedm.dcdigital.application.project.MaterialAssociationQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.bean.product.MaterialWithExtendInfoVo;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectBillMaterialEntity;
import com.zte.uedm.dcdigital.domain.service.ProjectBillMaterialDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.AssociatedMaterialQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAssociatedMaterialQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.AssociatedMaterialVo;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.Collator;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MaterialAssociationQueryServiceImpl implements MaterialAssociationQueryService {
    @Autowired
    private ProjectBillMaterialDomainService projectBillMaterialDomainService;
    @Autowired
    private ProductService productService;

    @Override
    public List<IdNameBean> queryAssociatedMaterialsSimple(String billId) {
        List<ProjectBillMaterialEntity> list = projectBillMaterialDomainService.queryAssociatedMaterials(billId);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<String> materialIds = list.stream().map(ProjectBillMaterialEntity::getMaterialId).collect(Collectors.toList());
        return productService.queryMaterialNameByIds(materialIds);
    }

    @Override
    public PageVO<AssociatedMaterialVo> queryPagingAssociatedMaterials(AssociatedMaterialQueryDto queryDto) {
        // 查询关联物料id
        PageVO<ProjectBillMaterialEntity> pageVO = projectBillMaterialDomainService.queryPagingAssociatedMaterials(queryDto.getBillId(), queryDto.getPageNum(), queryDto.getPageSize());
        PageVO<AssociatedMaterialVo> pageData = getPageData(pageVO);
        /* Started by AICoder, pid:h0286n21bb16ef4148960a99e0dd240eb818ea4b */
        Collator collator = Collator.getInstance(Locale.CHINA);
        Comparator<AssociatedMaterialVo> comparator = Comparator.comparing(AssociatedMaterialVo::getPathName, collator)
                .thenComparing(AssociatedMaterialVo::getGroupPathName, collator)
                .thenComparing(AssociatedMaterialVo::getName, collator);

        // 排序
        List<AssociatedMaterialVo> sortedBeans = pageData.getList().stream().sorted(comparator).collect(Collectors.toList());
        /* Ended by AICoder, pid:h0286n21bb16ef4148960a99e0dd240eb818ea4b */
        /* Started by AICoder, pid:qec5bh7e63h857b141650b19d06818159bf56adf */
        List<List<AssociatedMaterialVo>> pages = ListUtils.partition(sortedBeans, queryDto.getPageSize());
        int pageNumber = queryDto.getPageNum();
        // 安全地获取当前页的数据列表
        List<AssociatedMaterialVo> pagedBeans = (pageNumber > 0 && pageNumber <= pages.size())
                ? pages.get(pageNumber - 1)
                : Collections.emptyList();
        pageData.setList(pagedBeans);
        /* Ended by AICoder, pid:qec5bh7e63h857b141650b19d06818159bf56adf */
        return pageData;
    }

    /* Started by AICoder, pid:89399036b6913991438c0921a014e0172b08b8b6 */
    @Override
    public List<AssociatedMaterialVo> noPageQueryPagingAssociatedMaterials(AssociatedMaterialQueryDto queryDto) {
        // 查询关联物料id
        List<ProjectBillMaterialEntity> entityList = projectBillMaterialDomainService.noPageQueryPagingAssociatedMaterials(queryDto.getBillId());

        List<AssociatedMaterialVo> associatedMaterialVos = getVoData(entityList);

        Collator collator = Collator.getInstance(Locale.CHINA);
        Comparator<AssociatedMaterialVo> comparator = Comparator.comparing(AssociatedMaterialVo::getPathName, collator)
                .thenComparing(AssociatedMaterialVo::getGroupPathName, collator)
                .thenComparing(AssociatedMaterialVo::getName, collator);

        // 排序
        return associatedMaterialVos.stream()
                .sorted(comparator)
                .collect(Collectors.toList());
    }

    /* Ended by AICoder, pid:89399036b6913991438c0921a014e0172b08b8b6 */


    @Override
    public PageVO<AssociatedMaterialVo> queryAssociatedMaterials(ProjectAssociatedMaterialQueryDto queryDto) {
         PageVO<ProjectBillMaterialEntity> pageVO = projectBillMaterialDomainService.queryAllAssociatedMaterials(queryDto.getProjectId(), queryDto.getPageNum(), queryDto.getPageSize());
         return getPageData(pageVO);
    }

    private PageVO<AssociatedMaterialVo> getPageData(PageVO<ProjectBillMaterialEntity> pageVO) {
        PageVO<AssociatedMaterialVo> resultPageVO = new PageVO<>();
        if (CollectionUtils.isEmpty(pageVO.getList())) {
            resultPageVO.setList(Collections.emptyList());
            return resultPageVO;
        }
        List<String> materialIds = pageVO.getList().stream().map(ProjectBillMaterialEntity::getMaterialId).collect(Collectors.toList());
        // 查询物料信息
        List<MaterialWithExtendInfoVo> materialVos = productService.queryMaterialByIds(materialIds);
        Map<String, MaterialWithExtendInfoVo> tempMaterialMap;
        if (CollectionUtils.isNotEmpty(materialVos)) {
            tempMaterialMap = materialVos.stream().collect(Collectors.toMap(MaterialWithExtendInfoVo::getId, Function.identity()));
        } else {
            tempMaterialMap = new HashMap<>();
        }

        List<AssociatedMaterialVo> voList = pageVO.getList().stream().map(item -> {
            AssociatedMaterialVo vo = new AssociatedMaterialVo();
            vo.setMaterialId(item.getMaterialId());
            MaterialWithExtendInfoVo materialInfo = tempMaterialMap.get(item.getMaterialId());
            if (materialInfo == null) {
                return vo;
            }
            vo.setPathName(materialInfo.getPathName());
            vo.setProductCategoryId(materialInfo.getProductCategoryId());
            vo.setName(materialInfo.getMaterialName());
            vo.setGroupId(materialInfo.getGroupId());
            vo.setGroupPathName(materialInfo.getGroupPathName());
            vo.setExpirationDate(materialInfo.getExpirationDate());
            if (materialInfo.getPurchaseMode() != null) {
                vo.setPurchaseModeId(materialInfo.getPurchaseMode().getId());
                vo.setPurchaseMode(materialInfo.getPurchaseMode().getName());
            }
            if (materialInfo.getPdmInfo() != null) {
                vo.setSalesCode(materialInfo.getPdmInfo().getSalesCode());
                vo.setProductionCode(materialInfo.getPdmInfo().getProductionCode());
            }
            if (materialInfo.getOthInfo() != null) {
                vo.setSalesCode(materialInfo.getOthInfo().getSalesCode());
                vo.setProductionCode(materialInfo.getOthInfo().getProductionCode());
            }
            return vo;
        }).collect(Collectors.toList());
        resultPageVO.setTotal(pageVO.getTotal());
        resultPageVO.setList(voList);
        return resultPageVO;
    }

    /* Started by AICoder, pid:bea3b3d0681b89c14e5b09a5e0286356f7f76416 */
    private List<AssociatedMaterialVo> getVoData(List<ProjectBillMaterialEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }

        List<String> materialIds = entityList.stream()
                .map(ProjectBillMaterialEntity::getMaterialId)
                .collect(Collectors.toList());

        // 查询物料信息
        List<MaterialWithExtendInfoVo> materialVos = productService.queryMaterialByIds(materialIds);
        Map<String, MaterialWithExtendInfoVo> tempMaterialMap;

        if (CollectionUtils.isNotEmpty(materialVos)) {
            tempMaterialMap = materialVos.stream()
                    .collect(Collectors.toMap(MaterialWithExtendInfoVo::getId, Function.identity()));
        } else {
            tempMaterialMap = new HashMap<>();
        }

        List<AssociatedMaterialVo> voList = entityList.stream().map(item -> {
            AssociatedMaterialVo vo = new AssociatedMaterialVo();
            vo.setMaterialId(item.getMaterialId());
            MaterialWithExtendInfoVo materialInfo = tempMaterialMap.get(item.getMaterialId());

            if (materialInfo == null) {
                return vo;
            }

            vo.setPathName(materialInfo.getPathName());
            vo.setProductCategoryId(materialInfo.getProductCategoryId());
            vo.setName(materialInfo.getMaterialName());
            vo.setGroupId(materialInfo.getGroupId());
            vo.setGroupPathName(materialInfo.getGroupPathName());
            vo.setExpirationDate(materialInfo.getExpirationDate());
            vo.setAmount(item.getAmount());

            if (materialInfo.getPurchaseMode() != null) {
                vo.setPurchaseModeId(materialInfo.getPurchaseMode().getId());
                vo.setPurchaseMode(materialInfo.getPurchaseMode().getName());
            }

            if (materialInfo.getPdmInfo() != null) {
                vo.setSalesCode(materialInfo.getPdmInfo().getSalesCode());
                vo.setProductionCode(materialInfo.getPdmInfo().getProductionCode());
            }

            if (materialInfo.getOthInfo() != null) {
                vo.setSalesCode(materialInfo.getOthInfo().getSalesCode());
                vo.setProductionCode(materialInfo.getOthInfo().getProductionCode());
            }

            return vo;
        }).collect(Collectors.toList());

        return voList;
    }

    /* Ended by AICoder, pid:bea3b3d0681b89c14e5b09a5e0286356f7f76416 */
}
