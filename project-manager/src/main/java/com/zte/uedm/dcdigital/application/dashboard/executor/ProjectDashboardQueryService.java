package com.zte.uedm.dcdigital.application.dashboard.executor;

import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectScoreChartDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.TimeRangeDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProjectProductsVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProjectScoreResultVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProductScoreLineChartVo;

import java.util.List;

public interface ProjectDashboardQueryService {
    ProjectScoreResultVo selectProjectsScore(TimeRangeDto timeRange);

    ProjectProductsVo selectGuideProductCategories(String projectId);

    List<ProductScoreLineChartVo> lineChart(ProjectScoreChartDto chartDTO);
}
