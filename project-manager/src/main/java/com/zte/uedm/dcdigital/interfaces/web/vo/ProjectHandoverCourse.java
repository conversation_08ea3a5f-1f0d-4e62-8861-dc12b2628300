/* Started by AICoder, pid:2908ei31fc1a9111424b08870074ac3dcd7440a9 */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 项目里程碑 进程点
 */
@Getter
@Setter
@ToString
public class ProjectHandoverCourse {

    /**
     * 项目里程碑--交接
     */
    private boolean HANDOVER;

    /**
     * 项目里程碑--配置锁定
     */
    private boolean CONFIGURATION_LOCK;

    /**
     * 项目里程碑--PAC
     */
    private boolean PAC;

    /**
     * 项目里程碑--FAC
     */
    private boolean FAC;
}

/* Ended by AICoder, pid:2908ei31fc1a9111424b08870074ac3dcd7440a9 */