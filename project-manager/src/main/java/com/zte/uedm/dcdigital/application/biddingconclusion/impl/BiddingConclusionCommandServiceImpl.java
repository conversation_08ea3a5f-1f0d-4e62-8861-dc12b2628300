/* Started by AICoder, pid:k34e1jc0abn058f1477b0adc50f0802f9291ad90 */
package com.zte.uedm.dcdigital.application.biddingconclusion.impl;

import com.zte.uedm.dcdigital.application.biddingconclusion.BiddingConclusionCommandService;
import com.zte.uedm.dcdigital.domain.service.BiddingConclusionDomainService;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingConclusionDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BiddingConclusionCommandServiceImpl implements BiddingConclusionCommandService {

    @Autowired
    private BiddingConclusionDomainService domainService;

    @Override
    public void addOrUpdateBiddingConclusion(BiddingConclusionDto conclusionDto) {
        domainService.addOrUpdateBiddingConclusion(conclusionDto);
    }
}

/* Ended by AICoder, pid:k34e1jc0abn058f1477b0adc50f0802f9291ad90 */