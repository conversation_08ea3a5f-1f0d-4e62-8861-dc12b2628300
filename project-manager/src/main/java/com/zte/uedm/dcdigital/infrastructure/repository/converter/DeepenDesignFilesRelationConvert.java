/* Started by AICoder, pid:h568fa7b0cyf31d14b39095020e181237bf4c2f2 */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.domain.aggregate.model.DeepenDesignFilesRelationEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DeepenDesignFilesRelationPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DeepenDesignFilesRelationConvert {
    // 使用常量 INSTANCE 以便通过 Mappers 工厂获取单例实例
    DeepenDesignFilesRelationConvert INSTANCE = Mappers.getMapper(DeepenDesignFilesRelationConvert.class);

    @Mappings({})
    DeepenDesignFilesRelationEntity poToEntity(DeepenDesignFilesRelationPo filesRelationPo);

    @Mappings({})
    DeepenDesignFilesRelationPo entityToPo(DeepenDesignFilesRelationEntity filesRelationEntity);

    @Mappings({})
    List<DeepenDesignFilesRelationEntity> poListToEntityList(List<DeepenDesignFilesRelationPo> poList);
}

/* Ended by AICoder, pid:h568fa7b0cyf31d14b39095020e181237bf4c2f2 */