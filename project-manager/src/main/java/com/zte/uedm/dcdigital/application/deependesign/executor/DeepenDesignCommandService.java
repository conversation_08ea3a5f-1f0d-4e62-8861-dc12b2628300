package com.zte.uedm.dcdigital.application.deependesign.executor;

import com.zte.uedm.dcdigital.interfaces.web.dto.ConstructionDrawingReviewAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ConstructionDrawingReviewDeleteDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ConstructionDrawingReviewEditDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DeepenImplementationEditDto;

/**
 * 深化设计命令服务接口
 */
public interface DeepenDesignCommandService {

    /**
     * 编辑深化实施（包含成果输出部分）
     * 每次更新操作都需要记录历史记录，用于后续追踪和审计
     *
     * @param editDto 编辑信息
     */
    void editImplementation(DeepenImplementationEditDto editDto);

    /**
     * 新增施工图会审记录
     *
     * @param addDto 新增信息
     */
    void addReviewRecord(ConstructionDrawingReviewAddDto addDto);

    /**
     * 编辑施工图会审记录
     *
     * @param editDto 编辑信息
     */
    void editReviewRecord(ConstructionDrawingReviewEditDto editDto);

    /**
     * 删除施工图会审记录
     *
     * @param deleteDto 删除信息
     */
    void deleteReviewRecord(ConstructionDrawingReviewDeleteDto deleteDto);
}
