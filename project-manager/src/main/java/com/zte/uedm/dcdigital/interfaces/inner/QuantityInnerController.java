/* Started by AICoder, pid:v4abambda5oa9f0148660b32f1bcdb4cbf370ab5 */
package com.zte.uedm.dcdigital.interfaces.inner;


/* Started by AICoder, pid:0f5ebb67180884b142030b70c07f63459c545f53 */
import com.zte.uedm.dcdigital.application.project.BillOfQuantityQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.dto.BillOfQuantityQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BillOfQuantityVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * QuantityInnerController 类用于处理与工程量清单相关的内部 RESTful API 请求。
 *
 * <AUTHOR>
 */
@Path("quantity-inner")
@Controller
@Slf4j
public class QuantityInnerController {

    @Autowired
    private BillOfQuantityQueryService billOfQuantityQueryService; // 注入工程量清单查询服务

    /**
     * 根据项目ID分页查询工程量清单列表。
     *
     * @param queryDto 包含查询条件的数据传输对象
     * @return 查询结果
     */
    @POST
    @Path("/query-by-project-id")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "项目id分页查询工程量清单列表", notes = "项目id分页查询工程量清单列表", httpMethod = "POST")
    public BaseResult<PageVO<BillOfQuantityVo>> queryByProjectId(BillOfQuantityQueryDto queryDto) {
        queryDto.validate(); // 验证查询条件
        PageVO<BillOfQuantityVo> pageVO = billOfQuantityQueryService.queryByCondition(queryDto); // 调用服务层方法进行查询
        return BaseResult.success(pageVO); // 返回成功结果
    }
    /* Started by AICoder, pid:icc7f30514o38cb141e009c8105acd19af954860 */
    /**
     * 根据项目ID分页查询工程量清单列表。
     *
     * @param queryDto 包含查询条件的数据传输对象
     * @return 查询结果
     */
    @POST
    @Path("/query-by-project-id-filter")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "根据项目ID分页查询工程量清单列表(只保留指定产品小类的工程量清单id)。", notes = "根据项目ID分页查询工程量清单列表(只保留指定产品小类的工程量清单id)。", httpMethod = "POST")
    public BaseResult<PageVO<BillOfQuantityVo>> queryByProjectIdFilter(BillOfQuantityQueryDto queryDto) {
        queryDto.validate(); // 验证查询条件
        PageVO<BillOfQuantityVo> pageVO = billOfQuantityQueryService.queryByConditionFilter(queryDto); // 调用服务层方法进行查询
        return BaseResult.success(pageVO); // 返回成功结果
    }

    /* Ended by AICoder, pid:icc7f30514o38cb141e009c8105acd19af954860 */
}
/* Ended by AICoder, pid:0f5ebb67180884b142030b70c07f63459c545f53 */