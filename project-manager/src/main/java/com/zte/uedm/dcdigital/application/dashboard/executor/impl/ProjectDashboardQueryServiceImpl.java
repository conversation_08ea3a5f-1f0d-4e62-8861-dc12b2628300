package com.zte.uedm.dcdigital.application.dashboard.executor.impl;

import com.zte.uedm.dcdigital.application.dashboard.constant.ProjectDashboardConstant;
import com.zte.uedm.dcdigital.application.dashboard.executor.ProjectDashboardQueryService;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.bean.enums.system.RoleCodeEnum;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectStageScoreEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ProjectDashboardRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ProjectRepository;
import com.zte.uedm.dcdigital.domain.common.enums.ProjectStageEnum;
import com.zte.uedm.dcdigital.domain.common.valueobj.ChartDataPoint;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectScoreChartDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.TimeRangeDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProjectDashboardQueryServiceImpl implements ProjectDashboardQueryService {
    @Autowired
    private ProjectDashboardRepository projectDashboardRepository;
    @Autowired
    private ProjectRepository projectRepository;
    @Autowired
    private ProductService productService;
    @Autowired
    private AuthService authService;
    @Autowired
    private SystemService systemService;

    /**
     * 查询所有项目及其分数
     *
     * @param timeRange
     * @return
     */
    @Override
    public ProjectScoreResultVo selectProjectsScore(TimeRangeDto timeRange) {
        // 查询有项目评分查看人角色权限的项目ID
        List<String> authorizedProjectIds = getAuthorizedProjectIds(authService.getUserId(), RoleCodeEnum.PROJECT_SCORE_VIEWER.getCode());

        ProjectScoreResultVo resultVo = new ProjectScoreResultVo();
        List<ProjectScoreVo> projectList = new ArrayList<>();
        resultVo.setProjectList(projectList);

        if (CollectionUtils.isEmpty(authorizedProjectIds)) {
            return resultVo;
        }

        List<ProjectStageScoreEntity> projectScores = projectDashboardRepository.listProjectScore(timeRange, authorizedProjectIds);
        // 构造每个项目每个阶段的平均分map,便于后续获取
        Map<String, Map<Integer, ProjectStageScoreEntity>> projectScoreMap = assembleScoreMap(projectScores);

        // 查询有权限的项目
        List<ProjectEntity> projects = projectRepository.selectProjectByIds(authorizedProjectIds);

        BigDecimal totalScore = BigDecimal.ZERO;
        int count = 0;

        // 处理项目评分
        for (ProjectEntity project : projects) {
            ProjectScoreVo scoreVO = processProjectScore(project, projectScoreMap.get(project.getId()));
            if (scoreVO != null) {
                projectList.add(scoreVO);
                BigDecimal finalScore = scoreVO.getFinalScore();
                if (finalScore != null) {
                    totalScore = totalScore.add(finalScore);
                    count++;
                }
            }
        }

        // 计算总平均分
        ProjectAverageScoreVo averageScoreVO = buildAverageScore(projectList, totalScore, count);
        resultVo.setAverage(averageScoreVO);
        return resultVo;
    }

    private ProjectScoreVo processProjectScore(ProjectEntity project, Map<Integer, ProjectStageScoreEntity> stagesScoreMap) {
        if (stagesScoreMap == null) {
            return null;
        }

        ProjectStageScoreEntity businessOpScoreEntity = stagesScoreMap.get(ProjectStageEnum.BUSINESS_OPPORTUNITY.getKey());
        ProjectStageScoreEntity beforeBiddingScoreEntity = stagesScoreMap.get(ProjectStageEnum.BEFORE_BIDDING.getKey());
        ProjectStageScoreEntity biddingStageScoreEntity = stagesScoreMap.get(ProjectStageEnum.BIDDING_STAGE.getKey());

        ProjectScoreVo scoreVO = buildProjectScoreVo(businessOpScoreEntity, beforeBiddingScoreEntity, biddingStageScoreEntity);
        scoreVO.setId(project.getId());
        scoreVO.setProjectName(project.getName());
        scoreVO.setFinalScore(getFinalScore(businessOpScoreEntity, beforeBiddingScoreEntity, biddingStageScoreEntity));
        return scoreVO;
    }

    private ProjectAverageScoreVo buildAverageScore(List<ProjectScoreVo> projectList, BigDecimal totalScore, int count) {
        ProjectAverageScoreVo averageScoreVO = new ProjectAverageScoreVo();
        averageScoreVO.setBusinessOpportunity(calculateAverage(projectList, ProjectScoreVo::getBusinessOpportunity));
        averageScoreVO.setBeforeBidding(calculateAverage(projectList, ProjectScoreVo::getBeforeBidding));
        averageScoreVO.setBid(calculateAverage(projectList, ProjectScoreVo::getBid));

        if (count > 0) {
            averageScoreVO.setTotalAvg(totalScore.divide(BigDecimal.valueOf(count), 1, RoundingMode.HALF_UP));
        }
        return averageScoreVO;
    }


    private ProjectScoreVo buildProjectScoreVo(ProjectStageScoreEntity businessOpScoreEntity, ProjectStageScoreEntity beforeBiddingScoreEntity, ProjectStageScoreEntity biddingStageScoreEntity) {
        ProjectScoreVo scoreVO = new ProjectScoreVo();
        if (businessOpScoreEntity != null && businessOpScoreEntity.getScore() != null) {
            scoreVO.setBusinessOpportunity(businessOpScoreEntity.getScore().setScale(1, RoundingMode.HALF_UP));
        }
        if (beforeBiddingScoreEntity != null && beforeBiddingScoreEntity.getScore() != null) {
            scoreVO.setBeforeBidding(beforeBiddingScoreEntity.getScore().setScale(1, RoundingMode.HALF_UP));
        }
        if (biddingStageScoreEntity != null && biddingStageScoreEntity.getScore() != null) {
            scoreVO.setBid(biddingStageScoreEntity.getScore().setScale(1, RoundingMode.HALF_UP));
        }
        return scoreVO;
    }

    private BigDecimal getFinalScore(ProjectStageScoreEntity businessOpScoreEntity, ProjectStageScoreEntity beforeBiddingScoreEntity, ProjectStageScoreEntity biddingStageScoreEntity) {
        if (biddingStageScoreEntity != null) {
            return biddingStageScoreEntity.getScore();
        }
        if (beforeBiddingScoreEntity != null) {
            return beforeBiddingScoreEntity.getScore();
        }
        if (businessOpScoreEntity != null) {
            return businessOpScoreEntity.getScore();
        }
        return null;
    }

    private List<String> getAuthorizedProjectIds(String userId, String roleCode) {
        List<String> authorizedAreaIds = systemService.getAreaIdsByUserId(userId, roleCode);
        log.debug("Authorized area ids: {}", authorizedAreaIds);
        if (CollectionUtils.isNotEmpty(authorizedAreaIds)) {
            List<String> projectIds = projectRepository.selectProjectByAreaIds(authorizedAreaIds);
            log.debug("Authorized project ids: {}", projectIds);
            return projectIds;
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public ProjectProductsVo selectGuideProductCategories(String projectId) {
        ProjectProductsVo vo = new ProjectProductsVo();
        ProjectEntity projectEntity = projectRepository.getDetail(projectId);
        if (projectEntity != null) {
            vo.setId(projectEntity.getId());
            vo.setProjectName(projectEntity.getName());

            List<IdNameBean> list = projectDashboardRepository.selectGuideProductCategories(projectId);

            if (CollectionUtils.isNotEmpty(list)) {
                List<String> categoryIds = list.stream()
                        .map(IdNameBean::getId)
                        .collect(Collectors.toList());

                List<ProductCategoryInfoVo> productCategoryList = productService.selectProductCategoryList(categoryIds);

                if (CollectionUtils.isNotEmpty(productCategoryList)) {
                    Map<String, ProductCategoryInfoVo> map = productCategoryList.stream()
                            .collect(Collectors.toMap(ProductCategoryInfoVo::getId, Function.identity(), (a, b) -> a));

                    list.forEach(item -> {
                        ProductCategoryInfoVo categoryInfo = map.get(item.getId());
                        if (categoryInfo != null) {
                            item.setName(categoryInfo.getPathName());
                        }
                    });
                }
            }
            vo.setCategoryList(list);
        }
        return vo;
    }

    @Override
    public List<ProductScoreLineChartVo> lineChart(ProjectScoreChartDto chartDTO) {
        if (CollectionUtils.isEmpty(chartDTO.getCategoryIds())) {
            return Collections.emptyList();
        }
        String projectId = chartDTO.getProjectId();
        List<String> categoryIds = chartDTO.getCategoryIds();
        // 批量查询产品小类
        List<ProductCategoryInfoVo> productCategoryList = productService.selectProductCategoryList(categoryIds);
        Map<String, String> productCategoryNameMap = productCategoryList.stream().collect(Collectors.toMap(ProductCategoryInfoVo::getId, ProductCategoryInfoVo::getPathName, (a, b) -> a));
        List<ProductScoreLineChartVo> resultList = new ArrayList<>();
        for (String categoryId : categoryIds) {
            // 查询每个产品小类的图表数据
            ProductScoreLineChartVo chartVo = queryLineChartByOneProductCategory(projectId, categoryId);
            chartVo.setProductCategoryId(categoryId);
            String pathName = productCategoryNameMap.get(categoryId);
            chartVo.setTitle(pathName == null ? null : pathName + " " + I18nUtil.getI18n(ProjectDashboardConstant.I18N_KEY_SCORE));
            resultList.add(chartVo);
        }
        return resultList;
    }

    private ProductScoreLineChartVo queryLineChartByOneProductCategory(String projectId, String categoryId) {
        ProductScoreLineChartVo chartVo = new ProductScoreLineChartVo();
        // 查询单个产品小类所有阶段的引导数据
        List<ProjectStageScoreEntity> list = projectDashboardRepository.selectLineChartData(projectId, categoryId);
        if (CollectionUtils.isEmpty(list)) {
            chartVo.setChartData(new ArrayList<>());
        } else {
            List<ProductScoreLineChartDataVo> chartDataList = new ArrayList<>();
            // 按阶段分组,使用treeMap按阶段排好序
            Map<Integer, List<ProjectStageScoreEntity>> stagedMap = list.stream().collect(Collectors.groupingBy(ProjectStageScoreEntity::getStage, TreeMap::new, Collectors.toList()));
            for (Map.Entry<Integer, List<ProjectStageScoreEntity>> entry : stagedMap.entrySet()) {
                ProductScoreLineChartDataVo chartDataVo = new ProductScoreLineChartDataVo();
                Integer stage = entry.getKey();
                List<ProjectStageScoreEntity> scores = entry.getValue();
                // 获取阶段标题
                String stageName = getStageName(stage, scores);
                // 按时间升序排序
                List<ChartDataPoint> dataPoints = getSortedChartDataPoints(scores);
                chartDataVo.setName(stageName);
                chartDataVo.setDataPoints(dataPoints);
                chartDataList.add(chartDataVo);
            }
            chartVo.setChartData(chartDataList);
        }
        return chartVo;
    }

    private List<ChartDataPoint> getSortedChartDataPoints(List<ProjectStageScoreEntity> scores) {
        // 按时间升序排序
        Collections.sort(scores, (o1, o2) -> {
            LocalDateTime time1 = LocalDateTime.parse(o1.getRecordTime(), DateTimeUtils.DATE_TIME_FORMATTER);
            LocalDateTime time2 = LocalDateTime.parse(o2.getRecordTime(), DateTimeUtils.DATE_TIME_FORMATTER);
            return time1.compareTo(time2);
        });
        // 转换为返回的对象
        return scores.stream().map((item) -> {
            ChartDataPoint point = new ChartDataPoint();
            point.setX(item.getRecordTime());
            point.setY(item.getScore());
            return point;
        }).collect(Collectors.toList());
    }

    private String getStageName(Integer stage, List<ProjectStageScoreEntity> scores) {
        String stageName = ProjectStageEnum.getValueByKey(stage);
        if (StringUtils.isBlank(stageName)) {
            throw new IllegalStateException("illegal stage: " + stage);
        }

        // 获取最小 recordTime
        Optional<LocalDateTime> minRecordTime = scores.stream()
                .map(entity -> LocalDateTime.parse(entity.getRecordTime(), DateTimeUtils.DATE_TIME_FORMATTER))
                .min(LocalDateTime::compareTo);
        // 获取最大 recordTime
        Optional<LocalDateTime> maxRecordTime = scores.stream()
                .map(entity -> LocalDateTime.parse(entity.getRecordTime(), DateTimeUtils.DATE_TIME_FORMATTER))
                .max(LocalDateTime::compareTo);
        // 拼接阶段名称
        StringBuilder sb = new StringBuilder();
        sb.append(stageName).append("(")
                .append(minRecordTime.orElseThrow(() -> new NoSuchElementException("Min record time not found")).format(DateTimeUtils.DATE_FORMATTER))
                .append("~")
                .append(maxRecordTime.orElseThrow(() -> new NoSuchElementException("Max record time not found")).format(DateTimeUtils.DATE_FORMATTER))
                .append(")");
        return sb.toString();
    }

    private Map<String, Map<Integer, ProjectStageScoreEntity>> assembleScoreMap(List<ProjectStageScoreEntity> projectScores) {
        Map<String, Map<Integer, ProjectStageScoreEntity>> resultMap = projectScores.stream()
                .collect(Collectors.groupingBy(
                        ProjectStageScoreEntity::getProjectId,
                        Collectors.toMap(
                                ProjectStageScoreEntity::getStage,
                                projectStageScoreEntity -> projectStageScoreEntity,
                                (a, b) -> a
                        )
                ));
        return resultMap;
    }

    private static BigDecimal calculateAverage(List<ProjectScoreVo> list, Function<ProjectScoreVo, BigDecimal> scoreExtractor) {
        BigDecimal sum = BigDecimal.ZERO;
        int count = 0;

        for (ProjectScoreVo item : list) {
            BigDecimal score = scoreExtractor.apply(item);
            if (score != null) {
                sum = sum.add(score);
                count++;
            }
        }

        // 如果没有有效的元素，返回 null
        if (count == 0) {
            return null;
        }

        // 计算平均值，保留 1 位小数
        return sum.divide(BigDecimal.valueOf(count), 1, RoundingMode.HALF_UP);
    }

    private static BigDecimal calculateAverage(BigDecimal... values) {
        BigDecimal sum = BigDecimal.ZERO;
        int count = 0;

        for (BigDecimal num : values) {
            if (num != null) {
                sum = sum.add(num);
                count++;
            }
        }

        if (count == 0) {
            return null;
        }
        return sum.divide(BigDecimal.valueOf(count), 1, RoundingMode.HALF_UP);
    }

}
