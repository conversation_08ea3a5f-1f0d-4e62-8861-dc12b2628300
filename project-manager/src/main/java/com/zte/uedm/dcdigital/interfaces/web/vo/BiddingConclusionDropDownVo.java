/* Started by AICoder, pid:49a0bc5f6311a9f14a3109c670186532f784fbb2 */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import com.zte.uedm.dcdigital.interfaces.web.dto.CommonDropDownOption;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class BiddingConclusionDropDownVo {

    /**
     * 客户类别选项列表
     */
    private List<CommonDropDownOption> customerTypeOptions;

    /**
     * 营销类型选项列表
     */
    private List<CommonDropDownOption> marketingTypeOptions;

    /**
     * 项目金额类型选项列表
     */
    private List<CommonDropDownOption> projectAmountTypeOptions;

    /**
     * 项目类型选项列表
     */
    private List<CommonDropDownOption> projectTypeOptions;
}

/* Ended by AICoder, pid:49a0bc5f6311a9f14a3109c670186532f784fbb2 */