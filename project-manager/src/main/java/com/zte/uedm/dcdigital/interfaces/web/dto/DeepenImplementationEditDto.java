package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 深化实施编辑DTO
 */
@Getter
@Setter
@ToString
public class DeepenImplementationEditDto {

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    private String id;

    /**
     * 设计负责人
     */
    @NotBlank(message = "设计负责人不能为空")
    private String designDirector;

    /**
     * 启动时间，非必填：时间选择，精确到天
     */
    private String startTime;

    /**
     * 要求提资完成时间，非必填：时间选择，精确到天
     */
    private String requestInformationCompletionTime;

    /**
     * 实际提资完成时间，非必填：时间选择，精确到天
     */
    private String actualInformationCompletionTime;

    /**
     * 深化设计开始时间，非必填：时间选择，精确到天
     */
    private String deepenDesignStartTime;

    /**
     * 深化设计结束时间，非必填：时间选择，精确到天
     */
    private String deepensDesignEndTime;

    /**
     * 合图开始时间，非必填：时间选择，精确到天
     */
    private String combinedImageStartTime;

    /**
     * 合图结束时间，非必填：时间选择，精确到天
     */
    private String combinedImageEndTime;

    /**
     * BIM专项设计开始时间，非必填：时间选择，精确到天
     */
    private String bIMSpecialDesignStartTime;

    /**
     * BIM专项设计结束时间，非必填：时间选择，精确到天
     */
    private String bIMSpecialDesignEndTime;

    /**
     * 首批备料清单输出时间，非必填：时间选择，精确到天
     */
    private String firstBatchMaterialPreparationOutputTime;

    /**
     * 长周期物料清单输出时间，非必填：时间选择，精确到天
     */
    private String longPeriodMaterialListOutputTime;

    /**
     * 整体备料清单锁定时间，非必填：时间选择，精确到天
     */
    private String overallMaterialPreparationLockTime;

    /**
     * 工程深化进度，必填，取值范围：0-100
     */
    @NotNull(message = "工程深化进度不能为空")
    @Min(value = 0, message = "工程深化进度不能小于0")
    @Max(value = 100, message = "工程深化进度不能大于100")
    private Integer engineeringDeepenProgress;

    /**
     * 深化设计状态，必填，下拉框选项：A正常；B延误（客户需求变化）；B延误（产品原因）；B延误（商务原因）；B延误（其它）
     */
    @NotBlank(message = "深化设计状态不能为空")
    private String deepenDesignStatus;

    /**
     * 延误原因，仅当状态为"延误"时必填，最多输入1000个字符
     */
    private String delayReason;

    /**
     * 修改深化设计开始时间，非必填：时间选择，精确到天
     */
    private String modifyDeepenDesignStartTime;

    /**
     * 修改深化设计结束时间，非必填：时间选择，精确到天
     */
    private String modifyDeepenDesignEndTime;

    /**
     * 施工图校正开始时间，非必填：时间选择，精确到天
     */
    private String constructionDrawingCalibrationStartTime;

    /**
     * 施工图校正结束时间，非必填：时间选择，精确到天
     */
    private String constructionDrawingCalibrationEndTime;

    /**
     * 施工图修订开始时间，非必填：时间选择，精确到天
     */
    private String constructionDrawingRevisionStartTime;
}
