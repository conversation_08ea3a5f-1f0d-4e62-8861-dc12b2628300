package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.projectArea.executor.ProjectAreaCommandService;
import com.zte.uedm.dcdigital.application.projectArea.executor.ProjectAreaQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProjectAreaMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectAreaPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAreaAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAreaQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAreaUpdateDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProjectAreaVo;
import com.zte.uedm.dcdigital.log.annotation.DcOperationLog;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@Path("/aportal/project-area")
@Api(value = "项目地区", tags = {"项目地区接口"})
@Controller
public class ProjectAreaAPortalController {

    @Autowired
    private ProjectAreaCommandService commandService;
    @Autowired
    private ProjectAreaQueryService queryService;

    //查询项目地区
    @POST
    @Path("/search")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "查询项目地区", notes = "查询项目地区", httpMethod = "POST")
    public BaseResult<Object> queryProjectAreaList(ProjectAreaQueryDto queryDto){
        PageVO<ProjectAreaVo> pageVO = queryService.queryByCondition(queryDto);
        return BaseResult.success(pageVO);
    }

    @GET
    @Path("get-by-id")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "查询项目地区详情", notes = "查询项目地区详情", httpMethod = "GET")
    public BaseResult<Object> queryProjectAreaById(@Valid @NotBlank @QueryParam("id") String id){
        ProjectAreaVo projectAreaVo = queryService.getById(id);
        return BaseResult.success(projectAreaVo);
    }

    @POST
    @Path("/add")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "新增项目地区", notes = "新增项目地区", httpMethod = "POST")
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-project-manager",
            operation = "ProjectAreaAddDtoDescription", targetClass = ProjectAreaAddDto.class)
    public BaseResult<Object> addProjectArea(ProjectAreaAddDto projectAreaAddDto){
        projectAreaAddDto.parameterVerification();
        commandService.addProjectArea(projectAreaAddDto);
        return BaseResult.success();
    }

    //修改项目地区
    @POST
    @Path("/update")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "修改项目地区", notes = "修改项目地区", httpMethod = "POST")
    @DcOperationLog(method = OperationMethodEnum.UPDATE, module = "module-project-manager", mapperName = ProjectAreaMapper.class,
            operation = "ProjectAreaUpdateDtoDescription", targetClass = ProjectAreaUpdateDto.class)
    public BaseResult<Object> updateProjectArea(ProjectAreaUpdateDto updateDto){
        updateDto.parameterVerification();
        commandService.updateProjectArea(updateDto);
        return BaseResult.success();
    }

    //删除项目地区
    @POST
    @Path("/delete")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "删除项目地区", notes = "删除项目地区", httpMethod = "POST")
    @DcOperationLog(method = OperationMethodEnum.DELETE, module = "module-project-manager",mapperName = ProjectAreaMapper.class,
            operation = "ProjectAreaPoDescription", targetClass = ProjectAreaPo.class)
    public BaseResult<Object> deleteProjectArea(@QueryParam("id") String id){
        commandService.deleteProjectArea(id);
        return BaseResult.success();
    }
}
