/* Started by AICoder, pid:82a8dy526em76da143e90a7550bec01797d0455f */
package com.zte.uedm.dcdigital.application.project;

import com.zte.uedm.dcdigital.common.bean.project.OpportunitySupportInnerDto;

public interface OpportunitySupportCommandService {

    void addOpportunitySupport(OpportunitySupportInnerDto supportInnerDto);

    void updateOpportunitySupport(OpportunitySupportInnerDto supportInnerDto);
}

/* Ended by AICoder, pid:82a8dy526em76da143e90a7550bec01797d0455f */