package com.zte.uedm.dcdigital.interfaces.web.controller;

import com.zte.uedm.dcdigital.application.project.MaterialAssociationCommandService;
import com.zte.uedm.dcdigital.application.project.MaterialAssociationQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.dto.AssociatedMaterialQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.MaterialAssociationDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAssociatedMaterialQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.AssociatedMaterialVo;
import com.zte.uedm.dcdigital.log.annotation.DcOperationLog;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationLogRankEnum;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * 物料选型
 */
@Slf4j
@Path("/uportal/project/material-association")
@Component
public class MaterialAssociationController {

    @Autowired
    private MaterialAssociationQueryService materialAssociationQueryService;
    @Autowired
    private MaterialAssociationCommandService materialAssociationCommandService;

    @POST
    @Path("/material/associated-simple")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<List<IdNameBean>> queryAssociatedMaterialsSimple(@QueryParam("billId") String billId) {
        if (StringUtils.isBlank(billId)) {
            return BaseResult.paramError("billId must be not empty");
        }
        List<IdNameBean> list = materialAssociationQueryService.queryAssociatedMaterialsSimple(billId);
        return BaseResult.success(list);
    }

    @POST
    @Path("/material/associated")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<PageVO<AssociatedMaterialVo>> queryAssociatedMaterials(AssociatedMaterialQueryDto queryDto) {
        ValidResult validResult = ValidateUtils.validateObj(queryDto);
        if (validResult.isError()) {
            return BaseResult.paramError(validResult.getErrorMessage());
        }
        PageVO<AssociatedMaterialVo> pageVO = materialAssociationQueryService.queryPagingAssociatedMaterials(queryDto);
        return BaseResult.success(pageVO);
    }

    /* Started by AICoder, pid:ye09115f66u6d7714c03090c908acb08dfe78b8f */
    @POST
    @Path("/material/no-page-associated")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<List<AssociatedMaterialVo>> noPageQueryAssociatedMaterials(AssociatedMaterialQueryDto queryDto) {
        List<AssociatedMaterialVo> associatedMaterialVoList = materialAssociationQueryService.noPageQueryPagingAssociatedMaterials(queryDto);
        return BaseResult.success(associatedMaterialVoList);
    }

    /* Ended by AICoder, pid:ye09115f66u6d7714c03090c908acb08dfe78b8f */

    @POST
    @Path("/do-associate")
    @Produces({MediaType.APPLICATION_JSON})
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-project-manager",operation = "MaterialAssociationDescription",
            targetClass= MaterialAssociationDto.class, rank = OperationLogRankEnum.IMPORTANT)
    public BaseResult<List<IdNameBean>> doAssociate(MaterialAssociationDto dto) {
        ValidResult validResult = ValidateUtils.validateObj(dto);
        if (validResult.isError()) {
            return BaseResult.paramError(validResult.getErrorMessage());
        }
        materialAssociationCommandService.doAssociate(dto);
        return BaseResult.success();
    }

    @POST
    @Path("/details")
    @Produces({MediaType.APPLICATION_JSON})
    public BaseResult<PageVO<AssociatedMaterialVo>> queryAllAssociatedMaterials(ProjectAssociatedMaterialQueryDto queryDto) {
        ValidResult validResult = ValidateUtils.validateObj(queryDto);
        if (validResult.isError()) {
            return BaseResult.paramError(validResult.getErrorMessage());
        }
        PageVO<AssociatedMaterialVo> pageVO = materialAssociationQueryService.queryAssociatedMaterials(queryDto);
        return BaseResult.success(pageVO);
    }

}
