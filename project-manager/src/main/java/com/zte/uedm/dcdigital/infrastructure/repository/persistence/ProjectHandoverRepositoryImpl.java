/* Started by AICoder, pid:r27a4y039co0187146460931705aef4a9312e806 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectHandoverEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ProjectHandoverRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ProjectHandoverConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProjectHandoverMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectHandoverPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class ProjectHandoverRepositoryImpl implements ProjectHandoverRepository {

    @Autowired
    private ProjectHandoverMapper projectHandoverMapper;
    /**
     * 根据项目id查询售前售后交接信息
     * */
    @Override
    public ProjectHandoverEntity getProjectHandoverInfoByItemId(String itemId) {
        ProjectHandoverPo projectHandoverPo = projectHandoverMapper.selectProjectHandoverByItemId(itemId);
        return ProjectHandoverConverter.INSTANCE.poToEntity(projectHandoverPo);
    }
    /**
     * 根据主键id查询售前售后交接信息
     * */
    @Override
    public ProjectHandoverEntity getProjectHandoverInfoById(String id) {
        ProjectHandoverPo projectHandoverPo = projectHandoverMapper.selectProjectHandoverById(id);
        return ProjectHandoverConverter.INSTANCE.poToEntity(projectHandoverPo);
    }

    /**
     * 增加售前售后交接信息
     * */
    @Override
    public int addProjectHandoverInfo(ProjectHandoverEntity projectHandoverEntity) {
        ProjectHandoverPo projectHandoverPo = ProjectHandoverConverter.INSTANCE.entityToPo(projectHandoverEntity);
        return projectHandoverMapper.insertProjectHandover(projectHandoverPo);
    }
    /**
     * 修改售前售后交接信息
     * */
    @Override
    public int updateProjectHandoverInfoById(ProjectHandoverEntity projectHandoverEntity) {
        ProjectHandoverPo projectHandoverPo = ProjectHandoverConverter.INSTANCE.entityToPo(projectHandoverEntity);
        return projectHandoverMapper.updateProjectHandoverById(projectHandoverPo);
    }
}

/* Ended by AICoder, pid:r27a4y039co0187146460931705aef4a9312e806 */