package com.zte.uedm.dcdigital.interfaces.web.dto;


/* Started by AICoder, pid:fd144l03a809ea814a8a0a09d19b9b0aacb0e7ea */
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.enums.ProjectStageEnum;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProjectStatusCode;
import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import com.zte.uedm.dcdigital.security.annotation.DcResourceField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Setter
@Getter
@ToString
@Slf4j
public class ProjectAddDto {

    /**
     * 项目名称
     */
    @NotBlank(message = "The project name cannot be empty")
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    private String name;

    /**
     * 地区Id
     */
    @NotBlank(message = "The area cannot be empty")
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    @DcResourceField
    private String areaId;

    /**
     * 项目阶段
     */
    @NotNull(message = "The project stage cannot be empty")
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    private Integer projectStage;

    /**
     * 客户名称
     */
    @NotBlank(message = "The customer name cannot be empty")
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    private String customerName;

    /**
     * 方案SE
     */
    @NotEmpty(message = "The scheme se cannot be empty")
    private List<UserDto> schemeSE;

    /**
     * 交付TD
     */
    private List<UserDto> deliverTD;

    /**
     * 成本总监
     */
    private List<UserDto> costDirector;

    /**
     * 商务总监
     */
    private List<UserDto> businessDirector;

    /**
     * 物流总监
     */
    private List<UserDto> logisticsDirector;

    /**
     * 备注
     */
    private String notes;

    public void parameterVerification() throws BusinessException {
        ValidResult validResult = ValidateUtils.validateObj(this);
        // 参数不能为空
        if (validResult.isError()) {
            log.error("[ProductCategoryEditDto check param] Invalid parameter: {}", validResult.getErrorMessage());
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
        // 项目名称不能超过200个字符
        if(name.trim().length() > GlobalConstants.NAME_LENGTH_LIMIT){
            log.error("[ProductCategoryEditDto check param] Invalid parameter: {}", "The project name cannot be more than 200 characters");
            throw new BusinessException(ProjectStatusCode.PROJECT_NAME_LENGTH);
        }
        // 客户名称不能超过200个字符
        if(customerName.trim().length() > GlobalConstants.NAME_LENGTH_LIMIT){
            log.error("[ProductCategoryEditDto check param] Invalid parameter: {}", "The customer name cannot be more than 200 characters");
            throw new BusinessException(ProjectStatusCode.CUSTOMER_NAME_LENGTH_LIMIT);
        }
        if(notes != null && notes.trim().length() > GlobalConstants.DESCRIPTION_LENGTH_LIMIT){
            log.error("[ProductCategoryEditDto check param] Invalid parameter: {}", "The notes cannot be more than 500 characters");
            throw new BusinessException(ProjectStatusCode.NOTES_LENGTH_LIMIT);
        }
    }

    /**
     * 项目新增时，校验项目阶段
     */
    public void projectStageInspection(){
        //新增项目，项目阶段限制在标前阶段和商机阶段

        if(!ProjectStageEnum.BUSINESS_OPPORTUNITY.getKey().equals(projectStage) && !ProjectStageEnum.BEFORE_BIDDING.getKey().equals(projectStage)){
            log.error("[Project check param] Invalid parameter,New project, project phase limited to business opportunity phase or pre bid phase: error projectStage:{}", projectStage);
            throw new BusinessException(ProjectStatusCode.PROJECT_ADD_STAGE_ERROR);
        }
    }
}
/* Ended by AICoder, pid:fd144l03a809ea814a8a0a09d19b9b0aacb0e7ea */
