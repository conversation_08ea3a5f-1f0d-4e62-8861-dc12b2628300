/* Started by AICoder, pid:230baa45f174c4614b9509ac105ca522b7f1d5e3 */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BiddingConclusionPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BiddingConclusionMapper extends BaseMapper<BiddingConclusionPo> {

    int insertBiddingConclusion(BiddingConclusionPo biddingConclusionPo);

    int updateBiddingConclusionById(BiddingConclusionPo biddingConclusionPo);

    int deleteBiddingConclusionById(String id);

    BiddingConclusionPo selectBiddingConclusionById(String id);

    List<BiddingConclusionPo> selectBiddingConclusionList(BiddingConclusionPo biddingConclusionPo);

    BiddingConclusionPo selectBiddingConclusionByProjectId(String projectId);
}

/* Ended by AICoder, pid:230baa45f174c4614b9509ac105ca522b7f1d5e3 */