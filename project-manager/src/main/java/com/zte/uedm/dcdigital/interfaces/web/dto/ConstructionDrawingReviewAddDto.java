package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 施工图会审记录新增DTO
 */
@Getter
@Setter
@ToString
public class ConstructionDrawingReviewAddDto {

    /**
     * 项目ID，所属项目标识
     */
    @NotBlank(message = "项目ID不能为空")
    private String itemId;

    /**
     * 会审时间，默认填写当前时间，精确到天
     */
    @NotBlank(message = "会审时间不能为空")
    private String reviewTime;

    /**
     * 会审纪要，必填，最多输入3000个字符
     */
    @NotBlank(message = "会审纪要不能为空")
    @Size(max = 3000, message = "会审纪要不能超过3000个字符")
    private String summary;
}
