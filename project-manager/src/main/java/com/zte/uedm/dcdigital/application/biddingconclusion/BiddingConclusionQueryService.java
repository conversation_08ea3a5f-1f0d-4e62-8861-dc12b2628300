package com.zte.uedm.dcdigital.application.biddingconclusion;

import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingConclusionDropDownVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingConclusionVo;

public interface BiddingConclusionQueryService {
    /**
     * 获取投标结论下拉框数据
     * */
    BiddingConclusionDropDownVo queryDropDown();

    /**
     * 根据商机id查询投标结论是否是首次填写
     * */
    boolean getFirstFill(String projectId);

    /**
     * 根据商机id查询投标结论详情
     * */
    BiddingConclusionVo getDetail(String projectId);
}
