/* Started by AICoder, pid:1672158bd59f9d0146510ba870ef0136b4f25b9b */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BiddingDocumentClarificationPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingDocumentClarificationQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BiddingDocumentClarificationMapper extends BaseMapper<BiddingDocumentClarificationPo> {

    /**
     * 根据提供的查询条件获取 BiddingDocumentClarificationPo 列表。
     *
     * @param queryDto 包含查询条件的 BiddingDocumentClarificationQueryDto 对象。
     * @return 符合查询条件的 BiddingDocumentClarificationPo 列表。
     */
    List<BiddingDocumentClarificationPo> queryListByCondition(@Param("queryDto") BiddingDocumentClarificationQueryDto queryDto);

    /**
     * 根据项目 ID 获取 BiddingDocumentClarificationPo 列表。
     *
     * @param projectId 要查询的项目 ID。
     * @return 与给定项目 ID 关联的 BiddingDocumentClarificationPo 列表。
     */
    List<BiddingDocumentClarificationPo> queryByProjectId(@Param("projectId") String projectId);
}

/* Ended by AICoder, pid:1672158bd59f9d0146510ba870ef0136b4f25b9b */