-- Started by AICoder, pid:7000e64978m0933140b20869805c611aef22666d
DROP TABLE IF EXISTS project_area;
CREATE TABLE project_area (
    id TEXT NOT NULL PRIMARY KEY,
    area_name TEXT NOT NULL,
    parent_id TEXT,
    path_name TEXT NOT NULL,
    path_id TEXT NOT NULL,
    area_level INTEGER NOT NULL,
    create_time TEXT NOT NULL,
    update_time TEXT NOT NULL,
    create_by TEXT NOT NULL,
    update_by TEXT NOT NULL
);
-- Ended by AICoder, pid:7000e64978m0933140b20869805c611aef22666d
-- Started by AICoder, pid:0b2b5rc4db89f6b145cb09a3607da11ba379d3e7
COMMENT ON COLUMN project_area.id IS '唯一标识符 (id)';
COMMENT ON COLUMN project_area.area_name IS '地区名称';
COMMENT ON COLUMN project_area.parent_id IS '父级地区的 id，如果没有父级则为空字符串';
COMMENT ON COLUMN project_area.path_name IS '地区的路径名称，例如 ''path1/path2/path3''';
COMMENT ON COLUMN project_area.path_id IS '地区路径的 id，例如 ''id1/id2/id3''';
COMMENT ON COLUMN project_area.area_level IS '地区层级，0 表示所有地区，1 表示一级地区，2 表示二级地区';
COMMENT ON COLUMN project_area.create_time IS '创建时间';
COMMENT ON COLUMN project_area.update_time IS '更新时间';
COMMENT ON COLUMN project_area.create_by IS '创建者';
COMMENT ON COLUMN project_area.update_by IS '更新者';
-- Ended by AICoder, pid:0b2b5rc4db89f6b145cb09a3607da11ba379d3e7
-- 默认手动创建所有地区
insert into project_area
    (id, area_name, parent_id, path_name, path_id, area_level, create_time, update_time, create_by, update_by)
    values
    ('root', '所有地区', NULL, 'root', 'root', 0, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'system', 'system');


-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS brand_guide;

-- 创建品牌引导表
CREATE TABLE brand_guide (
    id TEXT NOT NULL PRIMARY KEY,
    status INTEGER NOT NULL,
    project_id TEXT NOT NULL,
    stage INTEGER NOT NULL,
    version TEXT,
    record_time TEXT NOT NULL,
    record_user TEXT NOT NULL,
    create_by TEXT NOT NULL,
    create_time TEXT NOT NULL,
    update_by TEXT NOT NULL,
    update_time TEXT NOT NULL
);

-- 添加列注释
COMMENT ON COLUMN brand_guide.id IS '主键，每次引导生成新的id';
COMMENT ON COLUMN brand_guide.status IS '状态(0:暂存;1:已提交)';
COMMENT ON COLUMN brand_guide.project_id IS '项目id';
COMMENT ON COLUMN brand_guide.stage IS '引导阶段(1:商机阶段;2:标前阶段;3:投标阶段;4:交付阶段)';
COMMENT ON COLUMN brand_guide.version IS '版本号，暂存状态可为空，提交后写入版本号，如“V1.0”';
COMMENT ON COLUMN brand_guide.record_time IS '记录时间';
COMMENT ON COLUMN brand_guide.record_user IS '记录人';
COMMENT ON COLUMN brand_guide.create_by IS '创建人';
COMMENT ON COLUMN brand_guide.create_time IS '创建时间';
COMMENT ON COLUMN brand_guide.update_by IS '更新人';
COMMENT ON COLUMN brand_guide.update_time IS '更新时间';

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS brand_guide_detail;

-- 创建品牌引导明细表
CREATE TABLE brand_guide_detail (
    id TEXT NOT NULL PRIMARY KEY,
    brand_guide_id TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    brand TEXT,
    brand_id TEXT,
    tech_spec TEXT,
    grade DECIMAL NOT NULL,
    create_by TEXT NOT NULL,
    create_time TEXT NOT NULL,
    update_by TEXT NOT NULL,
    update_time TEXT NOT NULL
);

-- 添加列注释
COMMENT ON COLUMN brand_guide_detail.id IS '主键';
COMMENT ON COLUMN brand_guide_detail.brand_guide_id IS '引导id，brand_guide表id';
COMMENT ON COLUMN brand_guide_detail.product_category_id IS '产品小类id';
COMMENT ON COLUMN brand_guide_detail.brand IS '引导品牌';
COMMENT ON COLUMN brand_guide_detail.brand_id IS '引导品牌id';
COMMENT ON COLUMN brand_guide_detail.tech_spec IS '技术规范';
COMMENT ON COLUMN brand_guide_detail.grade IS '评分(1-10分，可以有小数例如1.1，9.9)';
COMMENT ON COLUMN brand_guide_detail.create_by IS '创建人';
COMMENT ON COLUMN brand_guide_detail.create_time IS '创建时间';
COMMENT ON COLUMN brand_guide_detail.update_by IS '更新人';
COMMENT ON COLUMN brand_guide_detail.update_time IS '更新时间';


-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS short_brand;

-- 创建短名单品牌表
CREATE TABLE short_brand (
    id TEXT NOT NULL PRIMARY KEY,
    brand_guide_detail_id TEXT NOT NULL,
    short_brand TEXT NOT NULL
);

-- 添加列注释
COMMENT ON COLUMN short_brand.id IS '主键';
COMMENT ON COLUMN short_brand.brand_guide_detail_id IS '引导明细id';
COMMENT ON COLUMN short_brand.short_brand IS '短名单品牌';



-- Started by AICoder, pid:8184a9d0f9k0a5514085097920b9d738a6471b59
CREATE TABLE project (
                         id TEXT NOT NULL,  -- 项目ID
                         name TEXT NOT NULL,  -- 项目名称
                         area_id TEXT NOT NULL,  -- 地区ID
                         project_stage INTEGER NOT NULL,  -- 项目阶段，枚举1商机阶段、2标前阶段、3投标阶段、4交付阶段
                         notes TEXT,  -- 备注
                         customer TEXT NOT NULL,  -- 客户
                         scheme_se TEXT,  -- 方案SE
                         deliver_td TEXT,  -- 交付TD
                         cost_director TEXT,  -- 成本总监
                         business_director TEXT,  -- 商务总监
                         logistics_director TEXT,  -- 物流总监
                         create_time TEXT NOT NULL,  -- 创建时间
                         update_time TEXT NOT NULL,  -- 更新时间
                         create_by TEXT NOT NULL,  -- 创建用户
                         create_name TEXT NOT NULL,  -- 创建人
                         update_by TEXT NOT NULL,  -- 更新用户
                         PRIMARY KEY (id)
);

-- 添加注释以描述字段含义
COMMENT ON COLUMN project.id IS '项目ID';
COMMENT ON COLUMN project.name IS '项目名称';
COMMENT ON COLUMN project.area_id IS '地区ID';
COMMENT ON COLUMN project.project_stage IS '项目阶段，枚举1商机阶段、2标前阶段、3投标阶段、4交付阶段';
COMMENT ON COLUMN project.notes IS '备注';
COMMENT ON COLUMN project.customer IS '客户';
COMMENT ON COLUMN project.scheme_se IS '方案SE';
COMMENT ON COLUMN project.deliver_td IS '交付TD';
COMMENT ON COLUMN project.cost_director IS '成本总监';
COMMENT ON COLUMN project.business_director IS '商务总监';
COMMENT ON COLUMN project.logistics_director IS '物流总监';
COMMENT ON COLUMN project.create_time IS '创建时间';
COMMENT ON COLUMN project.update_time IS '更新时间';
COMMENT ON COLUMN project.create_by IS '创建用户';
COMMENT ON COLUMN project.create_name IS '创建人';
COMMENT ON COLUMN project.update_by IS '更新用户';
-- Ended by AICoder, pid:8184a9d0f9k0a5514085097920b9d738a6471b59

DROP TABLE IF EXISTS launch_bidding;
CREATE TABLE launch_bidding (
    id TEXT NOT NULL PRIMARY KEY, -- 主键ID
    bid_issuing_time TEXT NOT NULL, -- 发标时间
    clarify_submission_time TEXT NOT NULL, -- 澄清提交时间
    clarify_submission_complete_time TEXT NULL, -- 完成澄清提交时间
    configure_manifest_lock_time TEXT NOT NULL, -- 配置清单锁定时间
    configure_manifest_lock_time_complete_time TEXT NULL, -- 完成配置清单锁定时间
    bidding_documents_finalization_time TEXT NOT NULL, -- 招标文件定稿时间
    bidding_documents_finalization_completion_time TEXT NULL, -- 完成招标文件定稿时间
    bid_submission_time TEXT NOT NULL, -- 交标时间
    create_by TEXT NOT NULL, -- 创建人
    create_time TEXT NOT NULL, -- 创建时间
    update_by TEXT NOT NULL, -- 更新人
    update_time TEXT NOT NULL -- 更新时间
);
COMMENT ON COLUMN launch_bidding.id IS '主键同项目id';
COMMENT ON COLUMN launch_bidding.bid_issuing_time IS '发标时间';
COMMENT ON COLUMN launch_bidding.clarify_submission_time IS '澄清提交时间';
COMMENT ON COLUMN launch_bidding.clarify_submission_complete_time IS '完成澄清提交时间';
COMMENT ON COLUMN launch_bidding.configure_manifest_lock_time IS '配置清单锁定时间';
COMMENT ON COLUMN launch_bidding.configure_manifest_lock_time_complete_time IS '完成配置清单锁定时间';
COMMENT ON COLUMN launch_bidding.bidding_documents_finalization_time IS '招标文件定稿时间';
COMMENT ON COLUMN launch_bidding.bidding_documents_finalization_completion_time IS '完成招标文件定稿时间';
COMMENT ON COLUMN launch_bidding.bid_submission_time IS '交标时间';
COMMENT ON COLUMN launch_bidding.create_by IS '创建人';
COMMENT ON COLUMN launch_bidding.create_time IS '创建时间';
COMMENT ON COLUMN launch_bidding.update_by IS '更新人';
COMMENT ON COLUMN launch_bidding.update_time IS '更新时间';

-- Started by AICoder, pid:59dcdfa67as0edf14de70830204d7f360bc029a7
-- 标书澄清表
DROP TABLE IF EXISTS bidding_document_clarification;
CREATE TABLE bidding_document_clarification (
    id TEXT NOT NULL PRIMARY KEY,
    project_id TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    file_name TEXT NOT NULL,
    chapter_number TEXT NOT NULL,
    page_number TEXT NOT NULL,
    file_content TEXT NOT NULL,
    clarification TEXT NOT NULL,
    file_ids TEXT, -- 允许为空
    create_by TEXT NOT NULL,
    create_time TEXT NOT NULL,
    update_by TEXT NOT NULL,
    update_time TEXT NOT NULL
);

COMMENT ON COLUMN bidding_document_clarification.id IS '主键';
COMMENT ON COLUMN bidding_document_clarification.project_id IS '项目ID';
COMMENT ON COLUMN bidding_document_clarification.product_category_id IS '产品小类ID';
COMMENT ON COLUMN bidding_document_clarification.file_name IS '文件名称';
COMMENT ON COLUMN bidding_document_clarification.chapter_number IS '章节号';
COMMENT ON COLUMN bidding_document_clarification.page_number IS '所在页码';
COMMENT ON COLUMN bidding_document_clarification.file_content IS '招标文件原文';
COMMENT ON COLUMN bidding_document_clarification.clarification IS '澄清问题';
COMMENT ON COLUMN bidding_document_clarification.file_ids IS '文件ID列表，允许为空';
COMMENT ON COLUMN bidding_document_clarification.create_by IS '创建人';
COMMENT ON COLUMN bidding_document_clarification.create_time IS '创建时间';
COMMENT ON COLUMN bidding_document_clarification.update_by IS '更新人';
COMMENT ON COLUMN bidding_document_clarification.update_time IS '更新时间';
-- Ended by AICoder, pid:59dcdfa67as0edf14de70830204d7f360bc029a7
DROP TABLE IF EXISTS bill_of_quantity;
CREATE TABLE bill_of_quantity (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    ascription_id TEXT NOT NULL,
    characteristic TEXT NULL,
    unit_of_measurement TEXT,
    quantity DECIMAL(10, 2),
    sub_project TEXT,
    supply TEXT,
    product_subcategory TEXT,
    one_time_unloading TEXT,
    equipment_positioning TEXT,
    installation_supervision TEXT,
    commissioning TEXT,
    patrol_inspection TEXT,
    create_by TEXT NOT NULL,
    create_time TEXT NOT NULL,
    update_by TEXT NOT NULL,
    update_time TEXT NOT NULL
);
COMMENT ON COLUMN bill_of_quantity.id IS '主键';
COMMENT ON COLUMN bill_of_quantity.name IS '工程清单名称';
COMMENT ON COLUMN bill_of_quantity.ascription_id IS '归属id';
COMMENT ON COLUMN bill_of_quantity.characteristic IS '特征';
COMMENT ON COLUMN bill_of_quantity.unit_of_measurement IS '计量单位';
COMMENT ON COLUMN bill_of_quantity.quantity IS '工程量';
COMMENT ON COLUMN bill_of_quantity.sub_project IS '所属工程分项';
COMMENT ON COLUMN bill_of_quantity.supply IS '供货';
COMMENT ON COLUMN bill_of_quantity.product_subcategory IS '所属产品小类';
COMMENT ON COLUMN bill_of_quantity.one_time_unloading IS '一次卸货';
COMMENT ON COLUMN bill_of_quantity.equipment_positioning IS '设备就位';
COMMENT ON COLUMN bill_of_quantity.installation_supervision IS '安装督导';
COMMENT ON COLUMN bill_of_quantity.commissioning IS '调试';
COMMENT ON COLUMN bill_of_quantity.patrol_inspection IS '巡检';
COMMENT ON COLUMN bill_of_quantity.create_by IS '创建人';
COMMENT ON COLUMN bill_of_quantity.create_time IS '创建时间';
COMMENT ON COLUMN bill_of_quantity.update_by IS '更新人';
COMMENT ON COLUMN bill_of_quantity.update_time IS '更新时间';


-- public.project_bill_material_rel definition

DROP TABLE IF EXISTS project_bill_material_rel;
CREATE TABLE public.project_bill_material_rel (
	project_id varchar NOT NULL, -- 项目ID
	bill_id varchar NOT NULL, -- 工程量清单ID
	material_id varchar NOT NULL, -- 物料ID
	create_time bpchar(19) NOT NULL,
	create_by varchar NOT NULL,
	update_time bpchar(19) NOT NULL,
	update_by varchar NOT NULL,
	amount int  NULL,
	CONSTRAINT project_bill_material_rel_un UNIQUE (bill_id, material_id)
);

-- Column comments

COMMENT ON COLUMN public.project_bill_material_rel.project_id IS '项目ID';
COMMENT ON COLUMN public.project_bill_material_rel.bill_id IS '工程量清单ID';
COMMENT ON COLUMN public.project_bill_material_rel.material_id IS '物料ID';
COMMENT ON COLUMN public.project_bill_material_rel.amount IS '数量';

DROP TABLE IF EXISTS opportunity_support_team;
CREATE TABLE opportunity_support_team (
    id                TEXT PRIMARY KEY,          -- 主键ID
    opportunity_id    TEXT NOT NULL,             -- 商机ID
    product_sub_id    TEXT NOT NULL,             -- 产品小类ID
    support_staff_id  TEXT NOT NULL,              -- 商机支持人员ID
    create_by              TEXT,                       -- 创建人
    create_time            TEXT,                       -- 创建时间
    update_by              TEXT,                       -- 更新人
    update_time            TEXT                        -- 更新时间
);

COMMENT ON COLUMN public.opportunity_support_team.id IS '主键ID';
COMMENT ON COLUMN public.opportunity_support_team.opportunity_id IS '商机ID';
COMMENT ON COLUMN public.opportunity_support_team.product_sub_id IS '产品小类ID';
COMMENT ON COLUMN public.opportunity_support_team.support_staff_id IS '商机支持人员ID';
COMMENT ON COLUMN opportunity_support_team.create_by IS '创建人';
COMMENT ON COLUMN opportunity_support_team.create_time IS '创建时间';
COMMENT ON COLUMN opportunity_support_team.update_by IS '更新人';
COMMENT ON COLUMN opportunity_support_team.update_time IS '更新时间';

-- 售前售后交接表
DROP TABLE IF EXISTS project_handover;
CREATE TABLE project_handover (
    id                     TEXT PRIMARY KEY,           -- 所属项目id
    deliver_td             TEXT NOT NULL,              -- 交付TD
    item_id                TEXT NOT NULL,              -- 项目id
    first_handover_time    TEXT,                       -- 首次交接时间
    second_handover_time   TEXT,                       -- 二次交接时间
    configure_lock_time    TEXT,                       -- 配置锁定时间
    handover_cycle         INTEGER,                    -- 交接周期
    handover_progress      INTEGER,                    -- 交接进度
    handover_status        TEXT,                       -- 交接状态
    delay_reason           TEXT,                       -- 延误原因
    create_by              TEXT,                       -- 创建人
    create_time            TEXT,                       -- 创建时间
    update_by              TEXT,                       -- 更新人
    update_time            TEXT                        -- 更新时间
);

COMMENT ON COLUMN project_handover.id IS '所属项目id';
COMMENT ON COLUMN project_handover.deliver_td IS '交付TD';
COMMENT ON COLUMN project_handover.item_id IS '项目id';
COMMENT ON COLUMN project_handover.first_handover_time IS '首次交接时间';
COMMENT ON COLUMN project_handover.second_handover_time IS '二次交接时间';
COMMENT ON COLUMN project_handover.configure_lock_time IS '配置锁定时间';
COMMENT ON COLUMN project_handover.handover_cycle IS '交接周期';
COMMENT ON COLUMN project_handover.handover_progress IS '交接进度';
COMMENT ON COLUMN project_handover.handover_status IS '交接状态';
COMMENT ON COLUMN project_handover.delay_reason IS '延误原因';
COMMENT ON COLUMN project_handover.create_by IS '创建人';
COMMENT ON COLUMN project_handover.create_time IS '创建时间';
COMMENT ON COLUMN project_handover.update_by IS '更新人';
COMMENT ON COLUMN project_handover.update_time IS '更新时间';

-- 售前售后交接历史表
DROP TABLE IF EXISTS project_handover_history;
CREATE TABLE project_handover_history (
    id                     TEXT PRIMARY KEY,           -- 主键id
    handover_id            TEXT NOT NULL,              -- 所属交接id
    deliver_td             TEXT NOT NULL,              -- 交付TD
    item_id                TEXT NOT NULL,              -- 所属项目id
    first_handover_time    TEXT,                       -- 首次交接时间
    second_handover_time   TEXT,                       -- 二次交接时间
    configure_lock_time    TEXT,                       -- 配置锁定时间
    handover_cycle         INTEGER,                    -- 交接周期
    handover_progress      INTEGER,                    -- 交接进度
    handover_status        TEXT,                       -- 交接状态
    delay_reason           TEXT,                       -- 延误原因
    create_by              TEXT,                       -- 创建人
    create_time            TEXT,                       -- 创建时间
    update_by              TEXT,                       -- 更新人
    update_time            TEXT                        -- 更新时间
);

COMMENT ON COLUMN project_handover_history.id IS '主键id';
COMMENT ON COLUMN project_handover_history.handover_id IS '所属交接id';
COMMENT ON COLUMN project_handover_history.deliver_td IS '交付TD';
COMMENT ON COLUMN project_handover_history.item_id IS '所属项目id';
COMMENT ON COLUMN project_handover_history.first_handover_time IS '首次交接时间';
COMMENT ON COLUMN project_handover_history.second_handover_time IS '二次交接时间';
COMMENT ON COLUMN project_handover_history.configure_lock_time IS '配置锁定时间';
COMMENT ON COLUMN project_handover_history.handover_cycle IS '交接周期';
COMMENT ON COLUMN project_handover_history.handover_progress IS '交接进度';
COMMENT ON COLUMN project_handover_history.handover_status IS '交接状态';
COMMENT ON COLUMN project_handover_history.delay_reason IS '延误原因';
COMMENT ON COLUMN project_handover_history.create_by IS '创建人';
COMMENT ON COLUMN project_handover_history.create_time IS '创建时间';
COMMENT ON COLUMN project_handover_history.update_by IS '更新人';
COMMENT ON COLUMN project_handover_history.update_time IS '更新时间';

-- 投标结论表
DROP TABLE IF EXISTS bidding_conclusion;
CREATE TABLE bidding_conclusion (
    id                      TEXT PRIMARY KEY,                    -- 主键ID
    project_id              TEXT NOT NULL,                       -- 商机ID
    bidding_project_type    INTEGER NOT NULL,                    -- 项目类型 (1:PC,2:EPC)
    project_amount_type     INTEGER NOT NULL,                    -- 项目金额类型
    customer_type           INTEGER NOT NULL,                    -- 客户类别
    marketing_type          INTEGER NOT NULL,                    -- 营销类型
    project_no              TEXT,                                -- 项目编号（允许NULL）
    bid_publicity_date      TEXT NOT NULL,                       -- 中标公示时间
    bid_notice_date         TEXT NOT NULL,                       -- 中标通知时间
    fill_times              INTEGER NOT NULL DEFAULT 0,          -- 投标结论填写(更新)次数，默认0
    create_time             TEXT,                                -- 创建时间
    update_time             TEXT,                                -- 更新时间
    create_by               TEXT,                                -- 创建用户
    update_by               TEXT                                 -- 更新用户
);

-- 添加字段注释
COMMENT ON COLUMN bidding_conclusion.id IS '主键ID';
COMMENT ON COLUMN bidding_conclusion.project_id IS '商机ID';
COMMENT ON COLUMN bidding_conclusion.bidding_project_type IS '项目类型 (1:PC, 2:EPC)';
COMMENT ON COLUMN bidding_conclusion.project_amount_type IS '项目金额类型 (1:1000W以下, 2:1000W~3000W, 3:3000W~5000W, 4:5000W~1亿, 5:1亿~2亿, 6:2亿以上)';
COMMENT ON COLUMN bidding_conclusion.customer_type IS '客户类别 (1:运营商, 2:政企, 3:新业务)';
COMMENT ON COLUMN bidding_conclusion.marketing_type IS '营销类型 (1:一营, 2:二营, 3:三营, 5:五营, 6:政企, 7:港澳)';
COMMENT ON COLUMN bidding_conclusion.project_no IS '项目编号';
COMMENT ON COLUMN bidding_conclusion.bid_publicity_date IS '中标公示时间';
COMMENT ON COLUMN bidding_conclusion.bid_notice_date IS '中标通知时间';
COMMENT ON COLUMN bidding_conclusion.fill_times IS '投标结论填写(更新)次数';
COMMENT ON COLUMN bidding_conclusion.create_time IS '创建时间';
COMMENT ON COLUMN bidding_conclusion.update_time IS '更新时间';
COMMENT ON COLUMN bidding_conclusion.create_by IS '创建用户';
COMMENT ON COLUMN bidding_conclusion.update_by IS '更新用户';

-- 项目信息表

DROP TABLE IF EXISTS item_info;
CREATE TABLE item_info (
    id              TEXT PRIMARY KEY,                    -- 主键ID
    project_id      TEXT NOT NULL,                       -- 项目ID
    create_time     TEXT NOT NULL,                       -- 创建时间
    update_time     TEXT NOT NULL,                       -- 更新时间
    create_by       TEXT NOT NULL,                       -- 创建用户ID
    create_name     TEXT NOT NULL,                       -- 创建用户名
    update_by       TEXT NOT NULL                        -- 更新用户ID
);

-- 添加字段注释
COMMENT ON COLUMN item_info.id IS '主键ID';
COMMENT ON COLUMN item_info.project_id IS '项目ID';
COMMENT ON COLUMN item_info.create_time IS '创建时间';
COMMENT ON COLUMN item_info.update_time IS '更新时间';
COMMENT ON COLUMN item_info.create_by IS '创建用户ID';
COMMENT ON COLUMN item_info.create_name IS '创建用户名';
COMMENT ON COLUMN item_info.update_by IS '更新用户ID';

DROP TABLE IF EXISTS project_delivery;
CREATE TABLE project_delivery (
    id TEXT NOT NULL PRIMARY KEY,
    project_pd TEXT NOT NULL,
    current_project_stage TEXT NOT NULL,
    overall_state TEXT NOT NULL,
    delivery_progress INTEGER NOT NULL,
    pac_time TEXT NULL,
    fac_time TEXT NULL,
    project_delivery_milepost_program TEXT NULL,
    weekly_project_progress TEXT NULL,
    create_time TEXT NOT NULL,
    update_time TEXT NOT NULL,
    create_by TEXT NOT NULL,
    update_by TEXT NOT NULL
);

COMMENT ON COLUMN project_delivery.id IS '唯一标识符 (id)';
COMMENT ON COLUMN project_delivery.project_pd IS '项目PD';
COMMENT ON COLUMN project_delivery.current_project_stage IS '当前项目阶段';
COMMENT ON COLUMN project_delivery.overall_state IS '整体状态';
COMMENT ON COLUMN project_delivery.delivery_progress IS '交付进度0-100';
COMMENT ON COLUMN project_delivery.pac_time IS 'PAC时间';
COMMENT ON COLUMN project_delivery.fac_time IS 'FAC时间';
COMMENT ON COLUMN project_delivery.project_delivery_milepost_program IS '工程交付里程牌计划';
COMMENT ON COLUMN project_delivery.weekly_project_progress IS '项目周进展';

COMMENT ON COLUMN project_delivery.create_time IS '创建时间';
COMMENT ON COLUMN project_delivery.update_time IS '更新时间';
COMMENT ON COLUMN project_delivery.create_by IS '创建者';
COMMENT ON COLUMN project_delivery.update_by IS '更新者';

DROP TABLE IF EXISTS project_delivery_history;
CREATE TABLE project_delivery_history (
    id TEXT NOT NULL PRIMARY KEY,
    project_id TEXT NOT NULL,
    overall_state TEXT NOT NULL,
    delivery_progress INTEGER NOT NULL,
    weekly_project_progress TEXT NULL,
    create_time TEXT NOT NULL,
    update_time TEXT NOT NULL,
    create_by TEXT NOT NULL,
    update_by TEXT NOT NULL
);

COMMENT ON COLUMN project_delivery_history.id IS '唯一标识符 (id)';
COMMENT ON COLUMN project_delivery_history.project_id IS '项目id';
COMMENT ON COLUMN project_delivery_history.overall_state IS '整体状态';
COMMENT ON COLUMN project_delivery_history.delivery_progress IS '交付进度0-100';
COMMENT ON COLUMN project_delivery_history.weekly_project_progress IS '项目周进展';

COMMENT ON COLUMN project_delivery_history.create_time IS '创建时间';
COMMENT ON COLUMN project_delivery_history.update_time IS '更新时间';
COMMENT ON COLUMN project_delivery_history.create_by IS '创建者';
COMMENT ON COLUMN project_delivery_history.update_by IS '更新者';

DROP TABLE IF EXISTS project_legacy_issues;
CREATE TABLE project_legacy_issues (
    id TEXT NOT NULL PRIMARY KEY,
    project_id TEXT NOT NULL,
    legacy_issues TEXT NOT NULL,
    responsible_person TEXT NOT NULL,
    state TEXT NOT NULL,
    close_time TEXT NULL,
    create_time TEXT NOT NULL,
    update_time TEXT NOT NULL,
    create_by TEXT NOT NULL,
    update_by TEXT NOT NULL
);

COMMENT ON COLUMN project_legacy_issues.id IS '唯一标识符 (id)';
COMMENT ON COLUMN project_legacy_issues.project_id IS '所属项目id';
COMMENT ON COLUMN project_legacy_issues.legacy_issues IS '遗留问题';
COMMENT ON COLUMN project_legacy_issues.responsible_person IS '责任人';
COMMENT ON COLUMN project_legacy_issues.state IS '状态';
COMMENT ON COLUMN project_legacy_issues.close_time IS '关闭时间';

COMMENT ON COLUMN project_legacy_issues.create_time IS '创建时间';
COMMENT ON COLUMN project_legacy_issues.update_time IS '更新时间';
COMMENT ON COLUMN project_legacy_issues.create_by IS '创建者';
COMMENT ON COLUMN project_legacy_issues.update_by IS '更新者';

-- 20250707
DROP TABLE IF EXISTS bid_ai_analysis;
CREATE TABLE public.bid_ai_analysis (
	id varchar NOT NULL,
	project_id varchar NOT NULL,
	user_id varchar NOT NULL,
	short_brand varchar NULL,
	his varchar NOT NULL,
	msg varchar NULL,
	create_by varchar NULL,
	create_time varchar NULL
);

DROP TABLE IF EXISTS public.bid_ai_analysis_file;
CREATE TABLE public.bid_ai_analysis_file (
	id varchar NOT NULL,
	analysis_id varchar NOT NULL,
	file_id varchar NOT NULL,
	page varchar NULL,
	file_type varchar NULL,
	"content" varchar NULL,
	create_by varchar NULL,
	create_time varchar NULL,
	file_name varchar NULL
);

DROP TABLE IF EXISTS public.bid_ai_analysis_result;
CREATE TABLE public.bid_ai_analysis_result (
	id varchar NOT NULL,
	analysis_id varchar NOT NULL,
	product_category_id varchar NULL,
	brand_id varchar NULL,
	advantage varchar NULL,
	disadvantage varchar NULL,
	guiding_suggestions varchar NULL,
	create_by varchar NULL,
	create_time varchar NULL,
	update_by varchar NULL,
	update_time varchar NULL
);

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS project_deepen_implementation;

-- 创建项目深化实施表
CREATE TABLE project_deepen_implementation (
    id TEXT NOT NULL PRIMARY KEY,
    design_director TEXT NOT NULL,
    start_time TEXT,
    request_information_completion_time TEXT,
    actual_information_completion_time TEXT,
    deepen_design_start_time TEXT,
    deepens_design_end_time TEXT,
    combined_image_start_time TEXT,
    combined_image_end_time TEXT,
    BIM_special_design_start_time TEXT,
    BIM_special_design_end_time TEXT,
    first_batch_material_preparation_output_time TEXT,
    long_period_material_list_output_time TEXT,
    overall_material_preparation_lock_time TEXT,
    engineering_deepen_progress INTEGER NOT NULL,
    deepen_design_status TEXT NOT NULL,
    delay_reason TEXT,
    construction_drawing_revision_start_time TEXT,
    modify_deepen_design_start_time TEXT,
    modify_deepen_design_end_time TEXT,
    construction_drawing_calibration_start_time TEXT,
    construction_drawing_calibration_end_time TEXT,
    create_by TEXT NOT NULL,
    create_time TEXT NOT NULL,
    update_by TEXT NOT NULL,
    update_time TEXT NOT NULL
);

-- 添加列注释
COMMENT ON COLUMN project_deepen_implementation.id IS '主键id所属项目id，一个项目只有一个深化实施';
COMMENT ON COLUMN project_deepen_implementation.design_director IS '设计负责人';
COMMENT ON COLUMN project_deepen_implementation.start_time IS '启动时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.request_information_completion_time IS '要求提资完成时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.actual_information_completion_time IS '实际提资完成时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.deepen_design_start_time IS '深化设计开始时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.deepens_design_end_time IS '深化设计结束时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.combined_image_start_time IS '合图开始时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.combined_image_end_time IS '合图结束时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.BIM_special_design_start_time IS 'BIM专项设计开始时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.BIM_special_design_end_time IS 'BIM专项设计结束时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.first_batch_material_preparation_output_time IS '首批备料清单输出时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.long_period_material_list_output_time IS '长周期物料清单输出时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.overall_material_preparation_lock_time IS '整体备料清单锁定时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.engineering_deepen_progress IS '工程深化进度，必填，0-100';
COMMENT ON COLUMN project_deepen_implementation.deepen_design_status IS '深化设计状态，必填，下拉框选择：A正常；B延误（客户需求变化）；B延误（产品原因）；B延误（商务原因）；B延误（其它）';
COMMENT ON COLUMN project_deepen_implementation.delay_reason IS '延误原因，必填（仅延误时可填写），1000字符';
COMMENT ON COLUMN project_deepen_implementation.construction_drawing_revision_start_time IS '施工图修订开始时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.modify_deepen_design_start_time IS '修改深化设计开始时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.modify_deepen_design_end_time IS '修改深化设计结束时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.construction_drawing_calibration_start_time IS '施工图校正开始时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.construction_drawing_calibration_end_time IS '施工图校正结束时间，非必填：时间选择，精确到天';
COMMENT ON COLUMN project_deepen_implementation.create_by IS '创建人';
COMMENT ON COLUMN project_deepen_implementation.create_time IS '创建时间';
COMMENT ON COLUMN project_deepen_implementation.update_by IS '更新人';
COMMENT ON COLUMN project_deepen_implementation.update_time IS '更新时间';

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS deepen_implementation_history;

-- 创建深化实施历史表
CREATE TABLE deepen_implementation_history (
    id TEXT NOT NULL PRIMARY KEY,
    item_id TEXT NOT NULL,
    engineering_deepen_progress INTEGER,
    deepen_design_status TEXT,
    create_by TEXT NOT NULL,
    create_time TEXT NOT NULL,
    update_by TEXT NOT NULL,
    update_time TEXT NOT NULL
);

-- 添加列注释
COMMENT ON COLUMN deepen_implementation_history.id IS '主键id';
COMMENT ON COLUMN deepen_implementation_history.item_id IS '所属项目id';
COMMENT ON COLUMN deepen_implementation_history.engineering_deepen_progress IS '工程深化进度';
COMMENT ON COLUMN deepen_implementation_history.deepen_design_status IS '深化设计状态';
COMMENT ON COLUMN deepen_implementation_history.create_by IS '创建人';
COMMENT ON COLUMN deepen_implementation_history.create_time IS '创建时间';
COMMENT ON COLUMN deepen_implementation_history.update_by IS '更新人';
COMMENT ON COLUMN deepen_implementation_history.update_time IS '更新时间';

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS construction_drawing_review;

-- 创建施工图会审记录表
CREATE TABLE construction_drawing_review (
    id TEXT NOT NULL PRIMARY KEY,
    item_id TEXT NOT NULL,
    review_time TEXT NOT NULL,
    summary TEXT NOT NULL,
    create_by TEXT NOT NULL,
    create_time TEXT NOT NULL,
    update_by TEXT NOT NULL,
    update_time TEXT NOT NULL
);

-- 添加列注释
COMMENT ON COLUMN construction_drawing_review.id IS '主键id';
COMMENT ON COLUMN construction_drawing_review.item_id IS '所属项目id';
COMMENT ON COLUMN construction_drawing_review.review_time IS '会审时间，默认填写时单的时间，必填：时间选择，精确到天';
COMMENT ON COLUMN construction_drawing_review.summary IS '纪要（必填，3000字符）';
COMMENT ON COLUMN construction_drawing_review.create_by IS '创建人';
COMMENT ON COLUMN construction_drawing_review.create_time IS '创建时间';
COMMENT ON COLUMN construction_drawing_review.update_by IS '更新人';
COMMENT ON COLUMN construction_drawing_review.update_time IS '更新时间';

-- 创建提资任务表
DROP TABLE IF EXISTS submission_task;
CREATE TABLE submission_task (
    id TEXT NOT NULL PRIMARY KEY,
    project_id TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    task_name TEXT NOT NULL,
    task_status TEXT NOT NULL,
    task_sequence INTEGER NOT NULL DEFAULT 1,
    submitter TEXT,
    submit_time TEXT,
    reviewer TEXT,
    review_time TEXT,
    review_result TEXT,
    review_comment TEXT,
    attachment_ids TEXT,
    flow_id TEXT,
    create_by TEXT NOT NULL,
    create_time TEXT NOT NULL,
    update_by TEXT NOT NULL,
    update_time TEXT NOT NULL,
    CONSTRAINT unique_project_category_sequence UNIQUE (project_id, product_category_id, task_sequence)
);

-- 添加列注释
COMMENT ON COLUMN submission_task.id IS '主键id';
COMMENT ON COLUMN submission_task.project_id IS '项目id';
COMMENT ON COLUMN submission_task.product_category_id IS '产品小类id';
COMMENT ON COLUMN submission_task.task_name IS '任务名称';
COMMENT ON COLUMN submission_task.task_status IS '任务状态：PENDING_SUBMIT-待提交，IN_PROGRESS-进行中，SUBMITTED-已提交，REVIEW_PASS-验收通过，REVIEW_REJECT-验收不通过，CANCELLED-已取消';
COMMENT ON COLUMN submission_task.task_sequence IS '任务序号，同一产品小类的任务序号，用于确定最新任务';
COMMENT ON COLUMN submission_task.submitter IS '提交人（用户名+工号格式）';
COMMENT ON COLUMN submission_task.submit_time IS '提交时间';
COMMENT ON COLUMN submission_task.reviewer IS '审核人';
COMMENT ON COLUMN submission_task.review_time IS '审核时间';
COMMENT ON COLUMN submission_task.review_result IS '审核结果';
COMMENT ON COLUMN submission_task.review_comment IS '审核意见';
COMMENT ON COLUMN submission_task.attachment_ids IS '关联的文档ID列表（JSON格式）';
COMMENT ON COLUMN submission_task.flow_id IS '流程实例ID';
COMMENT ON COLUMN submission_task.create_by IS '创建人';
COMMENT ON COLUMN submission_task.create_time IS '创建时间';
COMMENT ON COLUMN submission_task.update_by IS '更新人';
COMMENT ON COLUMN submission_task.update_time IS '更新时间';

-- 创建索引
CREATE INDEX idx_submission_task_project_category ON submission_task (project_id, product_category_id);
CREATE INDEX idx_submission_task_status ON submission_task (task_status);
CREATE INDEX idx_submission_task_sequence ON submission_task (project_id, product_category_id, task_sequence DESC);




CREATE TABLE IF NOT EXISTS deepen_design_files_relation (
    flow_id TEXT NOT NULL, -- 流程flowId(关联文件id)
    item_id TEXT NOT NULL, -- 项目id
    product_category_id TEXT NOT NULL, -- 产品小类id
    create_by TEXT NOT NULL, -- 创建人
    create_time TEXT NOT NULL, -- 创建时间
    submit_user TEXT NOT NULL, -- 提交人
    update_time TEXT NOT NULL, -- 更新时间
    approval_result TEXT NOT NULL -- 0:未验证通过,1:验证通过
);

-- 添加注释
COMMENT ON TABLE deepen_design_files_relation IS '深化设计文档附件关联表';
COMMENT ON COLUMN deepen_design_files_relation.flow_id IS '流程flowId(关联文件id)';
COMMENT ON COLUMN deepen_design_files_relation.item_id IS '项目id';
COMMENT ON COLUMN deepen_design_files_relation.product_category_id IS '产品小类id';
COMMENT ON COLUMN deepen_design_files_relation.create_by IS '创建人';
COMMENT ON COLUMN deepen_design_files_relation.create_time IS '创建时间';
COMMENT ON COLUMN deepen_design_files_relation.submit_user IS '提交人';
COMMENT ON COLUMN deepen_design_files_relation.update_time IS '更新时间';
COMMENT ON COLUMN deepen_design_files_relation.approval_result IS '0:未验证通过,1:验证通过';