{"module-project-manager": "项目管理", "ProjectAreaAddDto-areaName": "项目地区名称", "ProjectAreaAddDto-parentId": "项目地区父id", "ProjectAreaAddDto-areaLevel": "项目地区层级", "ProjectAreaAddDtoDescription": "新增项目地区", "ProjectAreaUpdateDto-id": "项目地区id", "ProjectAreaUpdateDto-areaName": "项目地区名称", "ProjectAreaUpdateDtoDescription": "更新项目地区", "ProjectAreaPo-id": "项目地区id", "ProjectAreaPo-areaName": "项目地区名称", "ProjectAreaPo-parentId": "项目地区父id", "ProjectAreaPo-areaLevel": "项目地区层级", "ProjectAreaPoDescription": "删除项目地区", "ProjectAddDto-name": "项目名称", "ProjectAddDto-areaId": "项目地区id", "ProjectAddDto-projectStage": "项目阶段", "ProjectAddDto-customerName": "客户名称", "ProjectAddDtoDescription": "新增项目", "ProjectEditDto-id": "项目id", "ProjectEditDto-name": "项目名称", "ProjectEditDto-areaId": "项目地区id", "ProjectEditDto-projectStage": "项目阶段", "ProjectEditDto-customerName": "客户名称", "ProjectEditDtoDescription": "编辑项目", "ProjectPo-id": "项目id", "ProjectPo-name": "项目名称", "ProjectPo-areaId": "项目地区父id", "ProjectPo-projectStage": "项目阶段", "ProjectDeleteDescription": "删除项目", "BrandGuideAddDataDtoDescription": "新增品牌引导记录", "BrandGuideDataAddDto-projectId": "项目id", "SingleBrandGuideAddDtoDescription": "增加单个品牌引导", "BrandGuideAddDto-projectId": "项目id", "BrandGuideAddDto-productCategoryId": "产品小类ID", "BrandGuideAddDto-guideBrand": "引导品牌", "LaunchBidDto-id": "项目id", "LaunchBidDto-projectStage": "项目阶段", "LaunchBidDto-bidIssuingTime": "发标时间", "LaunchBidDto-clarifySubmissionTime": "澄清提交时间", "LaunchBidDto-configureManifestLockTime": "配置清单锁定时间", "LaunchBidDto-biddingDocumentsFinalizationTime": "招标文件定稿时间", "LaunchBidDto-bidSubmissionTime": "交标时间", "ProjectStartBiddingDescription": "启动投标", "LaunchBidEditDto-id": "项目id", "LaunchBidEditDto-projectStage": "项目阶段", "LaunchBidEditDto-bidIssuingTime": "发标时间", "LaunchBidEditDto-clarifySubmissionTime": "澄清提交时间", "LaunchBidEditDto-configureManifestLockTime": "配置清单锁定时间", "LaunchBidEditDto-biddingDocumentsFinalizationTime": "招标文件定稿时间", "LaunchBidEditDto-bidSubmissionTime": "交标时间", "ProjectUpdateLaunchBiddingInfoDescription": "更新招标信息", "BiddingDocumentClarificationAddDescription": "标书澄清新增", "BiddingDocumentClarificationAddDto-projectId": "项目id", "BiddingDocumentClarificationAddDto-productCategoryId": "产品分类id", "BiddingDocumentClarificationAddDto-fileName": "文件名称", "BiddingDocumentClarificationAddDto-chapterNumber": "章节号", "BiddingDocumentClarificationAddDto-pageNumber": "页码", "BiddingDocumentClarificationAddDto-fileContent": "招标文件原文", "BiddingDocumentClarificationAddDto-clarification": "澄清问题", "BiddingDocumentClarificationEditDto-id": "标书澄清id", "BiddingDocumentClarificationEditDescription": "标书澄清编辑", "BiddingDocumentClarificationDeleteDescription": "标书澄清删除", "BiddingDocumentClarificationPo-id": "标书澄清id", "BiddingDocumentClarificationPo-projectId": "项目id", "BiddingDocumentClarificationPo-productCategoryId": "产品分类id", "BiddingDocumentClarificationPo-fileName": "文件名称", "BiddingDocumentClarificationPo-chapterNumber": "章节号", "BiddingDocumentClarificationPo-pageNumber": "页码", "BiddingDocumentClarificationPo-fileContent": "招标文件原文", "BiddingDocumentClarificationPo-clarification": "澄清问题", "BillOfQuantityAddDescription": "新增工程量清单", "BillOfQuantityAddDto-ascriptionId": "归属项目id", "BillOfQuantityAddDto-name": "工程量清单名称", "BillOfQuantityAddDto-unitOfMeasurement": "工程量清单计量单位", "BillOfQuantityAddDto-quantity": "工程量", "BillOfQuantityAddDto-supply": "供货", "BillOfQuantityAddDto-productSubcategory": "产品小类id", "BillOfQuantityAddDto-oneTimeUnloading": "一次卸货", "BillOfQuantityAddDto-equipmentPositioning": "设备就位", "BillOfQuantityAddDto-installationSupervision": "安装督导", "BillOfQuantityAddDto-commissioning": "调试", "BillOfQuantityAddDto-patrolInspection": "巡检", "BillOfQuantityBatchAddDescription": "批量新增工程量清单", "BillOfQuantityEditDescription": "编辑工程量清单", "BillOfQuantityEditDto-id": "工程量清单id", "BillOfQuantityEditDto-ascriptionId": "归属项目id", "BillOfQuantityEditDto-name": "工程量清单名称", "BillOfQuantityEditDto-unitOfMeasurement": "工程量清单计量单位", "BillOfQuantityEditDto-quantity": "工程量", "BillOfQuantityEditDto-supply": "供货", "BillOfQuantityEditDto-productSubcategory": "产品小类id", "BillOfQuantityEditDto-oneTimeUnloading": "一次卸货", "BillOfQuantityEditDto-equipmentPositioning": "设备就位", "BillOfQuantityEditDto-installationSupervision": "安装督导", "BillOfQuantityEditDto-commissioning": "调试", "BillOfQuantityEditDto-patrolInspection": "巡检", "BillOfQuantityBatchEditDescription": "批量编辑工程量清单", "BillOfQuantityBatchEditDto-ids": "工程量清单集", "BillOfQuantityBatchEditDto-supply": "供货", "BillOfQuantityBatchEditDto-productSubcategory": "产品小类id", "BillOfQuantityBatchEditDto-oneTimeUnloading": "一次卸货", "BillOfQuantityBatchEditDto-equipmentPositioning": "设备就位", "BillOfQuantityBatchEditDto-installationSupervision": "安装督导", "BillOfQuantityBatchEditDto-commissioning": "调试", "BillOfQuantityBatchEditDto-patrolInspection": "巡检", "BillOfQuantityDeleteDescription": "删除工程量清单", "BillOfQuantityPo-id": "工程量清单id", "BillOfQuantityPo-ascriptionId": "归属项目id", "BillOfQuantityPo-name": "工程量清单名称", "BillOfQuantityPo-unitOfMeasurement": "工程量清单计量单位", "BillOfQuantityPo-quantity": "工程量", "BillOfQuantityPo-supply": "供货", "BillOfQuantityPo-productSubcategory": "产品小类id", "BillOfQuantityPo-oneTimeUnloading": "一次卸货", "BillOfQuantityPo-equipmentPositioning": "设备就位", "BillOfQuantityPo-installationSupervision": "安装督导", "BillOfQuantityPo-commissioning": "调试", "BillOfQuantityPo-patrolInspection": "巡检", "BillOfQuantityBatchDeleteDescription": "批量删除工程量清单", "BillOfQuantityBatchDeleteDto-ids": "工程量清单ID集", "MaterialAssociationDescription": "关联物料", "MaterialAssociationDto-billId": "工作量清单ID", "MaterialAssociationDto-selectedMaterials": "已关联物料", "ProjectLegacyIssuesEditDescription": "更新项目遗留问题", "LegacyIssuesEditDto-id": "项目遗留问题id", "ProjectLegacyIssuesAddDescription": "新增项目遗留问题", "LegacyIssuesAddDto-projectId": "归属项目id", "LegacyIssuesAddDto-legacyIssues": "遗留问题", "LegacyIssuesAddDto-responsiblePerson": "负责人", "LegacyIssuesAddDto-state": "遗留问题状态", "ProjectLegacyIssuesDeleteDescription": "删除项目遗留问题", "ProjectLegacyIssuesPo-id": "遗留问id", "ProjectLegacyIssuesPo-projectId": "归属项目id", "ProjectLegacyIssuesPo-legacyIssues": "遗留问题描述", "ProjectLegacyIssuesPo-responsiblePerson": "遗留问题负责人", "ProjectLegacyIssuesPo-state": "遗留问题的当前状态"}