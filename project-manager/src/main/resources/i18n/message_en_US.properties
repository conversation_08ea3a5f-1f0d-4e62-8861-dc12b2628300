project.dashboard.score=Score
file.name=File Name
chapter.number=Chapter Number
page.number=Page Number
file.content=Original text of bidding documents
clarification=Clarification
create.user=Create User
create.time=Create Time
update.user=Update User
update.time=Update Time
picture.attachment=Attachment
BOQ.name.empty=Bill of Quantities name is empty
BOQ.name.length.exceeds.limit=Bill of Quantities name length exceeds limit (200 characters)
BOQ.characteristic.length.exceeds.limit=Characteristic length more than limit (1000 characters)
BOQ.unit.measurement.empty=The unit of measurement is empty
BOQ.unit.measurement.length.exceeds.limit=Measuring unit length more than limit (10)
BOQ.quantity.empty=Quantity is empty
BOQ.quantity.less.than.zero=The quantity cannot be less than 0
BOQ.quantity.accurate.two.decimal.places=Decimal quantities are accurate only to two decimal places
BOQ.quantity.should.be.a.number=Quantities must be numerical
BOQ.subproject.length.exceeds.limit=Subproject item length exceeds the limit (20 characters)
BOQ.subcategory.required=When selecting the production line as the supplier, the product subcategory cannot be left blank
BOQ.subcategory.must.be.empty=When the supply selection is not for the production line, the product subcategory must be empty
BOQ.supply.empty=Supply selection is empty
BOQ.one.time.unloading.empty=One time unloading is empty
BOQ.equipment.positioning.empty=Installation is empty
BOQ.installation.supervision.empty=supervision is empty
BOQ.commissioning.empty=Debug as empty
BOQ.patrol.inspection.empty=Inspection is empty
BOQ.Not.within.the.selection.range=choose one from Party A's supply, engineering, or production line
Product.subcategory.does.not.exist=The product subcategory does not exist
Batch.deletion.bill.of.quantities=Batch deletion of bill of quantities
Batch.editing.bill.of.quantities=Batch editing of bill of quantities
Batch.import.of.bill.of.quantities=Batch import of bill of quantities
Batch.operation.details=Batch operation details:{0}
Module.project.manager=Project manager
Batch.operation.fail.reason=Reason for batch operation failure: {0}
operation.log.details=Operation log details:{0}
operation.fail.log=Operation fail log:{0}
update.project.delivery.information=update project delivery information