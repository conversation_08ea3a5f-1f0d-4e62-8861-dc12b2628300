project.dashboard.score=得分
file.name=文件名称
chapter.number=章节号
page.number=页码
file.content=招标文件原文
clarification=澄清问题
create.user=创建人
create.time=创建时间
update.user=更新人
update.time=更新时间
picture.attachment=附件
BOQ.name.empty=工程量清单名称为空
BOQ.name.length.exceeds.limit=工程量清单名称长度超过限制（200字符）
BOQ.characteristic.length.exceeds.limit=工程量清单特征长度超过限制（1000字符）
BOQ.unit.measurement.empty=工程量清单计量单位为空
BOQ.unit.measurement.length.exceeds.limit=工程量清单计量单位长度超过限制（10字符）
BOQ.quantity.empty=工程量为空
BOQ.quantity.less.than.zero=工程量不能小于0
BOQ.quantity.accurate.two.decimal.places=工程量小数只能精确到小数点后两位
BOQ.quantity.should.be.a.number=工程量必须为数字
BOQ.subproject.length.exceeds.limit=所属工程分项长度超过限制（20字符）
BOQ.subcategory.required=供货选择为产线时产品小类不能为空
BOQ.subcategory.must.be.empty=供货选择不是产线时产品小类必须为空
BOQ.supply.empty=供货选择为空
BOQ.one.time.unloading.empty=一次性卸货为空
BOQ.equipment.positioning.empty=安装为空
BOQ.installation.supervision.empty=监督为空
BOQ.commissioning.empty=调试为空
BOQ.patrol.inspection.empty=巡检为空
BOQ.Not.within.the.selection.range=不在选择范围内,从甲供、工程、产线中选择其一
Product.subcategory.does.not.exist=产品小类不存在
Batch.deletion.bill.of.quantities=批量删除工程量清单
Batch.editing.bill.of.quantities=批量编辑工程量清单
Batch.import.of.bill.of.quantities=批量导入工程量清单
Batch.operation.details=批量操作详情:{0}
Module.project.manager=项目管理
Batch.operation.fail.reason=批量操作失败原因:{0}
operation.log.details=操作日志详情:{0}
operation.fail.log=操作失败日志:{0}
update.project.delivery.information=更新项目交付信息