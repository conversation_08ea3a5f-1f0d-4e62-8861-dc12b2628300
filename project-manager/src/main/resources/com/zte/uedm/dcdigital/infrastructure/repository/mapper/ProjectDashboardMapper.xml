<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProjectDashboardMapper">

    <select id="listProjectScore" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.ProjectStageScoreEntity">
        SELECT
            bg.id AS brand_guide_id,
            bg.project_id,
            bg.stage,
            bg.record_time,
            bgs.score AS score
        FROM brand_guide bg JOIN (
            SELECT bgd.brand_guide_id, AVG(bgd.grade) AS score
            FROM brand_guide_detail bgd
            JOIN (
                SELECT a.id
                FROM brand_guide a
                JOIN (
                    SELECT project_id, stage, MAX(record_time) AS max_record_time
                    FROM brand_guide
                    <where>
                        <if test="timeRange != null and timeRange.startTime != null and timeRange.startTime != '' and timeRange.endTime != null and timeRange.endTime != ''">
                            AND record_time BETWEEN #{timeRange.startTime} AND #{timeRange.endTime}
                        </if>
                        <if test="projectIds != null and projectIds.size > 0">
                            AND project_id IN
                            <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                                #{projectId}
                            </foreach>
                        </if>
                        AND status = 1
                    </where>
                    GROUP BY project_id, stage
                ) t ON a.project_id = t.project_id AND a.stage = t.stage AND a.record_time = t.max_record_time
            ) AS latest_guides ON bgd.brand_guide_id = latest_guides.id
            GROUP BY bgd.brand_guide_id
        ) AS bgs ON bg.id = bgs.brand_guide_id
    </select>

    <select id="selectGuideProductCategories" resultType="com.zte.uedm.dcdigital.common.bean.enums.IdNameBean">
        SELECT
            bgd.product_category_id AS id
        FROM brand_guide_detail bgd JOIN (
            SELECT a.id
            FROM brand_guide a JOIN (
                SELECT project_id, MAX(record_time) AS max_record_time
                FROM brand_guide
                    <where>
                        status = 1
                        AND project_id = #{projectId}
                    </where>
                    GROUP BY project_id
            ) t ON a.project_id = t.project_id AND a.record_time = t.max_record_time
        ) AS latest ON latest.id = bgd.brand_guide_id
    </select>

    <select id="selectLineChartData" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.ProjectStageScoreEntity">
        SELECT bg.stage, a.grade AS score, bg.record_time
        FROM brand_guide_detail AS a
        LEFT JOIN brand_guide bg ON bg.id = a.brand_guide_id
        <where>
            bg.project_id = #{projectId}
            AND bg.status = 1
            AND a.product_category_id = #{productCategoryId}
        </where>
    </select>


</mapper>