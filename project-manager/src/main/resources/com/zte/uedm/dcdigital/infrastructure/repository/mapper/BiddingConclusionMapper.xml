<?xml version="1.0" encoding="UTF-8" ?>
<!-- Started by AICoder, pid:2feab451fcibdd8143a908e740931783fc49070a -->
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.BiddingConclusionMapper">

    <resultMap id="BaseResultMap" type="com.zte.uedm.dcdigital.infrastructure.repository.po.BiddingConclusionPo">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="bidding_project_type" property="biddingProjectType"/>
        <result column="project_amount_type" property="projectAmountType"/>
        <result column="customer_type" property="customerType"/>
        <result column="marketing_type" property="marketingType"/>
        <result column="project_no" property="projectNo"/>
        <result column="bid_publicity_date" property="bidPublicityDate"/>
        <result column="bid_notice_date" property="bidNoticeDate"/>
        <result column="fill_times" property="fillTimes"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <!-- 插入记录 -->
    <insert id="insertBiddingConclusion" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.BiddingConclusionPo">
        INSERT INTO bidding_conclusion (
        id, project_id, bidding_project_type, project_amount_type, customer_type,
        marketing_type, project_no, bid_publicity_date, bid_notice_date, fill_times,
        create_time, update_time, create_by, update_by
        ) VALUES (
        #{id}, #{projectId}, #{biddingProjectType}, #{projectAmountType}, #{customerType},
        #{marketingType}, #{projectNo}, #{bidPublicityDate}, #{bidNoticeDate}, #{fillTimes},
        #{createTime}, #{updateTime}, #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 根据ID更新记录 -->
    <update id="updateBiddingConclusionById" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.BiddingConclusionPo">
        UPDATE bidding_conclusion
        <set>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="biddingProjectType != null">bidding_project_type = #{biddingProjectType},</if>
            <if test="projectAmountType != null">project_amount_type = #{projectAmountType},</if>
            <if test="customerType != null">customer_type = #{customerType},</if>
            <if test="marketingType != null">marketing_type = #{marketingType},</if>
            <if test="projectNo != null">project_no = #{projectNo},</if>
            <if test="bidPublicityDate != null">bid_publicity_date = #{bidPublicityDate},</if>
            <if test="bidNoticeDate != null">bid_notice_date = #{bidNoticeDate},</if>
            <if test="fillTimes != null">fill_times = #{fillTimes},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除记录 -->
    <delete id="deleteBiddingConclusionById">
        DELETE FROM bidding_conclusion WHERE id = #{id}
    </delete>

    <!-- 根据ID查询记录 -->
    <select id="selectBiddingConclusionById" resultMap="BaseResultMap">
        SELECT * FROM bidding_conclusion WHERE id = #{id}
    </select>

    <!-- 动态条件查询列表 -->
    <select id="selectBiddingConclusionList" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.BiddingConclusionPo" resultMap="BaseResultMap">
        SELECT * FROM bidding_conclusion
        <where>
            <if test="id != null">AND id = #{id}</if>
            <if test="projectId != null">AND project_id = #{projectId}</if>
            <if test="biddingProjectType != null">AND bidding_project_type = #{biddingProjectType}</if>
            <if test="projectAmountType != null">AND project_amount_type = #{projectAmountType}</if>
            <if test="customerType != null">AND customer_type = #{customerType}</if>
            <if test="marketingType != null">AND marketing_type = #{marketingType}</if>
            <if test="projectNo != null">AND project_no = #{projectNo}</if>
            <if test="bidPublicityDate != null">AND bid_publicity_date = #{bidPublicityDate}</if>
            <if test="bidNoticeDate != null">AND bid_notice_date = #{bidNoticeDate}</if>
            <if test="fillTimes != null">AND fill_times = #{fillTimes}</if>
            <if test="createTime != null">AND create_time = #{createTime}</if>
            <if test="updateTime != null">AND update_time = #{updateTime}</if>
            <if test="createBy != null">AND create_by = #{createBy}</if>
            <if test="updateBy != null">AND update_by = #{updateBy}</if>
        </where>
    </select>
    <select id="selectBiddingConclusionByProjectId"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.BiddingConclusionPo">
        SELECT * FROM bidding_conclusion WHERE project_id = #{projectId}
    </select>

</mapper>

        <!-- Ended by AICoder, pid:2feab451fcibdd8143a908e740931783fc49070a -->