<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProjectBillMaterialRelMapper">

    <select id="countAssociatedMaterialNum" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.BillAssociationCountEntity">
        SELECT
            bill_id,
            COUNT(material_id) AS associated_num
        FROM project_bill_material_rel
        <where>
            <if test="billIds != null and billIds.size > 0">
                bill_id IN
                <foreach collection="billIds" item="billId" separator="," open="(" close=")">
                    #{billId}
                </foreach>
            </if>
        </where>
        GROUP BY bill_id;
    </select>

</mapper>