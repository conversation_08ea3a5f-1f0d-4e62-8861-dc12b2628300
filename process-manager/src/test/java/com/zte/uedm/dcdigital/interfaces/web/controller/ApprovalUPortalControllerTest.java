package com.zte.uedm.dcdigital.interfaces.web.controller;
/* Started by AICoder, pid:w5c3607b41n404c1462a09b6b2cb9c66484864b6 */
import com.zte.uedm.dcdigital.application.approval.executor.ApprovalCommandService;
import com.zte.uedm.dcdigital.application.approval.executor.ApprovalQueryService;
import com.zte.uedm.dcdigital.application.approval.executor.MktApprovalCommandService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalAddDto;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalCancelDto;

import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalWithResultObj;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.MktApprovalDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.TaskDetailVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ApprovalUPortalControllerTest {

    @Mock
    private ApprovalCommandService approvalCommandService;

    @Mock
    private MktApprovalCommandService mktApprovalCommandService;

    @Mock
    private ApprovalQueryService approvalQueryService;

    @InjectMocks
    private ApprovalUPortalController controller;

    @Before
    public void setUp() {
        // Setup code if needed
    }

    @Test
    public void testQueryPendingList() {
        PendingApprovalQueryDto queryDto = new PendingApprovalQueryDto();
        PageVO<ApprovalObj> mockPageVO = new PageVO<>();
        when(approvalQueryService.queryPendingList(queryDto)).thenReturn(mockPageVO);

        controller.queryPendingList(queryDto);


        verify(approvalQueryService, times(1)).queryPendingList(queryDto);
    }

    @Test
    public void testQueryInitiatedList() {
        InitiatedApprovalQueryDto queryDto = new InitiatedApprovalQueryDto();
        PageVO<ApprovalObj> mockPageVO = new PageVO<>();
        when(approvalQueryService.queryInitiatedList(queryDto)).thenReturn(mockPageVO);

        controller.queryInitiatedList(queryDto);


        verify(approvalQueryService, times(1)).queryInitiatedList(queryDto);
    }

    @Test
    public void testQueryCompletedList() {
        CompletedApprovalQueryDto queryDto = new CompletedApprovalQueryDto();
        PageVO<ApprovalWithResultObj> mockPageVO = new PageVO<>();
        when(approvalQueryService.queryCompletedList(queryDto)).thenReturn(mockPageVO);

        controller.queryCompletedList(queryDto);


        verify(approvalQueryService, times(1)).queryCompletedList(queryDto);
    }

    @Test
    public void testGetApprovalById() {
        String approvalId = "123";
        ApprovalVo mockApprovalVo = new ApprovalVo();
        when(approvalQueryService.getApprovalById(approvalId)).thenReturn(mockApprovalVo);

        controller.getApprovalById(approvalId);


        verify(approvalQueryService, times(1)).getApprovalById(approvalId);
    }

    @Test
    public void testGetMktApprovalById() {
        MktHistoryQueryDto queryDto = new MktHistoryQueryDto();
        ApprovalVo mockApprovalVo = new ApprovalVo();
        when(approvalQueryService.getMktFlowDataHistory(queryDto)).thenReturn(mockApprovalVo);

        controller.getMktApprovalById(queryDto);


        verify(approvalQueryService, times(1)).getMktFlowDataHistory(queryDto);
    }

    @Test
    public void testQueryMaterialsByApprovalId() {
        ApprovalMaterialQueryDto queryDto = new ApprovalMaterialQueryDto();

        controller.queryMaterialsByApprovalId(queryDto);
        verify(approvalQueryService, times(1)).queryMaterialsByApprovalId(queryDto);
    }

    @Test
    public void testSubmitApproval() {
        ApprovalSubmitDto submitDto = new ApprovalSubmitDto();

        doNothing().when(approvalCommandService).submitApproval(submitDto);

        controller.submitApproval(submitDto);


        verify(approvalCommandService, times(1)).submitApproval(submitDto);
    }

    @Test
    public void testWithdrawApproval() {
        String flowId = "123";

        doNothing().when(approvalCommandService).withdrawApproval(flowId);

        controller.withdrawApproval(flowId);


        verify(approvalCommandService, times(1)).withdrawApproval(flowId);
    }

    @Test
    public void testAcceptMktApproval() {
        MktApprovalSubmitDto mktApprovalSubmitDto = new MktApprovalSubmitDto();

        doNothing().when(mktApprovalCommandService).acceptMktApproval(mktApprovalSubmitDto);

        controller.acceptMktApproval(mktApprovalSubmitDto);


        verify(mktApprovalCommandService, times(1)).acceptMktApproval(mktApprovalSubmitDto);
    }

    @Test
    public void testGetMktApprovalDetail() {
        String flowId = "123";
        MktApprovalDetailVo mockDetailVo = new MktApprovalDetailVo();
        when(approvalQueryService.getMktApprovalDetail(flowId)).thenReturn(mockDetailVo);

        controller.getMktApprovalDetail(flowId);


        verify(approvalQueryService, times(1)).getMktApprovalDetail(flowId);
    }

    @Test
    public void testCreateMktApproval() {
        MktApprovalAddDto approvalDto = new MktApprovalAddDto();
        String id = "123";
        when(mktApprovalCommandService.createMarketBidApproval(approvalDto)).thenReturn(id);

        controller.createMktApproval(approvalDto);

        verify(mktApprovalCommandService, times(1)).createMarketBidApproval(approvalDto);
    }

    @Test
    public void testCancelMktApproval() {
        MktApprovalCancelDto cancelDto = new MktApprovalCancelDto();

        doNothing().when(mktApprovalCommandService).cancelMarketBidApproval(cancelDto);

        controller.cancelMktApproval(cancelDto);

        verify(mktApprovalCommandService, times(1)).cancelMarketBidApproval(cancelDto);
    }

    @Test
    public void testQueryTaskDetails() {
        TaskDetailQueryDto queryDto = new TaskDetailQueryDto();
        queryDto.setProjectId("1111");
        PageVO<TaskDetailVo> mockPageVO = new PageVO<>();
        when(approvalQueryService.queryTaskDetails(queryDto)).thenReturn(mockPageVO);

        controller.queryTaskDetails(queryDto);


        verify(approvalQueryService, times(1)).queryTaskDetails(queryDto);
    }

    @Test
    public void testQueryTaskDetails_Invalid() {
        TaskDetailQueryDto queryDto = new TaskDetailQueryDto();

        controller.queryTaskDetails(queryDto);

        verify(approvalQueryService, never()).queryTaskDetails(queryDto);
    }

    @Test
    public void testQueryTaskOverview() {
        String projectId = "123";
        TaskDetailVo mockTaskDetailVo = new TaskDetailVo();
        when(approvalQueryService.queryTaskOverview(projectId)).thenReturn(mockTaskDetailVo);

        controller.queryTaskOverview(projectId);

        verify(approvalQueryService, times(1)).queryTaskOverview(projectId);
    }

    @Test
    public void testQueryTaskOverview_EmptyProjectId() {
        String projectId = "";

        controller.queryTaskOverview(projectId);
        verify(approvalQueryService, never()).queryTaskOverview(projectId);
    }

    @Test
    public void testQueryTaskList() {
        TaskQueryDto queryDto = new TaskQueryDto();
        queryDto.setResourceId("111");
        queryDto.setProjectId("111");
        queryDto.setQueryType(0);

        List<ApprovalVo> mockList = Collections.singletonList(new ApprovalVo());
        when(approvalQueryService.queryTaskList(queryDto)).thenReturn(mockList);

        controller.queryTaskList(queryDto);

        verify(approvalQueryService, times(1)).queryTaskList(queryDto);
    }

    @Test
    public void testQueryTaskList_Invalid() {
        TaskQueryDto queryDto = new TaskQueryDto();
        controller.queryTaskList(queryDto);
        verify(approvalQueryService, never()).queryTaskList(queryDto);

    }
}
/* Ended by AICoder, pid:w5c3607b41n404c1462a09b6b2cb9c66484864b6 */