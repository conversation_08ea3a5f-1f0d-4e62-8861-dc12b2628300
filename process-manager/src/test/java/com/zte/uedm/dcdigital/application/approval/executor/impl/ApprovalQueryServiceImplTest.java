package com.zte.uedm.dcdigital.application.approval.executor.impl;
/* Started by AICoder, pid:ye28dt2d87sb5c7148750847b1a63f70cdc42655 */
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalInfoQueryDto;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalInfoVo;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalWithResultObj;
import com.zte.uedm.dcdigital.domain.service.ApprovalService;
import com.zte.uedm.dcdigital.domain.service.MktApprovalService;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.MktApprovalDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.TaskDetailVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ApprovalQueryServiceImplTest {

    @Mock
    private ApprovalService approvalService;

    @Mock
    private MktApprovalService mktApprovalService;

    @InjectMocks
    private ApprovalQueryServiceImpl approvalQueryService;

    @Before
    public void setUp() {
        // Setup code if needed
    }

    @Test
    public void testQueryPendingList() {
        PendingApprovalQueryDto queryDto = new PendingApprovalQueryDto();
        PageVO<ApprovalObj> mockPageVO = new PageVO<>();
        when(approvalService.queryPendingList(queryDto)).thenReturn(mockPageVO);

        PageVO<ApprovalObj> result = approvalQueryService.queryPendingList(queryDto);

        assertEquals(mockPageVO, result);
        verify(approvalService, times(1)).queryPendingList(queryDto);
    }

    @Test
    public void testQueryInitiatedList() {
        InitiatedApprovalQueryDto queryDto = new InitiatedApprovalQueryDto();
        PageVO<ApprovalObj> mockPageVO = new PageVO<>();
        when(approvalService.queryInitiatedList(queryDto)).thenReturn(mockPageVO);

        PageVO<ApprovalObj> result = approvalQueryService.queryInitiatedList(queryDto);

        assertEquals(mockPageVO, result);
        verify(approvalService, times(1)).queryInitiatedList(queryDto);
    }

    @Test
    public void testQueryCompletedList() {
        CompletedApprovalQueryDto queryDto = new CompletedApprovalQueryDto();
        PageVO<ApprovalWithResultObj> mockPageVO = new PageVO<>();
        when(approvalService.queryCompletedList(queryDto)).thenReturn(mockPageVO);

        PageVO<ApprovalWithResultObj> result = approvalQueryService.queryCompletedList(queryDto);

        assertEquals(mockPageVO, result);
        verify(approvalService, times(1)).queryCompletedList(queryDto);
    }

    @Test
    public void testGetApprovalById() {
        String approvalId = "123";
        ApprovalVo mockApprovalVo = new ApprovalVo();
        when(approvalService.getApprovalById(approvalId)).thenReturn(mockApprovalVo);

        ApprovalVo result = approvalQueryService.getApprovalById(approvalId);

        assertEquals(mockApprovalVo, result);
        verify(approvalService, times(1)).getApprovalById(approvalId);
    }

    @Test
    public void testQueryMaterialsByApprovalId() {
        ApprovalMaterialQueryDto queryDto = new ApprovalMaterialQueryDto();
        approvalQueryService.queryMaterialsByApprovalId(queryDto);
        verify(approvalService, times(1)).queryMaterialsByApprovalId(queryDto);
    }

    @Test
    public void testGetMktApprovalDetail() {
        String flowId = "123";
        MktApprovalDetailVo mockDetailVo = new MktApprovalDetailVo();
        when(approvalService.getMktApprovalDetail(flowId)).thenReturn(mockDetailVo);

        MktApprovalDetailVo result = approvalQueryService.getMktApprovalDetail(flowId);

        assertEquals(mockDetailVo, result);
        verify(approvalService, times(1)).getMktApprovalDetail(flowId);
    }

    @Test
    public void testSelectMarketBidInfo() {
        MktApprovalInfoQueryDto mtkDto = new MktApprovalInfoQueryDto();
        List<MktApprovalInfoVo> mockList = Collections.singletonList(new MktApprovalInfoVo());
        when(mktApprovalService.selectMarketBidInfo(mtkDto)).thenReturn(mockList);

        List<MktApprovalInfoVo> result = approvalQueryService.selectMarketBidInfo(mtkDto);

        assertEquals(mockList, result);
        verify(mktApprovalService, times(1)).selectMarketBidInfo(mtkDto);
    }

    @Test
    public void testGetMktFlowDataHistory() {
        MktHistoryQueryDto queryDto = new MktHistoryQueryDto();
        ApprovalVo mockApprovalVo = new ApprovalVo();
        when(mktApprovalService.getMktFlowDataHistory(queryDto)).thenReturn(mockApprovalVo);

        ApprovalVo result = approvalQueryService.getMktFlowDataHistory(queryDto);

        assertEquals(mockApprovalVo, result);
        verify(mktApprovalService, times(1)).getMktFlowDataHistory(queryDto);
    }

    @Test
    public void testQueryTaskDetails() {
        TaskDetailQueryDto queryDto = new TaskDetailQueryDto();
        PageVO<TaskDetailVo> mockPageVO = new PageVO<>();
        when(mktApprovalService.queryTaskDetails(queryDto)).thenReturn(mockPageVO);

        PageVO<TaskDetailVo> result = approvalQueryService.queryTaskDetails(queryDto);

        assertEquals(mockPageVO, result);
        verify(mktApprovalService, times(1)).queryTaskDetails(queryDto);
    }

    @Test
    public void testQueryTaskOverview() {
        String projectId = "123";
        TaskDetailVo mockTaskDetailVo = new TaskDetailVo();
        when(mktApprovalService.queryTaskOverview(projectId)).thenReturn(mockTaskDetailVo);

        TaskDetailVo result = approvalQueryService.queryTaskOverview(projectId);

        assertEquals(mockTaskDetailVo, result);
        verify(mktApprovalService, times(1)).queryTaskOverview(projectId);
    }

    @Test
    public void testQueryTaskList() {
        TaskQueryDto queryDto = new TaskQueryDto();
        List<ApprovalVo> mockList = Collections.singletonList(new ApprovalVo());
        when(mktApprovalService.queryMktTaskList(queryDto)).thenReturn(mockList);

        List<ApprovalVo> result = approvalQueryService.queryTaskList(queryDto);

        assertEquals(mockList, result);
        verify(mktApprovalService, times(1)).queryMktTaskList(queryDto);
    }
}
/* Ended by AICoder, pid:ye28dt2d87sb5c7148750847b1a63f70cdc42655 */