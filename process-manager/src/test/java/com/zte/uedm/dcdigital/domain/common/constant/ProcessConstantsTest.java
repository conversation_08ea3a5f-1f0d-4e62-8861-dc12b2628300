package com.zte.uedm.dcdigital.domain.common.constant;
/* Started by AICoder, pid:a48fdw3ff9y0a7214af10a32a00f3a175307d279 */
import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.Test;

public class ProcessConstantsTest {

    @Test
    public void testSuffix() {
        // 验证 BPMN 文件后缀名是否正确
        assertEquals(".bpmn", ProcessConstants.SUFFIX);
    }

    @Test
    public void testProcessStatusKey() {
        // 验证自定义属性：流程状态的键是否正确
        assertEquals("processStatus", ProcessConstants.PROCESS_STATUS_KEY);
    }
}
/* Ended by AICoder, pid:a48fdw3ff9y0a7214af10a32a00f3a175307d279 */
