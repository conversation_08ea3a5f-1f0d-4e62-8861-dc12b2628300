/* Started by AICoder, pid:6e111ucb9665778140400b96f095b10433022d02 */
package com.zte.uedm.dcdigital.startup.startup;


import com.zte.uedm.dcdigital.startup.ApplicationStartedListener;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.context.event.ApplicationStartedEvent;

import static org.mockito.Mockito.mock;

public class ApplicationStartedListenerTest {

    @InjectMocks
    private ApplicationStartedListener applicationStartedListener;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testOnApplicationEvent() {
        // Arrange
        ApplicationStartedEvent event = mock(ApplicationStartedEvent.class);
        // Act
        applicationStartedListener.onApplicationEvent(event);
    }
}
/* Ended by AICoder, pid:6e111ucb9665778140400b96f095b10433022d02 */