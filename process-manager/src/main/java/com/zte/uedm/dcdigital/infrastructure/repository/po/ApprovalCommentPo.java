package com.zte.uedm.dcdigital.infrastructure.repository.po;

/* Started by AICoder, pid:756e3ce215f1dd714be90911107cfc5645e6e362 */
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@TableName("approval_comment")
public class ApprovalCommentPo {

    /**
     * 唯一标识
     */
    private String id;

    /**
     * 审批id
     */
    private String approvalId;

    /**
     * 处理人id
     */
    private String userId;

    /**
     * 审批结果  0 不同意 1 同意
     */
    private Integer result;

    /**
     * 审批意见
     */
    private String comment;

    /**
     * 流程实例id
     */
    private String flowId;

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 市场投标支持主、子任务静态Id
     */
    private String staticTaskId;
}
/* Ended by AICoder, pid:756e3ce215f1dd714be90911107cfc5645e6e362 */