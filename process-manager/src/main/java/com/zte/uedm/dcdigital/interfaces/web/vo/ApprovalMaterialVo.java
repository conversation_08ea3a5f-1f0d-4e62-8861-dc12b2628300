package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ApprovalMaterialVo {
    /**
     * 物料ID，唯一标识每个物料。
     */
    private String id;

    /**
     * 物料名称，描述物料的名称。
     */
    private String name;

    /**
     * 生产代码
     */
    private String productCode;

    /**
     * 销售状态
     */
    private String saleStatus;

    /**
     * 产品的销售代码，用于在销售系统中标识产品。
     */
    private String salesCode;

    /**
     * 采购模式，表示物料的采购方式。
     */
    private String purchaseMode;

    /**
     * 失效日期，表示物料的有效期截止日期。
     */
    private String expirationDate;

    /**
     * 保质期，表示物料的保质期限。
     */
    private String warrantyPeriod;

    /**
     * 分组ID，用于将物料分组管理。
     */
    private String groupId;

    /**
     * 分组名称，用于显示物料所属的分组名称。
     */
    private String groupName;
}
