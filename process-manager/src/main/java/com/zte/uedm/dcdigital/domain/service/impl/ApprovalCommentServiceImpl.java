package com.zte.uedm.dcdigital.domain.service.impl;

import com.zte.uedm.dcdigital.domain.repository.ApprovalCommentRepository;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalCommentEntity;
import com.zte.uedm.dcdigital.domain.service.ApprovalCommentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class ApprovalCommentServiceImpl implements ApprovalCommentService {


    /* Started by AICoder, pid:c0eff93729l25ec1408a0b1b500b4c1689064d31 */
    /**
     * 用于注入审批评论仓库的资源。
     */
    @Resource
    private ApprovalCommentRepository approvalCommentRepository;

    /**
     * 添加审批评论信息。
     *
     * @param commentEntity 包含审批评论详细信息的实体对象。
     * @return 如果添加成功，返回 `true`；否则返回 `false`。
     */
    @Override
    public boolean addComment(ApprovalCommentEntity commentEntity) {
        return approvalCommentRepository.addCommentInfo(commentEntity);
    }
    /* Ended by AICoder, pid:c0eff93729l25ec1408a0b1b500b4c1689064d31 */
}
