package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@ToString
public class CompletedApprovalQueryDto extends ApprovalQueryDto {

    /**
     * 审批结果，可选。0-不同意，1-同意。
     */
    private Integer result;

    public boolean isQueryConditionEmpty() {
        return this.getType() == null
                && StringUtils.isEmpty(this.getTitle())
                && this.getResult() == null
                && StringUtils.isEmpty(this.getSubmitUser())
                && StringUtils.isEmpty(this.getStartTime())
                && StringUtils.isEmpty(this.getEndTime());
    }
}
