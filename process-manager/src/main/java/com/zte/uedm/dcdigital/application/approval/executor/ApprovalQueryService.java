/* Started by AICoder, pid:672d4b007fj7c33141c50859a011a777aaf3b079 */
package com.zte.uedm.dcdigital.application.approval.executor;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.process.ApprovalCommonVo;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalInfoQueryDto;
import com.zte.uedm.dcdigital.common.bean.process.MktApprovalInfoVo;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalWithResultObj;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.MktApprovalDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProjectTaskInfoVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.TaskDetailVo;

import java.util.List;

public interface ApprovalQueryService {

    /**
     * 查询待处理的审批列表。
     *
     * @param queryDto 查询条件，不能为空。
     * @return 包含审批对象的分页结果。
     */
    PageVO<ApprovalObj> queryPendingList(PendingApprovalQueryDto queryDto);

    /**
     * 查询已发起的审批列表。
     *
     * @param queryDto 查询条件，不能为空。
     * @return 包含审批对象的分页结果。
     */
    PageVO<ApprovalObj> queryInitiatedList(InitiatedApprovalQueryDto queryDto);

    /**
     * 查询已完成的审批列表。
     *
     * @param queryDto 查询条件，不能为空。
     * @return 包含带有审批结果的审批对象的分页结果。
     */
    PageVO<ApprovalWithResultObj> queryCompletedList(CompletedApprovalQueryDto queryDto);

    /**
     * 根据审批ID获取审批详细信息。
     *
     * @param approvalId 审批ID，不能为空。
     * @return 审批详细信息对象。
     */
    ApprovalVo getApprovalById(String approvalId);

    /**
     * 根据审批ID查询相关材料。
     *
     * @param queryDto 查询条件，不能为空。
     * @return 包含材料信息的分页结果。
     */
    PageVO<?> queryMaterialsByApprovalId(ApprovalMaterialQueryDto queryDto);

    /**
     * 根据任务ID获取市场投标支持的详细信息。
     *
     * @param flowId 流程ID，不能为空。
     * @return 市场投标支持的详细信息对象。
     */
    MktApprovalDetailVo getMktApprovalDetail(String flowId);

    /**
     * 查询市场投标支持信息。
     *
     * @param mtkDto 查询条件，不能为空。
     * @return 包含市场投标支持信息的列表。
     */
    List<MktApprovalInfoVo> selectMarketBidInfo(MktApprovalInfoQueryDto mtkDto);
    /**
     * 查询市场投标支持流程执行详情。
     *
     * @param queryDto 查询条件，不能为空。
     * @return 包含市场投标支持信息的列表。
     */
    ApprovalVo getMktFlowDataHistory(MktHistoryQueryDto queryDto);

    /* Started by AICoder, pid:id43d1d378k39931403d0b9200fc23320395e82c */
    /**
     * 查询项目的任务概述。
     *
     * <p>
     * 该方法根据项目ID查询并返回该项目的任务概述信息。
     * </p>
     *
     * @param projectId 项目ID，用于标识要查询的项目。
     * @return 返回一个包含任务概述信息的对象。如果没有找到相关任务，可能返回null或空对象。
     */
    TaskDetailVo queryTaskOverview(String projectId);

    /**
     * 查询任务详情列表。
     *
     * <p>
     * 该方法根据查询条件（如页码、每页大小、过滤条件等）查询并返回任务详情列表。
     * </p>
     *
     * @param queryDto 查询条件数据传输对象，包含分页和过滤条件。
     * @return 返回一个包含任务详情的分页对象。如果没有找到任何任务，将返回一个空的分页对象。
     */
    PageVO<TaskDetailVo> queryTaskDetails(TaskDetailQueryDto queryDto);

    PageVO<ProjectTaskInfoVo> queryTaskByType(TaskTypeDetailQueryDto queryDto);

    /**
     * 查询市场任务列表。
     *
     * <p>
     * 该方法根据查询条件（如过滤条件等）查询并返回市场任务列表。
     * </p>
     *
     * @param queryDto 查询条件数据传输对象，包含过滤条件。
     * @return 返回一个包含市场任务的列表。如果没有找到任何任务，将返回一个空列表。
     */
    List<ApprovalVo> queryTaskList(TaskQueryDto queryDto);
    /* Ended by AICoder, pid:id43d1d378k39931403d0b9200fc23320395e82c */

    List<ApprovalCommonVo> queryApprovalByProject(String projectId);
    ApprovalCommonVo getApprovalByApprovalId(String approvalId);

    ApprovalCommonVo queryApprovalByLectotypeId(String lectotypeId);

    List<ApprovalCommonVo> queryApprovalByLectotypeIdList(List<String> lectotypeIdList);
}

/* Ended by AICoder, pid:672d4b007fj7c33141c50859a011a777aaf3b079 */