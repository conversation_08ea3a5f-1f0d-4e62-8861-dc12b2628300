/* Started by AICoder, pid:y2af17a851icfad1457b0b686000311fb7998bab */
package com.zte.uedm.dcdigital.domain.strategy.productUpgrade;

import com.zte.uedm.dcdigital.interfaces.web.dto.ApprovalSubmitDto;

public interface ProductUpgradeTypeHandler {
    /**
     * 获取处理类型标识
     *
     * @return 类型标识字符串
     */
    String getType();

    /**
     * 处理产品提资审批提交
     *
     * @param submitDto 审批提交数据传输对象
     */
    void handle(ApprovalSubmitDto submitDto);
}

/* Ended by AICoder, pid:y2af17a851icfad1457b0b686000311fb7998bab */