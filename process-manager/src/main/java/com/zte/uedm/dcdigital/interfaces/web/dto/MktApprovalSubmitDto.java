package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ToString
public class MktApprovalSubmitDto {
    /**
     * 流程id
     * */
    @NotNull
    private String flowId;
    /**
     * 任务id
     * */
    @NotNull
    private String taskId;
    /**
     * 项目id
     * */
    private String projectId;
    /**
     * 任务类型 0:主任务,1:子任务
     * */
    private String taskType;

    /**
     * mkt任务处理类型 1:接受;2:转交;3:完成、4:验收通过,5:验收不通过
     * */
    @NotNull
    private String acceptType;

    /**
     * 任务分类(1:标书澄清、2：文档编写、3：材料选型、4:市场投标支持)
     * */
    private String mktBidTaskClass;
    /**
     * 转交人Id
     * */
    private String transId;

    /**
     * 转交人姓名
     * */
    private String transName;
    /**
     * 产品小类Id
     * */
    private String productCategoryId;
}
