/* Started by AICoder, pid:e4d5bo83fco885d143290a0ad047453290088001 */
package com.zte.uedm.dcdigital.domain.service;

import com.zte.uedm.dcdigital.domain.model.approval.ProductUpgradeRequest;
import com.zte.uedm.dcdigital.interfaces.web.dto.ApprovalSubmitDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProductUpgradeApprovalTaskDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProductUpgradeApprovalVo;

public interface ProductUpgradeApprovalService {
    /**
     * 添加产品提资审批流程
     *
     * @param productUpgradeRequest 产品提资请求对象
     */
    void addProductUpgradeApproval(ProductUpgradeRequest productUpgradeRequest);

    /**
     * 获取产品提资审批详情
     *
     * @param flowId 流程唯一标识
     * @return 产品提资审批任务详细信息对象
     */
    ProductUpgradeApprovalTaskDetailVo getProductUpgradeApprovalDetail(String flowId);

    /**
     * 接受产品提资审批流程
     *
     * @param submitDto 审批提交数据传输对象
     */
    void acceptProductUpgradeApproval(ApprovalSubmitDto submitDto);

    /**
     * 根据审批ID获取产品提资审批流程信息
     *
     * @param approvalId 审批唯一标识
     * @return 产品提资审批流程信息对象
     */
    ProductUpgradeApprovalVo getProductUpgradeApprovalFlowById(String approvalId);
}

/* Ended by AICoder, pid:e4d5bo83fco885d143290a0ad047453290088001 */