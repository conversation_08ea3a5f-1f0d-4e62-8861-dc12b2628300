/* Started by AICoder, pid:066d0qbb4eaf0b2141520b9900860f18a81869e1 */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.common.bean.process.MktApprovalInfoVo;
import com.zte.uedm.dcdigital.domain.model.approval.MktApprovalAssocInfoPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.MktApprovalAssocInfoDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MktApprovalConvert {
    MktApprovalConvert INSTANCE = Mappers.getMapper(MktApprovalConvert.class);
    @Mappings({})
    List<MktApprovalInfoVo> MktApprovalPoToVoList(List<MktApprovalAssocInfoPo> approvalPo);
    @Mappings({})
    MktApprovalAssocInfoPo MktApprovalDtoToPo(MktApprovalAssocInfoDto mtkDto);
}

/* Ended by AICoder, pid:066d0qbb4eaf0b2141520b9900860f18a81869e1 */
