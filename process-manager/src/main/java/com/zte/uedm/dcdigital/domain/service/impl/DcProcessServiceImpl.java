package com.zte.uedm.dcdigital.domain.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.constant.TaskConstants;
import com.zte.uedm.dcdigital.domain.common.enums.ProcessStatusEnum;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProcessStatusCode;
import com.zte.uedm.dcdigital.domain.model.process.ProcessBo;
import com.zte.uedm.dcdigital.domain.model.process.ProcessNodeDetailEntity;
import com.zte.uedm.dcdigital.domain.model.process.TaskEntity;
import com.zte.uedm.dcdigital.domain.service.ApprovalService;
import com.zte.uedm.dcdigital.domain.service.DcProcessService;
import com.zte.uedm.dcdigital.domain.service.DcTaskService;
import com.zte.uedm.dcdigital.domain.utils.ProcessUtils;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.identitylink.api.history.HistoricIdentityLink;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DcProcessServiceImpl implements DcProcessService {

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private ApprovalService approvalService;

    @Resource
    private AuthService authService;

    @Resource
    private IdentityService identityService;

    @Resource
    private DcTaskService dcTaskService;

    @Resource
    private HistoryService historyService;

    @Resource
    private TaskService taskService;


    @Resource
    private SystemService systemService;



    /* Started by AICoder, pid:s78089df5e76384147e00a78609ec524fc080846 */
    /**
     * 根据流程定义的键启动流程实例。
     *
     * @param processKey         流程定义的键
     * @param processBusinessKey 业务键，用于标识业务流程实例
     * @param variables          启动流程时传递的变量集合
     * @return                   返回新启动的流程实例的ID
     * @throws BusinessException 如果在启动流程过程中发生异常，抛出业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String startProcessByKey(String processKey, String processBusinessKey, Map<String, Object> variables) {
        try {
            // 查询最新的流程定义
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(processKey)
                    .latestVersion()
                    .singleResult();

            // 调用私有方法启动流程实例
            return startProcess(processDefinition, processBusinessKey, variables);
        } catch (Exception e) {
            // 记录错误日志
            log.error("启动流程失败, 错误: {}", e);
            // 抛出自定义业务异常，表示流程启动失败
            throw new BusinessException(ProcessStatusCode.PROCESS_START_FAILED);
        }
    }
    /* Ended by AICoder, pid:s78089df5e76384147e00a78609ec524fc080846 */


    /* Started by AICoder, pid:6429bt44a5j61a314d06098b108b9c2bda665d87 */
    /**
     * 根据流程ID启动流程实例。
     *
     * @param processId         流程定义的ID
     * @param processBusinessKey 业务键，用于标识业务流程实例
     * @param variables         启动流程时传递的变量集合
     * @return                  返回新启动的流程实例的ID
     * @throws BusinessException 如果在启动流程过程中发生异常，抛出业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String startProcessById(String processId, String processBusinessKey, Map<String, Object> variables) {
        try {
            // 根据提供的流程ID查询流程定义
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(processId)
                    .singleResult();

            // 调用私有方法启动流程实例
            return startProcess(processDefinition, processBusinessKey, variables);
        } catch (Exception e) {
            // 记录错误日志
            log.error("启动流程失败, 错误: {}", e);
            // 抛出自定义业务异常，表示流程启动失败
            throw new BusinessException(ProcessStatusCode.PROCESS_START_FAILED);
        }
    }
    /* Ended by AICoder, pid:6429bt44a5j61a314d06098b108b9c2bda665d87 */

    /* Started by AICoder, pid:t5a6b9223ecb669141e00929a092416ddcf02901 */
    /**
     * 获取当前用户待办任务列表、候选任务列表。
     *
     * @param processBo 包含查询条件和分页信息的业务对象
     * @return 包含待办任务列表的分页对象
     */
    @Override
    public PageVO<TaskEntity> todoList(ProcessBo processBo) {
        log.info("parameter: {}", processBo);
        PageVO<TaskEntity> page = new PageVO<>();
        TaskQuery taskQuery = taskService.createTaskQuery()
                .active()
                .includeProcessVariables()
                .taskCandidateOrAssigned(processBo.getUserId());
        if(processBo.getCandidateGroup() != null && !processBo.getCandidateGroup().isEmpty()){
            taskQuery.taskCandidateGroupIn(processBo.getCandidateGroup());
        }

        // 构建搜索条件
        ProcessUtils.buildProcessSearch(taskQuery, processBo);
        // 设置总记录数
        page.setTotal(taskQuery.count());
        int offset = processBo.getPageSize() * (processBo.getPageNum() - 1);
        int totalPages = (int) Math.ceil((double) taskQuery.count() / processBo.getPageSize());
        page.setPages(totalPages);
        List<Task> tasks = taskQuery.listPage(offset, processBo.getPageSize());
        log.info("tasks: {}", tasks);
        List<TaskEntity> flowList = new ArrayList<>();

        for (Task task : tasks) {
            TaskEntity flowTask = new TaskEntity();
            // 设置当前流程信息
            flowTask.setTaskId(task.getId());
            flowTask.setTaskDefKey(task.getTaskDefinitionKey());
            flowTask.setCreateTime(task.getCreateTime());
            flowTask.setProcDefId(task.getProcessDefinitionId());
            flowTask.setTaskName(task.getName());
            // 查询并设置流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(task.getProcessDefinitionId())
                    .singleResult();
            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(task.getProcessInstanceId());
            //在approve表根据流程实例id查询流程状态
            ApprovalVo approvalVo=approvalService.getApprovalByFlowId(task.getProcessInstanceId());
            flowTask.setCategory(String.valueOf(approvalVo.getApprovalType()));
            flowTask.setProcessStatus(String.valueOf(approvalVo.getStatus()));
            // 查询并设置流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId())
                    .singleResult();
            flowTask.setStartUserId(historicProcessInstance.getStartUserId());
            // 添加到待办任务列表
            flowList.add(flowTask);
        }
        page.setList(flowList);
        return page;
    }
    /* Ended by AICoder, pid:t5a6b9223ecb669141e00929a092416ddcf02901 */

    /* Started by AICoder, pid:8d28636c8ec9e62149570917d045cf63aed2f9f8 */
    /**
     * 获取当前用户已办任务列表。
     *
     * @param processBo 包含查询条件和分页信息的业务对象
     * @return 包含待办任务列表的分页对象
     */
    @Override
    public PageVO<TaskEntity> finishedList(ProcessBo processBo) {
        PageVO<TaskEntity> page = new PageVO<>();
        HistoricTaskInstanceQuery taskInstanceQuery = historyService.createHistoricTaskInstanceQuery()
                .includeProcessVariables()
                .finished()
                .taskAssignee(processBo.getUserId());

        // 构建搜索条件
        ProcessUtils.buildProcessSearch(taskInstanceQuery, processBo);
        // 设置总记录数
        page.setTotal(taskInstanceQuery.count());
        int totalPages = (int) Math.ceil((double) taskInstanceQuery.count() / processBo.getPageSize());
        page.setPages(totalPages);
        int offset = processBo.getPageSize() * (processBo.getPageNum() - 1);
        List<HistoricTaskInstance> historicTaskInstanceList = taskInstanceQuery.listPage(offset, processBo.getPageSize());
        List<TaskEntity> hisTaskList = new ArrayList<>();
        for (HistoricTaskInstance histTask : historicTaskInstanceList) {
            TaskEntity flowTask = new TaskEntity();
            // 当前流程信息
            flowTask.setTaskId(histTask.getId());
            // 审批人员信息
            flowTask.setCreateTime(histTask.getCreateTime());
            flowTask.setFinishTime(histTask.getEndTime());
            flowTask.setDuration(DateUtil.formatBetween(histTask.getDurationInMillis(), BetweenFormatter.Level.SECOND));
            flowTask.setProcDefId(histTask.getProcessDefinitionId());
            flowTask.setTaskDefKey(histTask.getTaskDefinitionKey());
            flowTask.setTaskName(histTask.getName());
            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(histTask.getProcessDefinitionId())
                    .singleResult();
            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(histTask.getProcessInstanceId());
            flowTask.setHisProcInsId(histTask.getProcessInstanceId());
            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(histTask.getProcessInstanceId())
                    .singleResult();
            flowTask.setStartUserId(historicProcessInstance.getStartUserId());
            hisTaskList.add(flowTask);
        }
        page.setList(hisTaskList);
        return page;
    }
    /* Ended by AICoder, pid:8d28636c8ec9e62149570917d045cf63aed2f9f8 */


    /**
     * 查询我发起的
     * @param processBo 包含查询条件和分页信息的业务对象
     * @return 包含待办任务列表的分页对象
     */
    /* Started by AICoder, pid:40b4fce1b1e6a80147c209685005cd6eba316283 */
    @Override
    public PageVO<TaskEntity> ownList(ProcessBo processBo) {
        PageVO<TaskEntity> page = new PageVO<>();
        HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery()
                .startedBy(processBo.getUserId());
        log.info("userId={}",processBo.getUserId());
        // 构建搜索条件
        ProcessUtils.buildProcessSearch(historicProcessInstanceQuery, processBo);
        int offset = processBo.getPageSize() * (processBo.getPageNum() - 1);
        List<HistoricProcessInstance> historicProcessInstanceList = historicProcessInstanceQuery.listPage(offset, processBo.getPageSize());
        page.setTotal(historicProcessInstanceQuery.count());
        int totalPages = (int) Math.ceil((double) historicProcessInstanceQuery.count() / processBo.getPageSize());
        page.setPages(totalPages);
        List<TaskEntity> taskEntityList = new ArrayList<>();
        for (HistoricProcessInstance hisIns : historicProcessInstanceList) {
            TaskEntity taskEntity = new TaskEntity();
            // 获取流程状态
            HistoricVariableInstance processStatusVariable = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(hisIns.getId())
                    .variableName(ProcessConstants.PROCESS_STATUS_KEY)
                    .singleResult();
            String processStatus = null;
            if (ObjectUtil.isNotNull(processStatusVariable)) {
                processStatus = Convert.toStr(processStatusVariable.getValue());
            }
            taskEntity.setProcessStatus(processStatus);
            taskEntity.setCreateTime(hisIns.getStartTime());
            taskEntity.setFinishTime(hisIns.getEndTime());
            taskEntity.setProcInsId(hisIns.getId());

            // 计算耗时
            if (Objects.nonNull(hisIns.getEndTime())) {
                taskEntity.setDuration(DateTimeUtils.getDatePoor(hisIns.getEndTime(), hisIns.getStartTime()));
            } else {
                taskEntity.setDuration(DateTimeUtils.getDatePoor(new Date(), hisIns.getStartTime()));
            }
            // 流程部署实例信息
            Deployment deployment = repositoryService.createDeploymentQuery()
                    .deploymentId(hisIns.getDeploymentId()).singleResult();
            taskEntity.setDeployId(hisIns.getDeploymentId());
            taskEntity.setProcDefId(hisIns.getProcessDefinitionId());
            taskEntity.setProcDefName(hisIns.getProcessDefinitionName());
            taskEntity.setProcDefVersion(hisIns.getProcessDefinitionVersion());
            taskEntity.setCategory(deployment.getCategory());
            // 当前所处流程
            List<Task> taskList = taskService.createTaskQuery().processInstanceId(hisIns.getId()).includeIdentityLinks().list();
            if (CollUtil.isNotEmpty(taskList)) {
                taskEntity.setTaskName(taskList.stream().map(Task::getName).filter(StringUtils::isNotEmpty).collect(Collectors.joining(",")));
            }
            taskEntityList.add(taskEntity);
        }
        page.setList(taskEntityList);
        return page;
    }
    /* Ended by AICoder, pid:40b4fce1b1e6a80147c209685005cd6eba316283 */


    /* Started by AICoder, pid:v0767nc1a9yed35141a50b3ed0cf3924a4057014 */
    /**
     * 流程记录
     * @param processId
     * @return
     */
    @Override
    public List<ProcessNodeDetailEntity> processNodeDetail(String processId) {
        log.info("processId={}",processId);
        // 获取流程实例
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processId).includeProcessVariables().singleResult();
        if (ObjectUtil.isNull(historicProcessInstance)) {
            log.error("process instance not found,processId={}", processId);
            throw new BusinessException(ProcessStatusCode.PROCESS_INSTANCE_NOT_FOUND);
        }
        // 获取流程实例的节点信息
        List<HistoricActivityInstance> historicActivityInstanceList = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(historicProcessInstance.getId())
                .activityTypes(CollUtil.newHashSet(BpmnXMLConstants.ELEMENT_EVENT_START, BpmnXMLConstants.ELEMENT_EVENT_END, BpmnXMLConstants.ELEMENT_TASK_USER))
                .orderByHistoricActivityInstanceStartTime().desc()
                .orderByHistoricActivityInstanceEndTime().desc()
                .list();
        log.info("historicProcessInstance id:{}",historicProcessInstance.getId());
        List<Comment> commentList = taskService.getProcessInstanceComments(historicProcessInstance.getId());
        log.info("historicActivityInstanceList size = {}",historicActivityInstanceList.size());
        return historicActivityInstanceList.stream()
                .map(activityInstance -> toProcessNodeDetailEntity(activityInstance, historicProcessInstance, commentList))
                .collect(Collectors.toList());
    }
    /* Ended by AICoder, pid:v0767nc1a9yed35141a50b3ed0cf3924a4057014 */



    /* Started by AICoder, pid:331dala8e7x7f0e14714081fb053cc3f67743ae2 */
    /**
     * 启动流程实例
     * @param processDefinition
     * @param processBusinessKey
     * @param variables
     */
    private String startProcess(ProcessDefinition processDefinition, String processBusinessKey, Map<String, Object> variables) {
        if(ObjectUtil.isNull(processDefinition)){
            log.error("process definition not found,processKey={}", processDefinition == null ? "null" : processDefinition.getKey());
            throw new BusinessException(ProcessStatusCode.PROCESS_DEFINITION_NOT_FOUND);
        }

        if(ObjectUtil.isNotNull(processDefinition) && processDefinition.isSuspended()){
            log.error("process definition is suspended,processKey={}", processDefinition.getKey());
            throw new BusinessException(ProcessStatusCode.PROCESS_SUSPENDED);
        }

        ProcessInstance existingInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processBusinessKey).singleResult();
        if(ObjectUtil.isNotNull(existingInstance)){
            log.error("process instance already exist,processBusinessKey={}", processBusinessKey);
            throw new BusinessException(ProcessStatusCode.PROCESS_INSTANCE_EXIST);
        }
        // 设置流程发起人id到流程中
        String userId = authService.getUserId();
        identityService.setAuthenticatedUserId(userId);
        variables.put(BpmnXMLConstants.ATTRIBUTE_EVENT_START_INITIATOR, userId);
        // 设置流程状态为进行中
        variables.put(ProcessConstants.PROCESS_STATUS_KEY, ProcessStatusEnum.RUNNING.getStatus());
        // 发起流程实例
        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), processBusinessKey, variables);
        // 第一个用户任务为发起人，则自动完成任务
        dcTaskService.startFistTask(processInstance, variables);
        return processInstance.getId();
    }
    /* Ended by AICoder, pid:331dala8e7x7f0e14714081fb053cc3f67743ae2 */


    /* Started by AICoder, pid:3b748l5f66o732c144030812904ebe32f1d7869a */
    /**
     * 将历史活动实例转换为流程节点详细实体对象。
     *
     * @param activityInstance       历史活动实例，包含活动的详细信息。
     * @param historicProcessInstance 历史流程实例，包含流程的详细信息。
     * @param commentList            与流程实例相关的评论列表。
     * @return ProcessNodeDetailEntity 转换后的流程节点详细实体对象。
     */
    private ProcessNodeDetailEntity toProcessNodeDetailEntity(HistoricActivityInstance activityInstance, HistoricProcessInstance historicProcessInstance, List<Comment> commentList) {
        ProcessNodeDetailEntity entity = new ProcessNodeDetailEntity();
        entity.setProcDefId(activityInstance.getProcessDefinitionId());
        entity.setActivityId(activityInstance.getActivityId());
        entity.setActivityName(activityInstance.getActivityName());
        entity.setActivityType(activityInstance.getActivityType());
        entity.setCreateTime(activityInstance.getStartTime());
        entity.setEndTime(activityInstance.getEndTime());

        if (activityInstance.getDurationInMillis() != null) {
            entity.setDuration(DateUtil.formatBetween(activityInstance.getDurationInMillis(), BetweenFormatter.Level.SECOND));
        }



        // 处理开始节点
        if (BpmnXMLConstants.ELEMENT_EVENT_START.equals(activityInstance.getActivityType())) {
            if (ObjectUtil.isNotNull(historicProcessInstance)) {
                entity.setAssigneeId(historicProcessInstance.getStartUserId());
                UserVo userinfo = systemService.getUserinfoById(historicProcessInstance.getStartUserId());
                entity.setAssigneeName(userinfo.getDisplayText());
            }
        } else if (BpmnXMLConstants.ELEMENT_TASK_USER.equals(activityInstance.getActivityType())) {
            if (StringUtils.isNotBlank(activityInstance.getAssignee())) {
                UserVo userinfo = systemService.getUserinfoById(activityInstance.getAssignee());
                entity.setAssigneeId(activityInstance.getAssignee());
                entity.setAssigneeName(userinfo.getDisplayText());
            }
            setCandidateUsers(entity, activityInstance.getTaskId());
            setComments(entity, commentList, activityInstance.getTaskId());
        }
        return entity;
    }
    /* Ended by AICoder, pid:3b748l5f66o732c144030812904ebe32f1d7869a */


    /* Started by AICoder, pid:9bff4u7ebcm8a0c14dc20b62e03d9d2942229ee6 */
    /**
     * 设置流程节点的候选用户信息。
     *
     * @param entity  流程节点详细实体对象
     * @param taskId  任务ID
     */
    private void setCandidateUsers(ProcessNodeDetailEntity entity, String taskId) {
        List<HistoricIdentityLink> identityLinks = historyService.getHistoricIdentityLinksForTask(taskId);
        StringBuilder candidateBuilder = new StringBuilder();

        for (HistoricIdentityLink identityLink : identityLinks) {
            if (TaskConstants.PROCESS_CANDIDATE.equals(identityLink.getType()) && StringUtils.isNotBlank(identityLink.getUserId())) {
                UserVo userinfo = systemService.getUserinfoById(identityLink.getUserId());
                String userName = userinfo.getDisplayText();
                candidateBuilder.append(userName).append(",");
            }
        }

        if (candidateBuilder.length() > 0) {
            entity.setCandidate(candidateBuilder.substring(0, candidateBuilder.length() - 1));
        }
    }
    /* Ended by AICoder, pid:9bff4u7ebcm8a0c14dc20b62e03d9d2942229ee6 */

    /* Started by AICoder, pid:86a20ob45d913461499808dbe0641f1cf60444c0 */
    /**
     * @param entity     流程节点详细实体对象
     * @param commentList 评论列表
     * @param taskId     任务ID
     */
    private void setComments(ProcessNodeDetailEntity entity, List<Comment> commentList, String taskId) {
        log.info("setComments, taskId: {}", taskId);
        log.info("setComments: commentList size:{}",commentList.size());
        if(CollUtil.isNotEmpty(commentList)){
            List<Comment> comments = new ArrayList<>();
            for (Comment comment : commentList) {
                log.info("comment taskId = {}",comment.getTaskId());
                if (comment.getTaskId().equals(taskId)) {
                    comments.add(comment);
                }
            }
            log.info("coments = {}",comments.size());
            entity.setCommentList(comments);
        }

    }
    /* Ended by AICoder, pid:86a20ob45d913461499808dbe0641f1cf60444c0 */


}
