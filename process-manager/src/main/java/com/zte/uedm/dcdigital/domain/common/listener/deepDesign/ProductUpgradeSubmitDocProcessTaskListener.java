/* Started by AICoder, pid:r4fe8vcf97i442114cd208b1d0c94d212737b7e3 */
package com.zte.uedm.dcdigital.domain.common.listener.deepDesign;

import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;

/**
 * 深化设计-产品提资任务 提交文档节点 任务监听器
 */
@Slf4j
@Component
public class ProductUpgradeSubmitDocProcessTaskListener implements TaskListener {

    @Override
    public void notify(DelegateTask delegateTask) {
        // 获取实际处理人变量
        String actualAssignee = (String) delegateTask.getVariable(ProcessConstants.PRODUCT_UPGRADE_USER);

        if (StringUtils.isNotBlank(actualAssignee)) {
            // 将接受任务的用户存储在流程变量中，用于验收不通过时流转使用
            delegateTask.setVariable(ProcessConstants.PRODUCT_UPGRADE_USER, actualAssignee);
        }
    }
}

/* Ended by AICoder, pid:r4fe8vcf97i442114cd208b1d0c94d212737b7e3 */