package com.zte.uedm.dcdigital.domain.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zte.uedm.dcdigital.common.bean.document.FileInfoVo;
import com.zte.uedm.dcdigital.common.bean.document.ProductUpgradeFileVo;
import com.zte.uedm.dcdigital.common.bean.enums.system.RoleCodeEnum;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryAndBrandInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.bean.project.ProjectDetailInfoVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.constant.TaskConstants;
import com.zte.uedm.dcdigital.domain.common.enums.*;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProcessStatusCode;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.approval.ProductUpgradeRequest;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalEntity;
import com.zte.uedm.dcdigital.domain.model.process.ProcessNodeDetailEntity;
import com.zte.uedm.dcdigital.domain.repository.ApprovalRepository;
import com.zte.uedm.dcdigital.domain.service.DcProcessService;
import com.zte.uedm.dcdigital.domain.service.DcTaskService;
import com.zte.uedm.dcdigital.domain.service.ProductUpgradeApprovalService;
import com.zte.uedm.dcdigital.domain.strategy.productUpgrade.ProductUpgradeTypeHandler;
import com.zte.uedm.dcdigital.interfaces.web.dto.ApprovalSubmitDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import com.zte.uedm.dcdigital.sdk.project.dto.DeepenDesignFilesRelationInnerAddDto;
import com.zte.uedm.dcdigital.sdk.project.service.DeepenDesignInnerService;
import com.zte.uedm.dcdigital.sdk.project.service.ItemInfoService;
import com.zte.uedm.dcdigital.sdk.project.service.ProjectService;
import com.zte.uedm.dcdigital.sdk.project.vo.DeepenDesignFilesRelationInnerVo;
import com.zte.uedm.dcdigital.sdk.project.vo.DeepenImplementationInnerVo;
import com.zte.uedm.dcdigital.sdk.project.vo.ItemInfoInnerVo;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductUpgradeApprovalServiceImpl implements ProductUpgradeApprovalService {
    private final Map<String, ProductUpgradeTypeHandler> handlerMap;
    // 通过构造函数注入所有TypeHandler实现
    @Autowired
    public ProductUpgradeApprovalServiceImpl(List<ProductUpgradeTypeHandler> handlers) {
        handlerMap = handlers.stream()
                .collect(Collectors.toMap(
                        ProductUpgradeTypeHandler::getType,
                        Function.identity()
                ));
    }

    @Resource
    private DeepenDesignInnerService designInnerService;
    @Resource
    private ProjectService projectService;

    @Resource
    private ProductService productService;

    @Resource
    private ApprovalRepository approvalRepository;

    @Resource
    private AuthService authService;
    @Resource
    private SystemService systemService;

    @Resource
    private RuntimeService runtimeService;
    @Resource
    private IdentityService identityService;
    @Autowired
    private DcTaskService dcTaskService;

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private DcProcessService dcProcessService;

    @Resource
    private ItemInfoService itemInfoService;

    @Resource
    private DocumentService documentService;

    @Override
    public void addProductUpgradeApproval(ProductUpgradeRequest productUpgrade) {
        log.info("addProductUpgradeApproval productUpgrade: {}", productUpgrade);
        //项目id
        String itemId=productUpgrade.getItemId();
        //商机id
        String businessId=productUpgrade.getResourceId();
        //产品小类id(去重)
        List<String> productCategoryIdList=productUpgrade.getCategoryIdList().stream().distinct().collect(Collectors.toList());
        //当前登录用户
        String currentUserId = authService.getUserId();
        //校验任务创建条件(产品小类是否配置产品SE)
        checkConditionByproductCategory(itemId, currentUserId, productCategoryIdList);
        // 调用RPC查询产品小类信息
        List<ProductCategoryInfoVo> productCategoryInfoVos = productService.selectProductCategoryList(productCategoryIdList);
        // 构建产品小类对应的路径名称map集合(key为产品小类id, value为产品小类路径名称)
        Map<String, String> productCategoryPathNameMap = productCategoryInfoVos.stream()
                .collect(Collectors.toMap(ProductCategoryInfoVo::getId, ProductCategoryInfoVo::getPathName));
        //当前任务类型
        Integer approvalType= Integer.parseInt(ProcessEnum.PRODUCT_SUBMISSION_TASK.getType());
        // 创建一个集合用于存储已经存在的"产品提资"流程任务(projectId, productCategoryId, approvalType)组合，防止重复
        Set<String> processedCombinations = new HashSet<>();
        //遍历产品小类生成"产品提资"任务
        for (String productCategoryId : productCategoryIdList) {
            // 创建一个唯一标识符表示当前组合(避免重复创建"产品提资"流程任务:项目id+产品小类+任务流程类型的任务只能有一个)
            String combinationKey = itemId + ":" + productCategoryId + ":" + approvalType;
            // 检查是否此组合已经被处理或存在于数据库中
            if (!processedCombinations.contains(combinationKey) && !approvalRepository.existsCombination(itemId, productCategoryId, approvalType)) {
                // 组装待添加的流程对象实体
                ApprovalEntity approvalEntity = assemblyApprovalEntityParam(itemId, productCategoryId, approvalType, currentUserId);
                String pathName = productCategoryPathNameMap.get(productCategoryId);
                // 创建"产品提资"流程任务
                String flowId=this.createProductUpgradeApproval(approvalEntity, pathName,businessId);
                // 将此组合标记为已处理
                processedCombinations.add(combinationKey);
                //添加产品小类到提资任务关联附件信息
                this.addDeepenDesignFilesRelation(flowId,itemId,productCategoryId,currentUserId);
            } else {
                //存在
                log.info("Duplicate approval task detected: itemId:{}, productCategoryId:{}, approvalType:{}", itemId, productCategoryId, approvalType);
            }
            //TODO 为当前用户(设计负责人)赋予LTC管理、项目管理菜单权限
            //dcTaskService.addProjectPermission(currentUserId, "", businessId,productCategoryId);
        }
    }
    public void addDeepenDesignFilesRelation(String flowId,String itemId,String categoryId,String currentUserId){
        String currentTime=DateTimeUtils.getCurrentTime();
        DeepenDesignFilesRelationInnerAddDto addDto=new DeepenDesignFilesRelationInnerAddDto();
        addDto.setFlowId(flowId);
        addDto.setItemId(itemId);
        addDto.setProductCategoryId(categoryId);
        addDto.setCreateBy(currentUserId);
        addDto.setCreateTime(currentTime);
        addDto.setUpdateTime(currentTime);
        addDto.setSubmitUser(currentUserId);
        //默认是隐藏/不通过状态
        addDto.setApprovalResult(ProcessConstants.STR_ZERO);
        designInnerService.addDeepenDesignFilesRelation(addDto);
    }
    @Override
    public ProductUpgradeApprovalTaskDetailVo getProductUpgradeApprovalDetail(String flowId) {
        ApprovalObj approvalObj = approvalRepository.getByFlowId(flowId);
        if (approvalObj==null){
            log.error("Failed to query market bid support details by task id，flowId:{}",flowId);
            throw new BusinessException(ProcessStatusCode.PROCESS_DEFINITION_NOT_FOUND);
        }
        ProductUpgradeApprovalTaskDetailVo detailVo=new ProductUpgradeApprovalTaskDetailVo();
        detailVo.setFlowType(ProcessEnum.PRODUCT_SUBMISSION_TASK.getProcessName());
        //调用RPC根据项目id查询项目信息
        ItemInfoInnerVo itemInfoDetailInfo = itemInfoService.getItemInfoDetailInfo(approvalObj.getProjectId());
        if (itemInfoDetailInfo==null){
            log.error("Failed to query project information by project id，projectId:{}",approvalObj.getProjectId());
            throw new BusinessException(ProcessStatusCode.PROJECT_NOT_FOUND);
        }
        //调用RPC根据商机id查询商机信息
        ProjectDetailInfoVo detailInfoVo=projectService.getProjectDetailInfo(itemInfoDetailInfo.getProjectId());
        List<String> categoryIds = new ArrayList<>();
        categoryIds.add(approvalObj.getResourceId());
        //调用RPC查询产品小类名称
        List<ProductCategoryAndBrandInfoVo> categoryAndBrandInfoVos = productService.selectProductCategoryAndBrandList(categoryIds);
        if (CollectionUtils.isEmpty(categoryAndBrandInfoVos)){
            log.error("Failed to query product category name by category id，categoryId:{}",approvalObj.getResourceId());
            throw new BusinessException(ProcessStatusCode.PRODUCT_CATEGORY_NOT_FOUND);
        }
        //产品小类名称(因为一个产品小类id只会对应一个产品小类，于是从下标0取值)
        String productCategoryName=categoryAndBrandInfoVos.get(0).getProductName();
        //项目id
        detailVo.setId(detailInfoVo.getId());
        //标题名称：项目名称+产品小类名称+任务类型名称
        String name=detailInfoVo.getName()+" "+productCategoryName+" "+ProcessEnum.PRODUCT_SUBMISSION_TASK.getProcessName();
        detailVo.setProcessTitleName(name);
        detailVo.setTitle(approvalObj.getTitle());
        String handlerUserId=approvalObj.getUpdateBy();
        UserVo userInfo = systemService.getUserinfoById(handlerUserId);
        //实时处理人
        detailVo.setSubmitUser(userInfo.getDisplayText());
        //实时处理时间
        detailVo.setSubmitTime(approvalObj.getSubmitTime());
        //设置按钮
        List<ManualButton> buttonArray=getButtonArray(approvalObj);
        detailVo.setButtonArray(buttonArray);
        //设置附件列表属性
        setFilesInfo(detailVo,flowId,ProductUpgradeApprovalTaskDetailVo::setFileInfos,approvalObj.getProjectId(),approvalObj.getResourceId());
        return detailVo;
    }

    @Override
    public void acceptProductUpgradeApproval(ApprovalSubmitDto submitDto) {
        log.info("AcceptanceTaskHandler acceptManualApproval submitDto:{}",submitDto);
        if (submitDto.getTransId()==null||submitDto.getTransId().equals("")){
            //当入参的转交人id为null时说明是非"转交"操作
            String currentUserId = authService.getUserId();
            UserVo currentUserInfo = systemService.getUserinfoById(currentUserId);
            submitDto.setTransId(currentUserId);
            submitDto.setCurrentProcessorId(currentUserId);
            submitDto.setCurrentProcessorName(currentUserInfo.getName());
        }
        //根据processTypeCode获取对应的策略
        String processTypeCode=submitDto.getResult().toString();
        // 获取并执行处理器
        ProductUpgradeTypeHandler handler = handlerMap.get(processTypeCode);
        if (handler == null) {
            log.error("Unknown process type: {}", processTypeCode);
            throw new BusinessException(ProcessStatusCode.PROCESS_INSTANCE_NOT_FOUND);
        }

        // 执行具体处理逻辑
        handler.handle(submitDto);
    }

    @Override
    public ProductUpgradeApprovalVo getProductUpgradeApprovalFlowById(String approvalId) {
        Locale currentLocale = I18nUtil.getLanguage();
        ApprovalObj approvalObj = approvalRepository.getById(approvalId);
        log.info("getProductUpgradeApprovalFlowById approvalObj: {}", approvalObj);
        ProductUpgradeApprovalVo approvalVo=new ProductUpgradeApprovalVo();
        BeanUtils.copyProperties(approvalObj,approvalVo);
        approvalVo.setFlowType(ProcessEnum.PRODUCT_SUBMISSION_TASK.getProcessName());
        approvalVo.setStatus(approvalObj.getStatus());
        log.info("getProductUpgradeApprovalFlowById approvalVo: {}", approvalVo);
        List<Comment> commentList = new ArrayList<>();
        UserVo userInfo = systemService.getUserinfoById(approvalVo.getSubmitUser());
        if(userInfo!=null){
            //发起人
            approvalVo.setSubmitUser(userInfo.getDisplayText());
        }
        //发起时间
        approvalVo.setSubmitTime(approvalObj.getSubmitTime());
        List<ProcessNodeDetailEntity> processNodeDetailEntities = dcProcessService.processNodeDetail(approvalObj.getFlowId());
        log.info("processNodeDetailEntities={}", processNodeDetailEntities);
        processNodeDetailEntities.forEach(processNodeDetail -> {
            if (ObjectUtil.isNotNull(processNodeDetail.getCommentList()) && ObjectUtil.isNotEmpty(processNodeDetail.getCommentList())) {
                commentList.addAll(processNodeDetail.getCommentList());

            }
        });
        List<String> userIds = commentList.stream().map(Comment::getUserId).collect(Collectors.toList());
        log.info("getProductUpgradeApprovalFlowById commentList: {}", commentList);
        List<Comment> filteredList = commentList.stream()
                .filter(item -> !item.getType().equals("comment") && !item.getType().equals("event"))
                .collect(Collectors.toList());
        List<UserVo> userVos = systemService.getUserinfoByIds(userIds);
        Map<String, String> map = userVos.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getDisplayText));
        List<ProductUpgradeCommentVo> comments = new ArrayList<>();
        filteredList.forEach(comment -> {
            String id = comment.getId();
            String userId = comment.getUserId();
            String time = DateTimeUtils.getStringTime(comment.getTime());
            String type = comment.getType();
            String fullMessage = comment.getFullMessage();
            if (type.equals("comment")){
                //String resultStr=I18nUtil.getI18nFromString(ProductUpgradeFlowCommentEnum.getRemarkByType(result), currentLocale);
                String role= ProductUpgradeRoleManualEnum.ACCEPTANCE.getDescription()+" "+map.get(userId);
                comments.add(new ProductUpgradeCommentVo(id, role, time, ProcessConstants.ZERO, fullMessage,""));
            }else {
                String role= ProductUpgradeRoleManualEnum.fromCode(Integer.parseInt(type)).getDescription()+" "+map.get(userId);
                String resultStr=I18nUtil.getI18nFromString(ProductUpgradeFlowCommentEnum.getRemarkByType(type), currentLocale);
                comments.add(new ProductUpgradeCommentVo(id, role, time, Integer.parseInt(type), fullMessage,resultStr));
            }
        });
        //流程发起人
        assemblyStartComment(approvalObj.getFlowId(),comments,map.get(approvalObj.getCreateBy()),approvalObj.getCreateTime());
        approvalVo.setProductUpgradeComments(comments);
        //设置附件列表属性
        setFilesInfo(approvalVo,approvalObj.getFlowId(),ProductUpgradeApprovalVo::setFileInfos,approvalObj.getProjectId(),approvalObj.getResourceId());
        return approvalVo;
    }
    private void assemblyStartComment(String flowId,List<ProductUpgradeCommentVo> comments,String user,String createTime){
        ProductUpgradeCommentVo startComment=new ProductUpgradeCommentVo();
        startComment.setId(flowId);
        startComment.setResult(ProcessConstants.ZERO);
        startComment.setCreateTime(createTime);
        startComment.setRoleUser(ProductUpgradeRoleManualEnum.ACCEPTANCE.getDescription()+" "+user);
        startComment.setProcessingResult("发起流程");
        comments.add(startComment);
    }
    //根据当前"产品提资任务"流程状态展示不同的按钮
    private List<ManualButton> getButtonArray(ApprovalObj approvalObj){
        List<ManualButton> buttonArray=new ArrayList<>();
        int condition=approvalObj.getStatus();
        Locale currentLocale = I18nUtil.getLanguage();
        if (condition==ApprovalStatusEnum.MKT_PENDING.getCode()){
            //接收
            ManualButton manualButton=new ManualButton();
            manualButton.setButtonId(Integer.parseInt(ProductUpgradeFlowCommentEnum.ACCEPT_PRODUCT_UPGRADE.getType()));
            manualButton.setButtonName(I18nUtil.getI18nFromString(ProductUpgradeFlowCommentEnum.getRemarkByType(ProductUpgradeFlowCommentEnum.ACCEPT_PRODUCT_UPGRADE.getType()), currentLocale));
            buttonArray.add(manualButton);
            //转交
            ManualButton manualButton2=new ManualButton();
            manualButton2.setButtonId(Integer.parseInt(ProductUpgradeFlowCommentEnum.TRANSFER_PRODUCT_UPGRADE.getType()));
            manualButton2.setButtonName(I18nUtil.getI18nFromString(ProductUpgradeFlowCommentEnum.getRemarkByType(ProductUpgradeFlowCommentEnum.TRANSFER_PRODUCT_UPGRADE.getType()), currentLocale));
            buttonArray.add(manualButton2);
        } else if (condition ==ApprovalStatusEnum.MKT_IN_PROGRESS.getCode()) {
            //转交
            ManualButton manualButton2=new ManualButton();
            manualButton2.setButtonId(Integer.parseInt(ProductUpgradeFlowCommentEnum.TRANSFER_PRODUCT_UPGRADE.getType()));
            manualButton2.setButtonName(I18nUtil.getI18nFromString(ProductUpgradeFlowCommentEnum.getRemarkByType(ProductUpgradeFlowCommentEnum.TRANSFER_PRODUCT_UPGRADE.getType()), currentLocale));
            buttonArray.add(manualButton2);
            //确认
            ManualButton manualButton4=new ManualButton();
            manualButton4.setButtonId(Integer.parseInt(ProductUpgradeFlowCommentEnum.COMPLETE_PRODUCT_UPGRADE.getType()));
            manualButton4.setButtonName(I18nUtil.getI18nFromString(ProductUpgradeFlowCommentEnum.getRemarkByType(ProductUpgradeFlowCommentEnum.COMPLETE_PRODUCT_UPGRADE.getType()), currentLocale));
            buttonArray.add(manualButton4);
        } else if (condition==ApprovalStatusEnum.MKT_APPROVED.getCode()) {
            //验收通过
            ManualButton manualButton5=new ManualButton();
            manualButton5.setButtonId(Integer.parseInt(ProductUpgradeFlowCommentEnum.ACCEPTANCE_PRODUCT_UPGRADE.getType()));
            manualButton5.setButtonName(I18nUtil.getI18nFromString(ProductUpgradeFlowCommentEnum.getRemarkByType(ProductUpgradeFlowCommentEnum.ACCEPTANCE_PRODUCT_UPGRADE.getType()), currentLocale));
            buttonArray.add(manualButton5);
            //验收不通过
            ManualButton manualButton6=new ManualButton();
            manualButton6.setButtonId(Integer.parseInt(ProductUpgradeFlowCommentEnum.REJECT_PRODUCT_UPGRADE.getType()));
            manualButton6.setButtonName(I18nUtil.getI18nFromString(ProductUpgradeFlowCommentEnum.getRemarkByType(ProductUpgradeFlowCommentEnum.REJECT_PRODUCT_UPGRADE.getType()), currentLocale));
            buttonArray.add(manualButton6);
        }
        return buttonArray;
    }

    /***
     * 创建流程任务
     * @param approvalEntity 流程对象实体
     * @param pathName 产品小类路径名称
     * @param opportunityId 商机id
     */
    public String createProductUpgradeApproval(ApprovalEntity approvalEntity,String pathName,String opportunityId) {
        log.info("createProductUpgradeApproval: {}", approvalEntity);
        // 设置流程任务标题
        String title = pathName+ TaskConstants.PRODUCT_SUBMISSION_TASK_TITLE;
        approvalEntity.setTitle(title);
        HashMap<String, Object> map = new HashMap<>();
        //产品小类id流程变量
        map.put(ProcessConstants.PRODUCT_CATEGORY, approvalEntity.getResourceId());
        //产品小类路径名称流程变量
        map.put(TaskConstants.PRODUCT_CATEGORY_PATH, pathName);
        //设计负责人流程变量
        map.put(ProcessConstants.DESIGN_LEADER, approvalEntity.getSubmitUser());
        //手动下发"产品提资任务"流程类型变量
        map.put(ProcessConstants.APPROVAL_TYPE, approvalEntity.getApprovalType());
        //项目id流程变量
        map.put(ProcessConstants.ITEM_ID, approvalEntity.getProjectId());
        //商机id流程变量
        map.put(ProcessConstants.PROJECT_ID, opportunityId);
        String id = approvalEntity.getId();
        approvalRepository.createApproval(approvalEntity);
        //"产品提资任务"流程
        String processKey = ProcessEnum.PRODUCT_SUBMISSION_TASK.getProcessKey();
        //approvalRepository.updateFlowIdById(id, flowId);
        String flowId = this.startProcessByKey(processKey, id, map);
        return flowId;
    }


    /**
     * 根据流程定义的键启动流程实例。
     *
     * @param processKey         流程定义的键
     * @param processBusinessKey 业务键，用于标识业务流程实例
     * @param variables          启动流程时传递的变量集合
     * @return                   返回新启动的流程实例的ID
     * @throws BusinessException 如果在启动流程过程中发生异常，抛出业务异常
     */
    @Transactional(rollbackFor = Exception.class)
    public String startProcessByKey(String processKey, String processBusinessKey, Map<String, Object> variables) {
        try {
            // 查询最新的流程定义
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(processKey)
                    .latestVersion()
                    .singleResult();

            // 调用私有方法启动流程实例
            return startProcess(processDefinition, processBusinessKey, variables);
        } catch (Exception e) {
            // 记录错误日志
            log.error("启动流程失败, 错误: {}", e);
            // 抛出自定义业务异常，表示流程启动失败
            throw new BusinessException(ProcessStatusCode.PROCESS_START_FAILED);
        }
    }

    /**
     * 启动流程实例
     * @param processDefinition
     * @param processBusinessKey
     * @param variables
     */
    public String startProcess(ProcessDefinition processDefinition, String processBusinessKey, Map<String, Object> variables) {
        if(ObjectUtil.isNull(processDefinition)){
            log.error("process definition not found,processKey={}", processDefinition == null ? "null" : processDefinition.getKey());
            throw new BusinessException(ProcessStatusCode.PROCESS_DEFINITION_NOT_FOUND);
        }

        if(ObjectUtil.isNotNull(processDefinition) && processDefinition.isSuspended()){
            log.error("process definition is suspended,processKey={}", processDefinition.getKey());
            throw new BusinessException(ProcessStatusCode.PROCESS_SUSPENDED);
        }

        ProcessInstance existingInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processBusinessKey).singleResult();
        if(ObjectUtil.isNotNull(existingInstance)){
            log.error("process instance already exist,processBusinessKey={}", processBusinessKey);
            throw new BusinessException(ProcessStatusCode.PROCESS_INSTANCE_EXIST);
        }
        // 设置流程发起人id到流程中
        String userId = authService.getUserId();
        identityService.setAuthenticatedUserId(userId);
        variables.put(BpmnXMLConstants.ATTRIBUTE_EVENT_START_INITIATOR, userId);
        // 设置流程状态为进行中
        variables.put(ProcessConstants.PROCESS_STATUS_KEY, ProcessStatusEnum.RUNNING.getStatus());
        // 发起流程实例
        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), processBusinessKey, variables);
        //更新approval表
        approvalRepository.updateFlowIdById(processBusinessKey, processInstance.getId());
        // 第一个用户任务为发起人，则自动完成任务
        dcTaskService.startFistTask(processInstance, variables);
        return processInstance.getId();
    }

    /**
     * @param itemId:       项目id
     * @param productCategoryId: 产品小类id
     * @param approvalType:    审批类型
     * @param currentUserId:   当前用户id
     * @return ApprovalEntity: 流程对象实体
     */
    private ApprovalEntity assemblyApprovalEntityParam(String itemId, String productCategoryId, Integer approvalType, String currentUserId) {
        String id = UUID.randomUUID().toString();
        ApprovalEntity approvalEntity = new ApprovalEntity();
        approvalEntity.setId(id);
        approvalEntity.setBussesDataJson(itemId);
        // 产品小类id
        approvalEntity.setResourceId(productCategoryId);
        approvalEntity.setApprovalType(approvalType);
        approvalEntity.setSubmitUser(currentUserId);
        approvalEntity.setSubmitTime(DateTimeUtils.getCurrentTime());
        approvalEntity.setStatus(ApprovalStatusEnum.MKT_PENDING.getCode());
        approvalEntity.setCreateTime(DateTimeUtils.getCurrentTime());
        approvalEntity.setUpdateTime(DateTimeUtils.getCurrentTime());
        approvalEntity.setCreateBy(currentUserId);
        approvalEntity.setUpdateBy(currentUserId);
        approvalEntity.setBillQuantityPerson(currentUserId);
        approvalEntity.setProjectId(itemId);
        return approvalEntity;
    }
    /**
     * 检查创建品审单之前是否设置必要角色(对于商机、项目需要校验方案经理;对于产品小类需要校验产品SE)
     *
     * @param itemId:          项目id
     * @param productCategoryIds: 产品小类id合集
     */
    private void checkConditionByproductCategory(String itemId,String currentUserId, List<String> productCategoryIds) {
        if (CollectionUtils.isEmpty(productCategoryIds)) {
            log.error("checkConditionByproductCategory:productCategoryIds is null");
            // 缺失产品小类
            throw new BusinessException(ProcessStatusCode.PRODUCT_CATEGORY_NOT_FOUND);
        }
        if (itemId == null) {
            log.error("checkConditionByproductCategory:itemId is null");
            // 未获取到项目信息
            throw new BusinessException(ProcessStatusCode.PROJECT_NOT_FOUND);
        }
        // 调用RPC判断创建用户是否是前项目的深化设计负责人
        DeepenImplementationInnerVo innerVo = designInnerService.innerQueryDeepenImplementation(itemId);
        // 深化设计负责人
        String designDirector = innerVo.getDesignDirector();
        if (StringUtils.isEmpty(designDirector)) {
            log.error("checkConditionByproductCategory:designDirector is null");
            // 当前项目缺失深化设计负责人
            throw new BusinessException(ProcessStatusCode.DESIGN_DIRECTOR_NOT_FOUND);
        }
        log.info("designDirector:{},currentUserId:{}", designDirector, currentUserId);
        // 如果不存在就抛出异常提示
        if (!designDirector.equals(currentUserId)) {
            log.error("checkConditionByproductCategory:The current user is not the design leader of this project and thus cannot issue the product upgrade task");
            // 当前用户不是此项目的设计负责人,无权限下发任务
            throw new BusinessException(ProcessStatusCode.CURRENT_USER_NOT_DESIGN_DIRECTOR);
        }
        // 查询产品小类是否设置产品SE，调用RPC查询产品小类的产品se信息(Map<String,List<String>> stringListMap key为产品小类id,value为产品seId集合)
        Map<String, List<String>> stringListMap = systemService.getUserIdByEntityIdAndRoleCode(productCategoryIds, RoleCodeEnum.PRODUCT_SE.getCode());
        // 遍历stringListMap，只要有一个value为空，就抛出异常提示产品小类缺失产品SE
        for (Map.Entry<String, List<String>> entry : stringListMap.entrySet()) {
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(entry.getValue())) {
                log.error("createMarketBidApproval:product se is null, productCategoryId:{}", entry.getKey());
                throw new BusinessException(ProcessStatusCode.PRODUCT_SE_NOT_FOUND);
            }
        }
    }

    /* Started by AICoder, pid:23a4cb20a4aa3bb1415b0bd2003de9442dc0b36e */
    /**
     * 设置文件信息到目标对象
     *
     * @param targetObject 目标对象，用于接收文件信息设置
     * @param flowId 当前流程ID，用于查询最新文件信息
     * @param setter 函数式接口，用于将合并后的文件信息列表设置到目标对象
     * @param itemId 项目ID，用于关联提资任务流程记录
     * @param productCategoryId 产品类别ID，用于限定文件查询范围
     */
    public <T> void setFilesInfo(T targetObject, String flowId, BiConsumer<T, List<FileInfoVo>> setter,
                                 String itemId, String productCategoryId) {
        // 创建包含当前和历史流程ID的列表
        List<String> flowIds = new ArrayList<>();
        flowIds.add(flowId);

        // 查询历史流程ID
        DeepenDesignFilesRelationInnerAddDto queryDto = new DeepenDesignFilesRelationInnerAddDto();
        queryDto.setItemId(itemId);
        queryDto.setProductCategoryId(productCategoryId);
        List<DeepenDesignFilesRelationInnerVo> historyFlows = designInnerService.queryDeepenDesignFilesRelation(queryDto);
        if (CollectionUtils.isNotEmpty(historyFlows)) {
            // 提取历史流程ID并合并到查询列表
            List<String> pastFlowIds = historyFlows.stream()
                    .map(DeepenDesignFilesRelationInnerVo::getFlowId)
                    .collect(Collectors.toList());
            flowIds.addAll(pastFlowIds);
        }

        // 查询所有关联流程的文件信息
        List<ProductUpgradeFileVo> fileGroups = documentService.queryProductUpgradeFilesByFlowIds(flowIds);
        // 合并所有文件信息为单一列表
        List<FileInfoVo> allFiles = fileGroups.stream()
                .flatMap(group -> group.getFileInfos().stream())
                .collect(Collectors.toList());

        // 通过函数式接口设置文件信息
        setter.accept(targetObject, allFiles);
    }

    /* Ended by AICoder, pid:23a4cb20a4aa3bb1415b0bd2003de9442dc0b36e */

}
