/* Started by AICoder, pid:v3986ref32s374414afc080ff1fe6a0248c8acff */
package com.zte.uedm.dcdigital.domain.strategy.productUpgrade;

import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.enums.ApprovalStatusEnum;
import com.zte.uedm.dcdigital.domain.common.enums.ProductUpgradeFlowCommentEnum;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalEntity;
import com.zte.uedm.dcdigital.domain.model.process.TaskBo;
import com.zte.uedm.dcdigital.domain.repository.ApprovalRepository;
import com.zte.uedm.dcdigital.domain.service.ApprovalService;
import com.zte.uedm.dcdigital.domain.service.DcTaskService;
import com.zte.uedm.dcdigital.domain.strategy.ManualTaskHandlerUtil;
import com.zte.uedm.dcdigital.interfaces.web.dto.ApprovalSubmitDto;
import com.zte.uedm.dcdigital.sdk.project.dto.DeepenDesignFilesRelationInnerAddDto;
import com.zte.uedm.dcdigital.sdk.project.service.DeepenDesignInnerService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.IdentityService;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class CompleteProductUpgradeTaskHandler implements ProductUpgradeTypeHandler {
    @Resource
    private ApprovalRepository approvalRepository;
    @Autowired
    private DcTaskService dcTaskService;
    @Autowired
    private ManualTaskHandlerUtil manualTaskHandlerUtil;
    @Resource
    private TaskService taskService;
    @Resource
    private ApprovalService approvalService;
    @Resource
    private IdentityService identityService;
    @Autowired
    @Qualifier("sdkDeepenDesignServiceImpl")
    private DeepenDesignInnerService designInnerService;

    @Override
    public String getType() {
        return ProductUpgradeFlowCommentEnum.COMPLETE_PRODUCT_UPGRADE.getType();
    }

    @Override
    @Transactional
    public void handle(ApprovalSubmitDto submitDto) {
        log.info("CompleteProductUpgradeTaskHandler handle taskBo: {}", submitDto);
        TaskBo taskBo = new TaskBo();
        taskBo.setTaskId(submitDto.getTaskId());
        taskBo.setProcInsId(submitDto.getFlowId());
        taskBo.setResult(submitDto.getResult());
        taskBo.setComment(submitDto.getComment());
        taskBo.setTransId(submitDto.getTransId());
        // 查询当前审批单
        ApprovalEntity approvalEntity = approvalRepository.queryByFlowId(submitDto.getFlowId());
        // 更新数据库
        taskBo.setProcessBusinessKey(approvalEntity.getId());
        // 完成即更改为"待验收"
        approvalEntity.setStatus(ApprovalStatusEnum.MKT_APPROVED.getCode());
        approvalRepository.updateApprovalStatus(approvalEntity);
        completeTask(taskBo, approvalEntity.getSubmitUser());

        updateDeepenDesignFilesRelation(submitDto.getFlowId());
    }

    private void completeTask(TaskBo taskBo, String designLeader) {
        Task task = manualTaskHandlerUtil.checkCompleteCondition(taskBo);
        // 获取当前处理人
        String currentUserId = taskBo.getTransId();
        identityService.setAuthenticatedUserId(currentUserId);
        // 根据审批结果执行业务逻辑
        handleApproval(task, taskBo);
        taskService.setAssignee(task.getId(), currentUserId);
        Map<String, Object> variables = new HashMap<>();
        // 将提交完成的用户存储在流程变量中，用于验收不通过时流转使用
        variables.put(ProcessConstants.PRODUCT_UPGRADE_USER, currentUserId);
        variables.put(ProcessConstants.DESIGN_LEADER, designLeader);
        // 根据"流程实例id"完成任务
        taskService.complete(taskBo.getTaskId(), variables);
    }

    private void handleApproval(Task task, TaskBo taskBo) {
        log.info("handleApproval task:{}", task);
        // 添加任务处理记录为"完成"
        taskService.addComment(task.getId(), task.getProcessInstanceId(),
                ProductUpgradeFlowCommentEnum.COMPLETE_PRODUCT_UPGRADE.getType(), taskBo.getComment());
        manualTaskHandlerUtil.createAndAddComment(task, taskBo);
    }

    private void updateDeepenDesignFilesRelation(String flowId) {
        String currentTime = DateTimeUtils.getCurrentTime();
        DeepenDesignFilesRelationInnerAddDto updateDto = new DeepenDesignFilesRelationInnerAddDto();
        updateDto.setFlowId(flowId);
        updateDto.setUpdateTime(currentTime);
        // 提资附件上传完成
        updateDto.setApprovalResult(ProcessConstants.STR_ONE);
        designInnerService.updateDeepenDesignFilesRelation(updateDto);
    }
}

/* Ended by AICoder, pid:v3986ref32s374414afc080ff1fe6a0248c8acff */