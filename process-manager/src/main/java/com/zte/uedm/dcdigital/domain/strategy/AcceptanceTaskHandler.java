/* Started by AICoder, pid:8cfe9i9a4ct514f1441308be9075b9251be11a02 */
package com.zte.uedm.dcdigital.domain.strategy;

import com.zte.uedm.dcdigital.common.bean.dto.LaunchBidDto;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.constant.TaskConstants;
import com.zte.uedm.dcdigital.domain.common.enums.ApprovalStatusEnum;
import com.zte.uedm.dcdigital.domain.common.enums.FlowCommentEnum;
import com.zte.uedm.dcdigital.domain.common.enums.MktAcceptTypeEnum;
import com.zte.uedm.dcdigital.domain.common.enums.ProcessEnum;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalEntity;
import com.zte.uedm.dcdigital.domain.model.process.TaskBo;
import com.zte.uedm.dcdigital.domain.repository.ApprovalRepository;
import com.zte.uedm.dcdigital.domain.service.ApprovalService;
import com.zte.uedm.dcdigital.domain.service.DcTaskService;
import com.zte.uedm.dcdigital.interfaces.web.dto.ApprovalSubmitDto;
import com.zte.uedm.dcdigital.sdk.project.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.flowable.engine.IdentityService;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 验收通过"市场投标"系列任务的处理策略。
 */
@Slf4j
@Service
public class AcceptanceTaskHandler implements ManualTaskHandlerStrategy {
    @Resource
    private ApprovalRepository approvalRepository;
    @Autowired
    private DcTaskService dcTaskService;
    @Autowired
    private ManualTaskHandlerUtil manualTaskHandlerUtil;
    @Resource
    private TaskService taskService;
    @Resource
    private ApprovalService approvalService;

    @Resource
    private IdentityService identityService;

    @Autowired
    @Qualifier("sdkProjectServiceImpl")
    private ProjectService projectService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handle(ApprovalSubmitDto submitDto) {
        log.info("AcceptanceTaskHandler handle submitDto:{}", submitDto);
        TaskBo taskBo = new TaskBo();
        taskBo.setTaskId(submitDto.getTaskId());
        taskBo.setProcInsId(submitDto.getFlowId());
        taskBo.setResult(submitDto.getResult());
        taskBo.setComment(submitDto.getComment());
        taskBo.setTransId(submitDto.getTransId());
        //查询当前审批单
        ApprovalEntity approvalEntity = approvalRepository.queryByFlowId(submitDto.getFlowId());
        //更新数据库
        taskBo.setProcessBusinessKey(approvalEntity.getId());
        //判断是否是本项目的所在类型(标书澄清、物料选型、文档编写)的最后一个任务，是就更新项目对应的阶段时间
        boolean result = getApprovalList(submitDto.getFlowId());
        if (result) {
            LaunchBidDto launchBidDto = new LaunchBidDto();
            launchBidDto.setId(approvalEntity.getProjectId());
            String currentTime = DateTimeUtils.getCurrentDate();
            launchBidDto.setConfigureManifestLockTime(currentTime);
            if (approvalEntity.getApprovalType().toString().equals(ProcessEnum.BID_CLARIFICATION.getType())) {
                updateClarifySubmissionTime(launchBidDto);
            } else if (approvalEntity.getApprovalType().toString().equals(ProcessEnum.MATERIAL_SELECTION.getType())) {
                updateConfigureManifestLockTime(launchBidDto);
            } else if (approvalEntity.getApprovalType().toString().equals(ProcessEnum.DOCUMENTATION.getType())) {
                updateBiddingDocumentsFinalizationTime(launchBidDto);
            }
        }
        //完成任务
        completeTask(taskBo);
    }

    private boolean getApprovalList(String flowId) {
        ApprovalEntity approvalEntity = approvalRepository.queryByFlowId(flowId);
        List<ApprovalObj> approvalEntities = approvalRepository.queryByApprovalLastInfo(approvalEntity);
        if (CollectionUtils.isNotEmpty(approvalEntities) && approvalEntities.size() > 1) {
            return false;
        } else if (CollectionUtils.isNotEmpty(approvalEntities) && approvalEntities.size() == 1) {
            return true;
        }
        return false;
    }

    private void completeTask(TaskBo taskBo) {
        Task task = manualTaskHandlerUtil.checkCompleteCondition(taskBo);
        // 获取当前处理人
        String userId = taskBo.getTransId();
        identityService.setAuthenticatedUserId(userId);
        // 根据审批结果执行业务逻辑
        handleApproval(task, taskBo);
        taskService.setAssignee(task.getId(), userId);
        Map<String, Object> variables = new HashMap<>();
        //设置"验收通过"的网关条件变量
        variables.put(ProcessConstants.PROCESS_APPROVAL, TaskConstants.TASK_APPROVAL);
        // 完成任务
        taskService.complete(taskBo.getTaskId(), variables);
    }

    private void handleApproval(Task task, TaskBo taskBo) {
        log.info("handleApproval task:{}", task);
        //添加任务处理记录为"验收通过"
        taskService.addComment(task.getId(), task.getProcessInstanceId(), FlowCommentEnum.ACCEPTANCE_ACCEPT.getType(), taskBo.getComment());
        //更新任务流程状态为"已验收"
        approvalService.updateApprovalStatus(taskBo.getProcInsId(), ApprovalStatusEnum.MKT_ACCEPTED.getCode());
        manualTaskHandlerUtil.createAndAddComment(task, taskBo);
    }

    @Override
    public MktAcceptTypeEnum getProcessType() {
        return MktAcceptTypeEnum.ACCEPTANCE;
    }


    private void updateConfigureManifestLockTime(LaunchBidDto launchBidDto) {
        //配置清单锁定时间
        log.info("update ConfigureManifestLockTime: {}", launchBidDto.getConfigureManifestLockTime());
        projectService.updateLaunchBidByProjectId(launchBidDto);
    }

    //更新澄清提交时间
    private void updateClarifySubmissionTime(LaunchBidDto launchBidDto) {
        //澄清提交时间
        log.info("update ClarifySubmissionTime: {}", launchBidDto.getClarifySubmissionTime());
        projectService.updateLaunchBidByProjectId(launchBidDto);
    }

    //更新招标文件定稿时间
    private void updateBiddingDocumentsFinalizationTime(LaunchBidDto launchBidDto) {
        //招标文件定稿时间
        log.info("update BiddingDocumentsFinalizationTime: {}", launchBidDto.getBiddingDocumentsFinalizationTime());
        projectService.updateLaunchBidByProjectId(launchBidDto);
    }
}
/* Ended by AICoder, pid:8cfe9i9a4ct514f1441308be9075b9251be11a02 */