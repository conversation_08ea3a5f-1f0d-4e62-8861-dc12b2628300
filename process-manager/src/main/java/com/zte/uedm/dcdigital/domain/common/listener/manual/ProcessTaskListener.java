package com.zte.uedm.dcdigital.domain.common.listener.manual;

import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;


/**
 * 处理手动下发"市场投标支持"系列任务 接收后 创建任务监听器
 * */
@Slf4j
@Component
public class ProcessTaskListener implements TaskListener {
    @Override
    public void notify(DelegateTask delegateTask) {
        String actualAssignee = (String) delegateTask.getVariable(ProcessConstants.MANUAL_USER);
        if (StringUtils.isNotBlank(actualAssignee)) {
            //将接受任务的用户存储在流程变量中，用于验收不通过时流转使用
            delegateTask.setVariable(ProcessConstants.MANUAL_USER, actualAssignee);
        }
    }
}
