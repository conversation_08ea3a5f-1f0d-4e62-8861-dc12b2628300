package com.zte.uedm.dcdigital.interfaces.web.vo;

/* Started by AICoder, pid:y26b1l9ee5c38c314ac60a2b80330a219473a531 */
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * ApprovalMaterialAll1Vo 类继承自 ApprovalMaterialVo，用于表示包含额外信息的物料审批视图对象。
 * 该类主要增加了成本费用和交期两个属性。
 */
@Getter
@Setter
@ToString
public class ApprovalMaterialAll1Vo extends ApprovalMaterialVo {

    /**
     * 成本费用，表示物料的成本价格。
     */
    private String cost;

    /**
     * 交期，表示物料的交付时间。
     */
    private String othCode;
}
/* Ended by AICoder, pid:y26b1l9ee5c38c314ac60a2b80330a219473a531 */