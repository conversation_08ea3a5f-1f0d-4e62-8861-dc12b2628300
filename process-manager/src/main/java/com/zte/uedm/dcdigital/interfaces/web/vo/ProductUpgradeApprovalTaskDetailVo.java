/* Started by AICoder, pid:sd3fak37486b4411458e095c70dd48502871be87 */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import com.zte.uedm.dcdigital.common.bean.document.FileInfoVo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ProductUpgradeApprovalTaskDetailVo {

    /**
     * 主键
     */
    private String id;

    /**
     * 标题名称
     */
    private String processTitleName;

    /**
     * 发起人
     */
    private String submitUser;

    /**
     * 提交时间
     */
    private String submitTime;

    /**
     * 审批名称
     */
    private String title;

    /**
     * 流程类型
     */
    private String flowType;

    /**
     * 审批状态,0-待审批，1-审批中，2-审批通过，3-审批不通过，4-已撤回,5-待处理，6-处理中，7-待验收，8-已验收，9-已终止
     */
    private Integer status;

    /**
     * 前端展示按钮组合逻辑
     */
    private List<ManualButton> buttonArray;

    /**
     * 附件列表
     * */
    private List<FileInfoVo> fileInfos;
}

/* Ended by AICoder, pid:sd3fak37486b4411458e095c70dd48502871be87 */