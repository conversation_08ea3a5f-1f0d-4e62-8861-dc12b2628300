package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class MktApprovalAssocInfoDto {
    /**
     * 主键
     */
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 方案SE ID
     */
    private String schemeSeId;

    /**
     * 产品SE ID
     */
    private String productSeId;

    /**
     * 产品小类ID
     */
    private String productCategoryId;

    /**
     * 任务处理人ID
     */
    private String taskHandlerId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 流程ID
     */
    private String flowId;
    /**
     * 地区
     */
    private String areaPath;

    /**
     * 任务分类(1:标书澄清、2：文档编写、3：材料选型、4:市场投标支持)
     * */
    private String mktBidTaskClass;
    /**
     * 创建时间
     */
    private String createTime;
}
