package com.zte.uedm.dcdigital.domain.service.impl;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.MktTaskConstants;
import com.zte.uedm.dcdigital.domain.repository.ApprovalCommentRepository;
import com.zte.uedm.dcdigital.domain.service.MktTaskHistoryFunctionService;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ApprovalCommentPo;
import com.zte.uedm.dcdigital.interfaces.web.vo.CommentVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.task.Comment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MktTaskHistoryFunctionServiceImpl implements MktTaskHistoryFunctionService {
    @Autowired
    private HistoryService historyService;

    @Autowired
    private ApprovalCommentRepository approvalCommentRepository;

    /**
     *  根据流程实例id查询流程处理的节点组合列表
     * */
    @Override
    public List<String> getActivityIds(String staticTaskId) {
        if (MktTaskConstants.REP_MAIN_TASK_ID.equals(staticTaskId)) {
            //1:查询被接受的主任务流程处理详情(被接受的主任务id->标书分解id)
            return Arrays.asList(MktTaskConstants.REP_MAIN_TASK_ID, MktTaskConstants.BREAKDOWN_TENDER_ID
            );
        } else if (MktTaskConstants.MATERIAL_SELECTION_ID.equals(staticTaskId)) {
            //2:查询物料选型流程处理详情(物料选型id->标书分解id)
            return Arrays.asList(MktTaskConstants.MATERIAL_SELECTION_ID, MktTaskConstants.BREAKDOWN_TENDER_ID
            );
        } else if (MktTaskConstants.BID_CLARIFICATION_ID.equals(staticTaskId)) {
            //3:查询标书澄清流程处理详情(标书澄清id->标书分解id)
            return Arrays.asList(MktTaskConstants.BID_CLARIFICATION_ID, MktTaskConstants.BREAKDOWN_TENDER_ID
            );
        } else if (MktTaskConstants.DOCUMENTATION_ID.equals(staticTaskId)) {
            //4:查询文档编写流程处理详情(文档编写id->标书分解id)
            return Arrays.asList(MktTaskConstants.DOCUMENTATION_ID, MktTaskConstants.BREAKDOWN_TENDER_ID
            );
        } else if (MktTaskConstants.MATERIAL_SELECTION_VERIFY_ID.equals(staticTaskId)) {
            //5:查询物料选型验证流程处理详情(物料选型验证id->物料选型id->标书分解id)
            return Arrays.asList(MktTaskConstants.MATERIAL_SELECTION_VERIFY_ID, MktTaskConstants.MATERIAL_SELECTION_ID, MktTaskConstants.
                    BREAKDOWN_TENDER_ID);
        } else if (MktTaskConstants.DOCUMENTATION_VERIFY_ID.equals(staticTaskId)) {
            //6:查询文档编写验证流程处理详情(文档编写验证id->文档编写id->标书分解id)
            return Arrays.asList(MktTaskConstants.DOCUMENTATION_VERIFY_ID, MktTaskConstants.DOCUMENTATION_ID, MktTaskConstants.BREAKDOWN_TENDER_ID);
        } else if (MktTaskConstants.BID_CLARIFICATION_VERIFY_ID.equals(staticTaskId)) {
            //7:查询标书澄清验证流程处理详情(标书澄清验证id->标书澄清id->标书分解id)
            return Arrays.asList(MktTaskConstants.BID_CLARIFICATION_VERIFY_ID, MktTaskConstants.BID_CLARIFICATION_ID, MktTaskConstants.BREAKDOWN_TENDER_ID);
        }
        log.error("staticTaskId is not found");
        throw new BusinessException(StatusCode.DATA_NOT_FOUND);
    }
    /**
     *  根据流程实例id与流程节点组合查询流程处理历史
     * */
    @Override
    public List<HistoricActivityInstance> getMktFlowDataHistory(String flowId, List<String> targetActivityIds) {
        //根据 流程id 和 节点id 查询历史处理详情
        List<HistoricActivityInstance> historicActivityInstances = new ArrayList<>();
        for (String activityId : targetActivityIds) {
            List<HistoricActivityInstance> instances = historyService
                    .createHistoricActivityInstanceQuery()
                    .processInstanceId(flowId)
                    .activityId(activityId) // 逐个查询
                    .orderByHistoricActivityInstanceStartTime().asc()
                    .list() ;
            //收集当前流程每个节点的历史处理详情
            historicActivityInstances.addAll(instances);
        }
        // 过滤掉 assignee(处理人) 为 null 的记录，并根据 startTime 从大到小排序
        List<HistoricActivityInstance> filteredAndSorted = historicActivityInstances
                .stream()
                .filter(instance -> instance.getAssignee() != null) // 过滤掉 assignee 为 null 的记录
                .sorted(Comparator.comparing(HistoricActivityInstance::getStartTime).reversed()) // 按照 startTime 从大到小排序
                .collect(Collectors.toList());

        return filteredAndSorted;
    }
    /**
     *  将流程处理历史转换为自定义的列表类
     * */
    @Override
    public List<CommentVo> getCommentList(List<HistoricActivityInstance> historicActivityInstances) {
        //转换为 CommentVo 列表
        List<CommentVo> commentList = new ArrayList<>();
        //收集taskId
        List<String> taskIds=historicActivityInstances.stream().map(HistoricActivityInstance::getTaskId).collect(Collectors.toList());
        List<ApprovalCommentPo> commentInfoByTaskIds = approvalCommentRepository.getCommentInfoByTaskIds(taskIds);
        Map<String, Integer> commentMap=new HashMap<>();
        if (CollectionUtils.isNotEmpty(commentInfoByTaskIds)) {
            //构造成taskId为key,result为value的map
            commentMap = commentInfoByTaskIds.stream().collect(Collectors.toMap(ApprovalCommentPo::getNodeId, ApprovalCommentPo::getResult, (key1, key2) -> key2));
            /* Ended by AICoder, pid:4502aj46e4b7df114dba09cfb093a806ace6582d */
        }
        //从approval_comment表中获取任务审批结果
        for (HistoricActivityInstance activityInstance : historicActivityInstances) {
            //只收集有处理人(即有审批人)的任务
            CommentVo comment = new CommentVo();
            comment.setId(activityInstance.getActivityId());
            comment.setUsername(activityInstance.getAssignee());
            //获取任务处理状态
            String taskId= activityInstance.getTaskId();
            comment.setResult(commentMap.get(taskId)==null?null:commentMap.get(taskId));
            comment.setCreateTime(DateTimeUtils.getStringTime(activityInstance.getStartTime()));
            commentList.add(comment);
        }
        return commentList;
    }
}
