/* Started by AICoder, pid:gb230q7cc5rb47814bde09048083e51117d04a46 */
package com.zte.uedm.dcdigital.application.approval.executor;

import com.zte.uedm.dcdigital.domain.model.approval.ManualApprovalProjectRequest;
import com.zte.uedm.dcdigital.domain.model.process.TaskBo;
import com.zte.uedm.dcdigital.interfaces.web.dto.ApprovalSubmitDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.ApprovalVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.MktApprovalDetailVo;

public interface ManualApprovalCommandService {
    /**
     * 创建'工程量清单' 手动下发评审单
     */
    void addManualApproval(ManualApprovalProjectRequest approvalDto);
    /**
     * 处理'市场投标支持'系列评审单
     */
    void acceptManualApproval(ApprovalSubmitDto submitDto);

    /**
     * 根据任务ID获取市场投标支持系列任务的详细信息。
     *
     * @param flowId 流程ID，不能为空。
     * @return 市场投标支持的详细信息对象。
     */
    MktApprovalDetailVo getMktApprovalDetail(String flowId);


    /**
     *
     * */
    ApprovalVo getApprovalById(String approvalId);


    /**
     * 取消"市场投标支持"流程任务
     * @param projectId 商机id
     * @param productCategoryId 产品小类id
     * */
    void cancelMktTask(String projectId, String productCategoryId);
}

/* Ended by AICoder, pid:gb230q7cc5rb47814bde09048083e51117d04a46 */