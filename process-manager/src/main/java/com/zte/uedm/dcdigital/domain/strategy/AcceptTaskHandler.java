/* Started by AICoder, pid:rcfe979a4cv514f1441308be9075b9251be11a02 */
package com.zte.uedm.dcdigital.domain.strategy;

import com.zte.uedm.dcdigital.common.bean.product.DemandManagementInnerDto;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementInnerVo;
import com.zte.uedm.dcdigital.common.bean.project.LaunchBiddingInnerVo;
import com.zte.uedm.dcdigital.common.bean.project.OpportunitySupportInnerDto;
import com.zte.uedm.dcdigital.common.bean.project.OpportunitySupportInnerVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.constant.TaskConstants;
import com.zte.uedm.dcdigital.domain.common.enums.ApprovalStatusEnum;
import com.zte.uedm.dcdigital.domain.common.enums.FlowCommentEnum;
import com.zte.uedm.dcdigital.domain.common.enums.MktAcceptTypeEnum;
import com.zte.uedm.dcdigital.domain.common.enums.ProcessEnum;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProcessStatusCode;
import com.zte.uedm.dcdigital.domain.model.approval.ApprovalObj;
import com.zte.uedm.dcdigital.domain.model.process.ApprovalEntity;
import com.zte.uedm.dcdigital.domain.model.process.TaskBo;
import com.zte.uedm.dcdigital.domain.repository.ApprovalRepository;
import com.zte.uedm.dcdigital.domain.service.ApprovalService;
import com.zte.uedm.dcdigital.domain.service.DcTaskService;
import com.zte.uedm.dcdigital.interfaces.web.dto.ApprovalSubmitDto;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import com.zte.uedm.dcdigital.sdk.project.service.ProjectService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 接受"市场投标"系列任务的处理策略。
 */
@Slf4j
@Service
public class AcceptTaskHandler implements ManualTaskHandlerStrategy {
    //需求选型清单默认状态
    private static final String DEFAULT_DEMAND_TYPE = "1";
    @Resource
    private ApprovalRepository approvalRepository;
    @Autowired
    private DcTaskService dcTaskService;
    @Resource
    private RuntimeService runtimeService;

    @Autowired
    private ManualTaskHandlerUtil manualTaskHandlerUtil;
    @Resource
    private TaskService taskService;
    @Resource
    private ApprovalService approvalService;

    @Resource
    private AuthService authService;

    @Resource
    private IdentityService identityService;

    @Resource
    private ProductService productService;
    @Resource
    private ProjectService projectService;
    @Override
    @Transactional
    public void handle(ApprovalSubmitDto submitDto) {
        log.info("AcceptTaskHandler handle submitDto: {}", submitDto);
        //查询当前审批单
        ApprovalEntity approvalEntity = approvalRepository.queryByFlowId(submitDto.getFlowId());
        log.info("AcceptTaskHandler handle approvalEntity: {}", approvalEntity);
        acceptTasks(approvalEntity, submitDto);
    }

    /**
     * 获取当前项目/商机下的当前任务产品小类的所有任务
     */
    private void acceptTasks(ApprovalEntity transApproval, ApprovalSubmitDto submitDto) {
        String currentUserId = authService.getUserId();
        List<ApprovalObj> approvalEntities = approvalRepository.queryByApprovalIngInfo(transApproval);
        if (CollectionUtils.isNotEmpty(approvalEntities)) {
            Set<String> processedFlowIds = new HashSet<>();
            //修改涉及审批单流程负责人
            for (ApprovalObj approvalObj : approvalEntities) {
                //取出流程创建时的方案经理
                String schemeSe = (String) runtimeService.getVariable(approvalObj.getFlowId(), ProcessConstants.SCHEME_SE);
                if (processedFlowIds.contains(approvalObj.getFlowId())) {
                    continue; // 已处理过该流程实例
                }
                processedFlowIds.add(approvalObj.getFlowId());
                ApprovalEntity approvalEntity = new ApprovalEntity();
                approvalEntity.setId(approvalObj.getId());
                //接收即更改为"处理中"
                approvalEntity.setStatus(ApprovalStatusEnum.MKT_IN_PROGRESS.getCode());
                //接收人设置为工程量清单任务处理人
                approvalEntity.setBillQuantityPerson(submitDto.getTransId());
                approvalRepository.updateApprovalStatus(approvalEntity);
                //根据流程实例id获取任务id
                List<Task> tasks = taskService.createTaskQuery().processInstanceId(approvalObj.getFlowId()).list();
                log.info("AcceptTaskHandler acceptTasks tasks: {}", tasks);
                if (CollectionUtils.isNotEmpty(tasks)) {
                    for (Task item : tasks) {
                        TaskBo taskBo = new TaskBo();
                        taskBo.setTaskId(item.getId());
                        taskBo.setProcInsId(approvalObj.getFlowId());
                        taskBo.setResult(submitDto.getResult());
                        taskBo.setComment(submitDto.getComment());
                        taskBo.setProcessBusinessKey(approvalEntity.getId());
                        Task task = manualTaskHandlerUtil.checkCompleteCondition(taskBo);
                        identityService.setAuthenticatedUserId(currentUserId);
                        //增加流程接收记录
                        handleApproval(task, taskBo);
                        if (item.getAssignee() == null) {
                            //认领任务
                            taskService.claim(item.getId(), currentUserId);
                        }
                        //设置"接收"的流程网关条件变量
                        Map<String, Object> variables = new HashMap<>();
                        variables.put(ProcessConstants.ACCEPT, TaskConstants.TASK_APPROVAL); // 示例变量
                        variables.put(ProcessConstants.MANUAL_USER, currentUserId);
                        taskService.complete(item.getId(), variables);
                        log.info("AcceptTaskHandler handle complete task: {}", taskBo);
                    }
                }
                //为接收用户赋予权限
                dcTaskService.addProjectPermission(submitDto.getTransId(), submitDto.getTransName(), approvalObj.getProjectId(), approvalObj.getResourceId());
                //获取方案经理变量
                String schemeSeId = (String) runtimeService.getVariable(submitDto.getFlowId(), TaskConstants.PROCESS_INITIATOR);
                //为接收用户添加处理人员信息
                dcTaskService.addMarketBidInfo(approvalObj.getProjectId(), approvalObj.getFlowId(), approvalObj.getResourceId(), schemeSeId, submitDto.getTransId(), submitDto.getTransId(), "");
                //接收任务时创建需求选型清单
                //只保存"物料选型"流程任务的需求选型清单
                addDemandInfo(approvalObj.getApprovalType().toString(), approvalObj.getBillQuantityId(), approvalObj.getProjectId(), approvalObj.getResourceId(), currentUserId,schemeSe);
                //接收任务时创建或更新商机-产品小类-市场任务支持SE
                manualTaskHandlerUtil.updateOrAddOpportunitySupport(approvalObj.getProjectId(), approvalObj.getResourceId(), submitDto.getTransId());
            }

        }
    }

    /* Started by AICoder, pid:f674au0401e3afa141cc0beea0d2f523ad557eda */
    /**
     * 添加需求需求选型清单
     *
     * @param type               流程类型
     * @param billQuantityId     工程量清单Id
     * @param projectId          项目id
     * @param productSubcategory 产品小类id
     * @param currentUserId 当前创建人
     * @param schemeSE      方案经理
     */
    private void addDemandInfo(String type, String billQuantityId, String projectId, String productSubcategory,String currentUserId, String schemeSE) {
        if (ProcessEnum.MATERIAL_SELECTION.getType().equals(type)) {
            DemandManagementInnerDto condition=new DemandManagementInnerDto();
            condition.setProjectId(projectId);
            condition.setProductCategoryId(productSubcategory);
            //condition.setBillQuantityId(billQuantityId);
            if (isExistDemandManagement(condition,currentUserId)){
                String currentTime=DateTimeUtils.getCurrentTime();
                // 满足"物料选型"就调用rpc增加需求记录
                DemandManagementInnerDto demandManagementInnerDto = new DemandManagementInnerDto();
                demandManagementInnerDto.setProjectId(projectId);
                demandManagementInnerDto.setProductCategoryId(productSubcategory);
                demandManagementInnerDto.setBillQuantityId(billQuantityId);
                demandManagementInnerDto.setDemandType(DEFAULT_DEMAND_TYPE);
                demandManagementInnerDto.setCreateBy(currentUserId);
                demandManagementInnerDto.setUpdateBy(currentUserId);
                demandManagementInnerDto.setCreateUser(schemeSE);
                demandManagementInnerDto.setProcessor(currentUserId);
                demandManagementInnerDto.setUpdateTime(currentTime);
                demandManagementInnerDto.setCreateTime(currentTime);
                //工程量清单条目发生变化时间
                demandManagementInnerDto.setBillQuantitieTime(currentTime);
                //RPC查询商机澄清时间
                LaunchBiddingInnerVo innerVo = projectService.queryLaunchBidById(projectId);
                if (innerVo == null|| StringUtils.isEmpty(innerVo.getClarifySubmissionTime())) {
                    log.error("createMarketBidApproval:addDemandInfo:innerVo is null or innerVo.getClarifySubmissionTime() is null");
                    throw new BusinessException(ProcessStatusCode.TIME_ERROR);
                }else {
                    //获取配置清单锁定时间
                    demandManagementInnerDto.setExpTimeComplet(innerVo.getConfigureManifestLockTime());
                }

                log.info("createMarketBidApproval:addDemandInfo:{}", demandManagementInnerDto);
                productService.addDemandManagementInfo(demandManagementInnerDto);
            }

        }
    }
    /* Started by AICoder, pid:6f220od7b9t4066148570b3e207aad1ade390f01 */
    /**
     * @param demandManagementInnerDto:项目id，产品小类id
     */
    private boolean isExistDemandManagement(DemandManagementInnerDto demandManagementInnerDto, String currentUserId) {
        //调用RPC判断当前商机+产品小类是否已存在需求清单中
        DemandManagementInnerVo innerVo = productService.getDemandManagement(demandManagementInnerDto);
        if (innerVo == null || innerVo.getId() == null) {
            return true;
        } else {
            //存在就更新需求状态为"分析中"
            demandManagementInnerDto.setDemandType(DEFAULT_DEMAND_TYPE);
            demandManagementInnerDto.setUpdateBy(currentUserId);
            demandManagementInnerDto.setId(innerVo.getId());
            String currentTime = DateTimeUtils.getCurrentTime();
            demandManagementInnerDto.setUpdateTime(currentTime);
            productService.updateDemandManagementInfo(demandManagementInnerDto);
            return false;
        }
    }

    /* Ended by AICoder, pid:6f220od7b9t4066148570b3e207aad1ade390f01 */

    private void handleApproval(Task task, TaskBo taskBo) {
        log.info("handleApproval task:{}", task);
        //添加任务处理记录为"接收"
        taskService.addComment(task.getId(), task.getProcessInstanceId(), FlowCommentEnum.MANUAL_ACCEPT.getType(), taskBo.getComment());
        manualTaskHandlerUtil.createAndAddComment(task, taskBo);
    }

    @Override
    public MktAcceptTypeEnum getProcessType() {
        return MktAcceptTypeEnum.ACCEPT;
    }
}
/* Ended by AICoder, pid:rcfe979a4cv514f1441308be9075b9251be11a02 */