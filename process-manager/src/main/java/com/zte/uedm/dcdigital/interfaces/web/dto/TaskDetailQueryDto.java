/* Started by AICoder, pid:913492f846ge60014bc70b9010b0db30aed25e3e */
package com.zte.uedm.dcdigital.interfaces.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * 任务详情查询DTO，用于根据项目ID查询任务详情。
 */
@Getter
@Setter
@ToString
public class TaskDetailQueryDto {

    /**
     * 项目ID。此字段是必需的，不能为空。
     */
    @NotBlank(message = "projectId 不能为空")
    private String projectId;

    /**
     * 分页的页码，默认为1。
     */
    private Integer pageNum = 1;

    /**
     * 每页的大小，默认为10。
     */
    private Integer pageSize = 10;
}
/* Ended by AICoder, pid:913492f846ge60014bc70b9010b0db30aed25e3e */