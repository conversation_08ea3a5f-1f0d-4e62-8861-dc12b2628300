/* Started by AICoder, pid:de019m51fd2729814b260a511008b65b5da1baa0 */
package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * 手动评论的值对象 (Value Object)。
 * 该类用于表示手动评论的相关信息，包括评论ID、用户名、创建时间、结果、消息和处理结果。
 */
@Getter
@Setter
@AllArgsConstructor
public class ManualCommentVo {

    /**
     * 评论的唯一标识符。
     */
    private String id;

    /**
     * 发布评论的用户名。
     */
    private String username;

    /**
     * 评论的创建时间。
     */
    private String createTime;

    /**
     * 评论的结果，通常是一个整数值，表示某种状态或结果。
     */
    private Integer result;

    /**
     * 评论的消息内容。
     */
    private String message;

    /**
     * 处理结果，可能包含对评论的进一步处理信息。
     */
    private String processingResult;

    /**
     * 无参构造函数。
     */
    public ManualCommentVo() {
    }
}

/* Ended by AICoder, pid:de019m51fd2729814b260a511008b65b5da1baa0 */