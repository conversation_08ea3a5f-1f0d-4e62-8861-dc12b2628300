package com.zte.uedm.dcdigital.application.approval.executor.impl;

import com.zte.uedm.dcdigital.application.approval.executor.ApprovalCommandService;
import com.zte.uedm.dcdigital.common.bean.process.NewApprovalDto;
import com.zte.uedm.dcdigital.domain.service.ApprovalService;
import com.zte.uedm.dcdigital.interfaces.web.dto.ApprovalSubmitDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ApprovalCommandServiceImpl implements ApprovalCommandService {

    @Autowired
    private ApprovalService approvalService;

    @Override
    public String createApproval(NewApprovalDto newApprovalDto) {
        return approvalService.createApproval(newApprovalDto);
    }

    @Override
    public void submitApproval(ApprovalSubmitDto submitDto) {
        approvalService.submitApproval(submitDto);
    }

    @Override
    public void withdrawApproval(String flowId) {
        approvalService.withdrawApproval(flowId);
    }
}
