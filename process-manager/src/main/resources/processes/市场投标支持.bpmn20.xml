<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2">
  <process id="MarketBidSupport" name="市场投标支持" isExecutable="true">
    <documentation>市场投标支持</documentation>
    <startEvent id="startEvent1" flowable:formFieldValidation="true"/>
    <userTask id="sid-B08CA7D5-E32A-48AC-8351-C28E263554C1" name="市场投标支持" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.event.MktCreateAddCandidateGroupListener"/>
      </extensionElements>
    </userTask>
    <endEvent id="sid-890A07E7-8218-49EE-BB00-1BB074199852"/>
    <parallelGateway id="sid-6AD97405-1698-4A16-BC54-FB2A211AD06A"/>
    <sequenceFlow id="sid-2BA30737-DEA3-4D1C-9AB5-DBC19A6942C0" sourceRef="sid-B08CA7D5-E32A-48AC-8351-C28E263554C1" targetRef="sid-6AD97405-1698-4A16-BC54-FB2A211AD06A"/>
    <userTask id="sid-A428E5F8-2B5A-437A-9F09-201382A49772" name="物料选型" flowable:assignee="${materiel_candidate}" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.event.MktMaterielCreateListener"/>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
          <![CDATA[ false ]]>
        </modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-65571342-7185-4FBA-B817-47526C512BFE" name="标书澄清" flowable:assignee="${tender_candidate}" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.event.MktTenderCreateListener"/>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
          <![CDATA[ false ]]>
        </modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-71641091-3062-4EFB-A0AB-218BF5AE907C" sourceRef="sid-6AD97405-1698-4A16-BC54-FB2A211AD06A" targetRef="sid-65571342-7185-4FBA-B817-47526C512BFE"/>
    <userTask id="sid-E568363F-04EA-4304-A202-B60A6CAE6A36" name="文档编写" flowable:assignee="${document_candidate}" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.event.MktDocumentCreateListener"/>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
          <![CDATA[ false ]]>
        </modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <parallelGateway id="sid-332547CA-E0C1-483D-85DF-DC5012A2DBF9"/>
    <userTask id="sid-B6BA3F6F-75CE-4170-B3B5-543E0D2D6A0C" name="标书澄清验证" flowable:assignee="${verifier}" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.event.MktTenderCompleteListener"/>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
          <![CDATA[ false ]]>
        </modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-46CB463C-F895-41FF-8ACA-1A9C5C5AE0A2" name="物料选型验证" flowable:assignee="${verifier}" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.event.MktMaterielCompleteListener"/>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
          <![CDATA[ false ]]>
        </modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sid-4F4A6D37-AC42-4252-B3CF-1E420654C38C" name="文档编写验证" flowable:assignee="${verifier}" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.event.MktDocumentCompleteListener"/>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
          <![CDATA[ false ]]>
        </modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-43ABBBAC-5D78-4184-8DC6-050A30BD64CF" sourceRef="sid-6AD97405-1698-4A16-BC54-FB2A211AD06A" targetRef="sid-A428E5F8-2B5A-437A-9F09-201382A49772"/>
    <sequenceFlow id="sid-A0B27CDF-F24B-4654-8C3B-92A9837C1A51" sourceRef="sid-6AD97405-1698-4A16-BC54-FB2A211AD06A" targetRef="sid-E568363F-04EA-4304-A202-B60A6CAE6A36"/>
    <userTask id="sid-6D28EC1F-7954-4F7C-8C8E-7FACAB0D3F54" name="标书分解" flowable:assignee="${initiator}" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
          <![CDATA[ false ]]>
        </modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-59DBBF57-2B3F-465F-9804-742097E64786" sourceRef="sid-6D28EC1F-7954-4F7C-8C8E-7FACAB0D3F54" targetRef="sid-B08CA7D5-E32A-48AC-8351-C28E263554C1"/>
    <sequenceFlow id="sid-38E9B0FE-30E7-4BB7-AB7A-F80FF0C70535" sourceRef="startEvent1" targetRef="sid-6D28EC1F-7954-4F7C-8C8E-7FACAB0D3F54"/>
    <sequenceFlow id="sid-9018BDD2-5EBA-4B2D-BDDE-06232239FA6D" name="结束" sourceRef="sid-332547CA-E0C1-483D-85DF-DC5012A2DBF9" targetRef="sid-890A07E7-8218-49EE-BB00-1BB074199852"/>
    <sequenceFlow id="sid-A68A374E-A931-4EAD-A7C3-1E7C9644FD35" name="完成" sourceRef="sid-65571342-7185-4FBA-B817-47526C512BFE" targetRef="sid-B6BA3F6F-75CE-4170-B3B5-543E0D2D6A0C"/>
    <sequenceFlow id="sid-8F873E56-BF2F-430B-96EE-3FDFA40B393B" name="完成" sourceRef="sid-A428E5F8-2B5A-437A-9F09-201382A49772" targetRef="sid-46CB463C-F895-41FF-8ACA-1A9C5C5AE0A2"/>
    <sequenceFlow id="sid-0F207C9F-098B-45B8-9E1B-60B23FAF96C3" name="完成" sourceRef="sid-E568363F-04EA-4304-A202-B60A6CAE6A36" targetRef="sid-4F4A6D37-AC42-4252-B3CF-1E420654C38C"/>
    <exclusiveGateway id="sid-066CF9BF-9A04-4CE1-81F5-F516D1AFE6FB"/>
    <exclusiveGateway id="sid-568848D0-C051-44C3-B2DC-E3465BB8CE3A"/>
    <exclusiveGateway id="sid-6760B50D-E75C-46FD-A457-4ECBD88A41BD"/>
    <sequenceFlow id="sid-43D5B14B-6F19-4828-8512-33AA1BCD41A1" sourceRef="sid-46CB463C-F895-41FF-8ACA-1A9C5C5AE0A2" targetRef="sid-568848D0-C051-44C3-B2DC-E3465BB8CE3A"/>
    <sequenceFlow id="sid-C7603FA6-0D45-45BB-A9DD-957E3E5A2647" name="、" sourceRef="sid-4F4A6D37-AC42-4252-B3CF-1E420654C38C" targetRef="sid-6760B50D-E75C-46FD-A457-4ECBD88A41BD"/>
    <userTask id="sid-59F19EE8-CDA5-46DC-A19F-7717040EFFAF" name="市场投标支持" flowable:assignee="${candidate}" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:taskListener event="create" class="com.zte.uedm.dcdigital.domain.common.event.MktCreateVirtualMasterTaskListener"/>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">
          <![CDATA[ false ]]>
        </modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-0DCA504E-0949-483D-B708-EF575E8B69D7" sourceRef="sid-6AD97405-1698-4A16-BC54-FB2A211AD06A" targetRef="sid-59F19EE8-CDA5-46DC-A19F-7717040EFFAF"/>
    <sequenceFlow id="sid-42E1985F-3917-405C-959F-9144688ECA34" sourceRef="sid-59F19EE8-CDA5-46DC-A19F-7717040EFFAF" targetRef="sid-332547CA-E0C1-483D-85DF-DC5012A2DBF9"/>
    <sequenceFlow id="sid-E886FBCA-3EF1-4972-8E70-5EB92A54B621" sourceRef="sid-B6BA3F6F-75CE-4170-B3B5-543E0D2D6A0C" targetRef="sid-066CF9BF-9A04-4CE1-81F5-F516D1AFE6FB"/>
    <sequenceFlow id="sid-30DD5D5F-48E4-4AF5-A14A-7B6D5ECED710" name="验证通过" sourceRef="sid-6760B50D-E75C-46FD-A457-4ECBD88A41BD" targetRef="sid-332547CA-E0C1-483D-85DF-DC5012A2DBF9">
      <extensionElements>
        <flowable:executionListener event="start" class="com.zte.uedm.dcdigital.domain.common.event.VerificationTaskListener"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[ ${approvalResult==1} ]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-7A790D28-CF41-4302-AA8A-DEAB9C111868" name="验证通过" sourceRef="sid-066CF9BF-9A04-4CE1-81F5-F516D1AFE6FB" targetRef="sid-332547CA-E0C1-483D-85DF-DC5012A2DBF9">
      <extensionElements>
        <flowable:executionListener event="start" class="com.zte.uedm.dcdigital.domain.common.event.VerificationTaskListener"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[ ${approvalResult==1} ]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-A073A467-66C0-4EA7-8FEA-B1FB60D50D70" name="验证不通过" sourceRef="sid-066CF9BF-9A04-4CE1-81F5-F516D1AFE6FB" targetRef="sid-65571342-7185-4FBA-B817-47526C512BFE">
      <extensionElements>
        <flowable:executionListener event="start" class="com.zte.uedm.dcdigital.domain.common.event.RejectTaskListener"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[ ${approvalResult==0} ]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-CAAAD52A-9B2B-4BD5-8240-A431B9E246AA" name="验证不通过" sourceRef="sid-568848D0-C051-44C3-B2DC-E3465BB8CE3A" targetRef="sid-A428E5F8-2B5A-437A-9F09-201382A49772">
      <extensionElements>
        <flowable:executionListener event="start" class="com.zte.uedm.dcdigital.domain.common.event.RejectTaskListener"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[ ${approvalResult==0} ]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-C8F50371-AF9A-49A0-B6FA-C4E692BF1F2E" name="验证不通过" sourceRef="sid-6760B50D-E75C-46FD-A457-4ECBD88A41BD" targetRef="sid-E568363F-04EA-4304-A202-B60A6CAE6A36">
      <extensionElements>
        <flowable:executionListener event="start" class="com.zte.uedm.dcdigital.domain.common.event.RejectTaskListener"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[ ${approvalResult==0} ]]>
      </conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-C7E61921-76B2-49C1-B99F-78F882E8B021" name="验证通过" sourceRef="sid-568848D0-C051-44C3-B2DC-E3465BB8CE3A" targetRef="sid-332547CA-E0C1-483D-85DF-DC5012A2DBF9">
      <extensionElements>
        <flowable:executionListener event="start" class="com.zte.uedm.dcdigital.domain.common.event.VerificationTaskListener"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression">
        <![CDATA[ ${approvalResult==1} ]]>
      </conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_MarketBidSupport">
    <bpmndi:BPMNPlane bpmnElement="MarketBidSupport" id="BPMNPlane_MarketBidSupport">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="30.0" y="280.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B08CA7D5-E32A-48AC-8351-C28E263554C1" id="BPMNShape_sid-B08CA7D5-E32A-48AC-8351-C28E263554C1">
        <omgdc:Bounds height="80.0" width="100.0" x="270.0" y="255.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-890A07E7-8218-49EE-BB00-1BB074199852" id="BPMNShape_sid-890A07E7-8218-49EE-BB00-1BB074199852">
        <omgdc:Bounds height="28.0" width="28.0" x="1170.0" y="236.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6AD97405-1698-4A16-BC54-FB2A211AD06A" id="BPMNShape_sid-6AD97405-1698-4A16-BC54-FB2A211AD06A">
        <omgdc:Bounds height="40.0" width="40.0" x="420.0" y="275.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-A428E5F8-2B5A-437A-9F09-201382A49772" id="BPMNShape_sid-A428E5F8-2B5A-437A-9F09-201382A49772">
        <omgdc:Bounds height="80.0" width="100.0" x="540.0" y="255.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-65571342-7185-4FBA-B817-47526C512BFE" id="BPMNShape_sid-65571342-7185-4FBA-B817-47526C512BFE">
        <omgdc:Bounds height="80.0" width="100.0" x="540.0" y="130.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-E568363F-04EA-4304-A202-B60A6CAE6A36" id="BPMNShape_sid-E568363F-04EA-4304-A202-B60A6CAE6A36">
        <omgdc:Bounds height="80.0" width="100.0" x="540.0" y="465.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-332547CA-E0C1-483D-85DF-DC5012A2DBF9" id="BPMNShape_sid-332547CA-E0C1-483D-85DF-DC5012A2DBF9">
        <omgdc:Bounds height="40.0" width="40.0" x="1080.0" y="230.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B6BA3F6F-75CE-4170-B3B5-543E0D2D6A0C" id="BPMNShape_sid-B6BA3F6F-75CE-4170-B3B5-543E0D2D6A0C">
        <omgdc:Bounds height="80.0" width="100.0" x="765.0" y="130.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-46CB463C-F895-41FF-8ACA-1A9C5C5AE0A2" id="BPMNShape_sid-46CB463C-F895-41FF-8ACA-1A9C5C5AE0A2">
        <omgdc:Bounds height="80.0" width="100.0" x="765.0" y="255.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-4F4A6D37-AC42-4252-B3CF-1E420654C38C" id="BPMNShape_sid-4F4A6D37-AC42-4252-B3CF-1E420654C38C">
        <omgdc:Bounds height="80.0" width="100.0" x="780.0" y="465.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6D28EC1F-7954-4F7C-8C8E-7FACAB0D3F54" id="BPMNShape_sid-6D28EC1F-7954-4F7C-8C8E-7FACAB0D3F54">
        <omgdc:Bounds height="80.0" width="100.0" x="105.0" y="255.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-066CF9BF-9A04-4CE1-81F5-F516D1AFE6FB" id="BPMNShape_sid-066CF9BF-9A04-4CE1-81F5-F516D1AFE6FB">
        <omgdc:Bounds height="40.0" width="40.0" x="960.0" y="150.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-568848D0-C051-44C3-B2DC-E3465BB8CE3A" id="BPMNShape_sid-568848D0-C051-44C3-B2DC-E3465BB8CE3A">
        <omgdc:Bounds height="40.0" width="40.0" x="962.0" y="275.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6760B50D-E75C-46FD-A457-4ECBD88A41BD" id="BPMNShape_sid-6760B50D-E75C-46FD-A457-4ECBD88A41BD">
        <omgdc:Bounds height="40.0" width="40.0" x="962.0" y="485.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-59F19EE8-CDA5-46DC-A19F-7717040EFFAF" id="BPMNShape_sid-59F19EE8-CDA5-46DC-A19F-7717040EFFAF">
        <omgdc:Bounds height="80.0" width="100.0" x="540.0" y="0.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-43ABBBAC-5D78-4184-8DC6-050A30BD64CF" id="BPMNEdge_sid-43ABBBAC-5D78-4184-8DC6-050A30BD64CF" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="459.9433544303797" y="295.0"/>
        <omgdi:waypoint x="539.9999999999363" y="295.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C7603FA6-0D45-45BB-A9DD-957E3E5A2647" id="BPMNEdge_sid-C7603FA6-0D45-45BB-A9DD-957E3E5A2647" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="879.9499999999758" y="505.0"/>
        <omgdi:waypoint x="962.0" y="505.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0F207C9F-098B-45B8-9E1B-60B23FAF96C3" id="BPMNEdge_sid-0F207C9F-098B-45B8-9E1B-60B23FAF96C3" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="639.9499999994019" y="505.0"/>
        <omgdi:waypoint x="780.0" y="505.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-59DBBF57-2B3F-465F-9804-742097E64786" id="BPMNEdge_sid-59DBBF57-2B3F-465F-9804-742097E64786" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="204.9499999998897" y="295.0"/>
        <omgdi:waypoint x="269.99999999998465" y="295.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8F873E56-BF2F-430B-96EE-3FDFA40B393B" id="BPMNEdge_sid-8F873E56-BF2F-430B-96EE-3FDFA40B393B" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="639.9499999999999" y="295.0"/>
        <omgdi:waypoint x="765.0" y="295.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A0B27CDF-F24B-4654-8C3B-92A9837C1A51" id="BPMNEdge_sid-A0B27CDF-F24B-4654-8C3B-92A9837C1A51" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="440.0" y="314.9452522608281"/>
        <omgdi:waypoint x="440.0" y="505.0"/>
        <omgdi:waypoint x="539.9999999999363" y="505.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7A790D28-CF41-4302-AA8A-DEAB9C111868" id="BPMNEdge_sid-7A790D28-CF41-4302-AA8A-DEAB9C111868" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="991.9690029985007" y="177.97999999999996"/>
        <omgdi:waypoint x="1087.991004497751" y="242.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A073A467-66C0-4EA7-8FEA-B1FB60D50D70" id="BPMNEdge_sid-A073A467-66C0-4EA7-8FEA-B1FB60D50D70" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="980.0" y="189.93221925133693"/>
        <omgdi:waypoint x="980.0" y="226.0"/>
        <omgdi:waypoint x="755.0" y="223.0"/>
        <omgdi:waypoint x="639.9499999999999" y="186.04454545454544"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-42E1985F-3917-405C-959F-9144688ECA34" id="BPMNEdge_sid-42E1985F-3917-405C-959F-9144688ECA34" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="639.9499999999999" y="40.0"/>
        <omgdi:waypoint x="1100.0" y="40.0"/>
        <omgdi:waypoint x="1100.0" y="230.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0DCA504E-0949-483D-B708-EF575E8B69D7" id="BPMNEdge_sid-0DCA504E-0949-483D-B708-EF575E8B69D7" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="440.0" y="275.0"/>
        <omgdi:waypoint x="440.0" y="40.0"/>
        <omgdi:waypoint x="540.0" y="40.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-38E9B0FE-30E7-4BB7-AB7A-F80FF0C70535" id="BPMNEdge_sid-38E9B0FE-30E7-4BB7-AB7A-F80FF0C70535" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="59.9499984899576" y="295.0"/>
        <omgdi:waypoint x="105.0" y="295.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-30DD5D5F-48E4-4AF5-A14A-7B6D5ECED710" id="BPMNEdge_sid-30DD5D5F-48E4-4AF5-A14A-7B6D5ECED710" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="988.3112600536193" y="491.3270777479892"/>
        <omgdi:waypoint x="1093.6729222520107" y="263.63873994638067"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-43D5B14B-6F19-4828-8512-33AA1BCD41A1" id="BPMNEdge_sid-43D5B14B-6F19-4828-8512-33AA1BCD41A1" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="864.9499999999143" y="295.0"/>
        <omgdi:waypoint x="962.0" y="295.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-E886FBCA-3EF1-4972-8E70-5EB92A54B621" id="BPMNEdge_sid-E886FBCA-3EF1-4972-8E70-5EB92A54B621" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="864.9499999998898" y="170.0"/>
        <omgdi:waypoint x="960.0" y="170.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C7E61921-76B2-49C1-B99F-78F882E8B021" id="BPMNEdge_sid-C7E61921-76B2-49C1-B99F-78F882E8B021" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="996.4423312883434" y="289.47852760736197"/>
        <omgdi:waypoint x="1085.521472392638" y="255.5076687116564"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A68A374E-A931-4EAD-A7C3-1E7C9644FD35" id="BPMNEdge_sid-A68A374E-A931-4EAD-A7C3-1E7C9644FD35" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="639.9499999998377" y="170.0"/>
        <omgdi:waypoint x="765.0" y="170.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-9018BDD2-5EBA-4B2D-BDDE-06232239FA6D" id="BPMNEdge_sid-9018BDD2-5EBA-4B2D-BDDE-06232239FA6D" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="1119.9381391200868" y="250.0"/>
        <omgdi:waypoint x="1170.0" y="250.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2BA30737-DEA3-4D1C-9AB5-DBC19A6942C0" id="BPMNEdge_sid-2BA30737-DEA3-4D1C-9AB5-DBC19A6942C0" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="369.9499999999756" y="295.0"/>
        <omgdi:waypoint x="420.0" y="295.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-71641091-3062-4EFB-A0AB-218BF5AE907C" id="BPMNEdge_sid-71641091-3062-4EFB-A0AB-218BF5AE907C" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="440.0" y="275.0"/>
        <omgdi:waypoint x="440.0" y="170.0"/>
        <omgdi:waypoint x="540.0" y="170.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-CAAAD52A-9B2B-4BD5-8240-A431B9E246AA" id="BPMNEdge_sid-CAAAD52A-9B2B-4BD5-8240-A431B9E246AA" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="979.4257425742574" y="312.3821782178218"/>
        <omgdi:waypoint x="969.0" y="383.0"/>
        <omgdi:waypoint x="639.9499999999998" y="306.597889182058"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C8F50371-AF9A-49A0-B6FA-C4E692BF1F2E" id="BPMNEdge_sid-C8F50371-AF9A-49A0-B6FA-C4E692BF1F2E" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="982.0" y="485.0"/>
        <omgdi:waypoint x="982.0" y="400.0"/>
        <omgdi:waypoint x="596.0" y="400.0"/>
        <omgdi:waypoint x="592.2828571428571" y="465.0"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>