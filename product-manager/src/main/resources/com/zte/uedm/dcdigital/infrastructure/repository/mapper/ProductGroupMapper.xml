<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductGroupMapper">

    <select id="selectByNameAndProductCategoryId"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo">
        select
            id,name,pdm_model_spec, product_category_id,parent_id,path_name,path_id,group_level,create_time,update_time,create_by,update_by
        from
            product_group
        where
            name = #{name} and product_category_id = #{productCategoryId}
    </select>
    <select id="selectByParentId"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo">
        select
            id,name,pdm_model_spec, product_category_id,parent_id,path_name,path_id,group_level,create_time,update_time,create_by,update_by
        from
            product_group
        where
            parent_id = #{id}
    </select>
    <select id="queryProductGroups"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo">
        select
        id,name,pdm_model_spec, product_category_id,parent_id,path_name,path_id,group_level,create_time,update_time,create_by,update_by
        from
        product_group
        <where>
            <if test="queryDto.groupName != null and queryDto.groupName != ''">
                and LOWER(name) like concat('%',LOWER(#{queryDto.groupName}),'%')
            </if>
            <if test="queryDto.productCategoryId != null and queryDto.productCategoryId != ''">
                and product_category_id = #{queryDto.productCategoryId}
            </if>
        </where>
        order by name asc
    </select>
    <select id="selectProductGroupAndMaterialByGroupId"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo">
        select
            pg.*
        from
         product_group pg
        right join material m on pg.id = m.group_id
        left join material_temporary mt on pg.id = mt.group_id
        where pg.id = #{id}
    </select>
    <select id="selectMaterialApproveByGroupId"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo">
        select
            pg.*
        from
            product_group pg
        right join material m on pg.id = m.group_id
        where pg.id = #{id} and (m.material_status  = '2' or m.material_status = '5' or m.material_status = '6')
        group by pg.id
    </select>
    <select id="selectNodeAllChildNode"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo">
        WITH RECURSIVE
            ancestor_group AS (
                SELECT id, name, pdm_model_spec, product_category_id, parent_id, path_name, path_id, group_level, create_time, update_time, create_by, update_by
                FROM product_group
                WHERE id = #{id}
                UNION ALL
                SELECT t.id, t.name, t.pdm_model_spec, t.product_category_id, t.parent_id, t.path_name, t.path_id, t.group_level, t.create_time, t.update_time, t.create_by, t.update_by
                FROM product_group t
                         INNER JOIN ancestor_group a ON t.id = a.parent_id
            ),
            child_group AS (
                SELECT id,name,pdm_model_spec, product_category_id,parent_id,path_name,path_id,group_level,create_time,update_time,create_by,update_by
                FROM product_group
                WHERE id = #{id}
                UNION ALL
                SELECT t.id,t.name,t.pdm_model_spec, t.product_category_id,t.parent_id,t.path_name,t.path_id,t.group_level,t.create_time,t.update_time,t.create_by,t.update_by
                FROM product_group t INNER JOIN child_group c ON t.parent_id = c.id
            )
        SELECT *
        FROM (
                 SELECT * FROM ancestor_group
                 UNION
                 SELECT * FROM child_group
             ) combined_groups
        ORDER BY group_level
    </select>

    <select id="selectGroupIdByProductCategoryId" resultType="java.lang.String">
        select id from product_group where product_category_id = #{categoryId}
    </select>
    <select id="selectAllChildNodeId" resultType="java.lang.String">
        WITH RECURSIVE subnodes AS (
        SELECT id, parent_id
        FROM product_group
        WHERE id IN
        <foreach item="id" index="index" collection="groupIds"
                 open="(" separator="," close=")">
            #{id}
        </foreach>

        UNION ALL

        SELECT A.id, A.parent_id
        FROM product_group A
        JOIN subnodes ON A.parent_id = subnodes.id
        )
        SELECT id
        FROM subnodes
        ORDER BY id
    </select>
    <select id="queryingLeafNodeGroups" resultType="java.lang.String">
        WITH RECURSIVE CategoryTree AS (
        -- 初始选择：从给定的id开始，根据node_type来确定其是产品线、产品大类还是产品小类
        SELECT
        id,
        parent_id,
        node_type,
        1 AS level -- 记录层次级别
        FROM product_category
        WHERE id = #{categoryId} -- 传入的参数为产品线/产品大类/产品小类id

        UNION ALL

        -- 递归部分：寻找当前类别下的所有子类别
        SELECT
        pc.id,
        pc.parent_id,
        pc.node_type,
        ct.level + 1 AS level
        FROM product_category pc
        INNER JOIN CategoryTree ct ON pc.parent_id = ct.id
        ),
        ProductCategoryIds AS (
        -- 获取所有需要查询的产品小类id (node_type = 3)
        SELECT id FROM CategoryTree WHERE node_type = 3
        ),
        GroupTree AS (
        -- 从所有产品小类id中找到所有直接分组
        SELECT
        id,
        name,
        parent_id
        FROM product_group
        WHERE product_category_id IN (SELECT id FROM ProductCategoryIds)

        UNION ALL

        -- 递归地找到当前分组的所有直接子分组
        SELECT
        pg.id,
        pg.name,
        pg.parent_id
        FROM product_group pg
        INNER JOIN GroupTree gt ON pg.parent_id = gt.id
        ),
        LeafGroups AS (
        -- 筛选出没有子分组的叶子节点
        SELECT
        gt.*
        FROM GroupTree gt
        LEFT JOIN product_group pg ON gt.id = pg.parent_id
        WHERE pg.id IS NULL
        )
        -- 最终选择：返回叶子分组信息
        select DISTINCT * FROM LeafGroups
    </select>

    <select id="queryingLeafAllNodeGroups" resultType="java.lang.String">
        WITH RECURSIVE subnodes AS (
        -- 初始选择：从所有没有父节点的组开始，或者根据你的需要调整为所有节点
        SELECT id, parent_id
        FROM product_group
        WHERE parent_id IS NULL  -- 修改点1：这里改为选择所有顶级节点

        UNION ALL

        -- 递归部分：查找所有后代节点
        SELECT pg.id, pg.parent_id
        FROM product_group pg
        INNER JOIN subnodes sn ON pg.parent_id = sn.id
        ),
        leaf_nodes AS (
        -- 过滤出不是任何节点父节点的叶子节点
        SELECT sn.id
        FROM subnodes sn
        LEFT JOIN product_group pg ON sn.id = pg.parent_id
        WHERE pg.id IS NULL
        )
        SELECT DISTINCT id
        FROM leaf_nodes
        ORDER BY id;
    </select>

    <select id="queryProductGroup"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo">
        select
        id,name,pdm_model_spec, product_category_id,parent_id,path_name,path_id,group_level,create_time,update_time,create_by,update_by
        from
        product_group
        <where>
            product_category_id = #{productCategoryId}
            <if test="groupName != null and groupName != ''">
                and name = #{groupName}
            </if>
            <if test="parentId != null and parentId != ''">
                and parent_id = #{parentId}
            </if>
        </where>
        order by name asc
    </select>

    <select id="selectChildNodeByParentId"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo">
          select id , product_name as name, parent_id from product_category pc where parent_id = #{parentId}
    </select>
    <!-- Started by AICoder, pid:g6b8df1d0ezd06a1408f0a7cf0bb4d1926606613 -->
    <select id="selectProductNodeById" resultType="com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupEntity">
        select id , product_name as name, parent_id from product_category pc where id = #{id}
    </select>

    <select id="selectChildNodeByParentIdList" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo">
        select id , product_name as name, parent_id from product_category pc where parent_id IN
        <foreach item="item" index="index" collection="parentIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!-- Ended by AICoder, pid:g6b8df1d0ezd06a1408f0a7cf0bb4d1926606613 -->
</mapper>