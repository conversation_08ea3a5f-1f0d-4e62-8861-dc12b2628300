<?xml version="1.0" encoding="UTF-8"?>
<!-- Started by AICoder, pid:6f7c4592a44f5bd1433d0a85e099c135c2f8a949 -->
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductCoreParamMapper">

    <select id="selectProductCoreParamList"
            resultType="com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCoreParamVo">
        select id,product_category_id,param_group_L1,param_group_L2,main_param,param_remark,update_time
        from product_core_param
        <where>
            <if test="queryDto.productCategoryId != null and queryDto.productCategoryId != ''">
                and product_category_id = #{queryDto.productCategoryId}
            </if>
            <if test="queryDto.paramGroupL1 != null and queryDto.paramGroupL1 != ''">
                AND LOWER(REPLACE(REPLACE(param_group_L1, '\n', ' '), '\t', ' ')) LIKE CONCAT('%', LOWER(#{queryDto.paramGroupL1}), '%')
            </if>
            <if test="queryDto.paramGroupL2 != null and queryDto.paramGroupL2 != ''">
                AND LOWER(REPLACE(REPLACE(param_group_L2, '\n', ' '), '\t', ' ')) LIKE CONCAT('%', LOWER(#{queryDto.paramGroupL2}), '%')
            </if>
            <if test="queryDto.mainParam != null and queryDto.mainParam != ''">
                AND LOWER(REPLACE(REPLACE(main_param, '\n', ' '), '\t', ' ')) LIKE CONCAT('%', LOWER(#{queryDto.mainParam}), '%')
            </if>
            and del_flag = 0
        </where>
        order by update_time
        <choose>
            <when test="queryDto.sort != '' and queryDto.sort == 'asc'">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>
    <insert id="addProductCoreParamBatch" useGeneratedKeys="false">
        INSERT INTO product_core_param (
        id,
        product_category_id,
        param_group_L1,
        param_group_L2,
        main_param,
        param_remark,
        del_flag,
        create_time,
        update_time,
        create_by,
        update_by
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.productCategoryId},
            #{item.paramGroupL1},
            #{item.paramGroupL2},
            #{item.mainParam},
            #{item.paramRemark},
            #{item.delFlag},
            #{item.createTime},
            #{item.updateTime},
            #{item.createBy},
            #{item.updateBy}
            )
        </foreach>
    </insert>

    <update id="batchUpdateDelFlag">
        UPDATE product_core_param
        SET del_flag = 1,
        update_time = CURRENT_TIMESTAMP
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>

        <!-- Ended by AICoder, pid:6f7c4592a44f5bd1433d0a85e099c135c2f8a949 -->