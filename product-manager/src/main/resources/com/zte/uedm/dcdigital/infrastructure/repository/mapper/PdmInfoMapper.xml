<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.PdmInfoMapper">
    <insert id="batchAdd">
        insert into pdm_info
            (id, name, sales_code, production_code, sales_status, unit, description)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.id}, #{item.name}, #{item.salesCode}, #{item.productionCode}, #{item.salesStatus},
            #{item.unit}, #{item.description} )
        </foreach>
    </insert>
    <update id="batchUpdateByIds" parameterType="java.util.List">
        <foreach collection="pdmInfoPoList" item="item" separator=";">
            UPDATE pdm_info set
            <if test="item.name!=null and item.name!=''">
                "name" = #{item.name},
            </if>
            <if test="item.productionCode!=null and item.productionCode!=''">
                production_code = #{item.productionCode},
            </if>
            <if test="item.salesStatus!=null and item.salesStatus!=''">
                sales_status = #{item.salesStatus},
            </if>
            <if test="item.unit!=null and item.unit!=''">
                unit = #{item.unit},
            </if>
            <if test="item.description!=null and item.description!=''">
                description = #{item.description},
            </if>
            where id = #{item.id} AND sales_code = #{item.salesCode}
        </foreach>
    </update>
    <select id="queryBySalesCode" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.PdmInfoPo">
        select * from pdm_info where sales_code = #{salesCode}
    </select>
    <select id="queryAllSalesCode" resultType="java.lang.String">
        select distinct sales_code from pdm_info
    </select>

    <select id="queryAllSalesStatus" resultType="java.lang.String">
        select distinct sales_status from pdm_info
    </select>
    <select id="queryPdmInfoBySalesCodeList"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.PdmInfoPo">
        select * from pdm_info where sales_code in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>