<?xml version="1.0" encoding="UTF-8"?>
<!-- Started by AICoder, pid:o6c3fb9c1co56df1409009d660f1668494861817 -->
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductBrandTagMapper">

    <!-- resultMap for ProductBrandTagPo -->
    <resultMap type="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductBrandTagPo" id="ProductBrandTagResult">
        <id property="id" column="id"/>
        <result property="tagName" column="tag_name"/>
        <result property="brandId" column="brand_id"/>
        <result property="tagOrder" column="tag_order"/>
    </resultMap>

    <!-- Insert a new record into product_brand_tag table -->
    <insert id="insertProductBrandTag" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductBrandTagPo" keyProperty="id">
        INSERT INTO product_brand_tag (
        id,
        tag_name,
        brand_id,
        tag_order
        ) VALUES (
        #{id,jdbcType=VARCHAR},
        #{tagName,jdbcType=VARCHAR},
        #{brandId,jdbcType=VARCHAR},
        #{tagOrder,jdbcType=INTEGER}
        )
    </insert>

    <!-- Select records from product_brand_tag table by ID -->
    <select id="selectProductBrandTagById" parameterType="String" resultMap="ProductBrandTagResult">
        SELECT id, tag_name, brand_id FROM product_brand_tag
        WHERE id = #{id}
    </select>

    <!-- Select records from product_brand_tag table with conditions -->
    <select id="selectProductBrandTagList" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductBrandTagPo" resultMap="ProductBrandTagResult">
        SELECT id, tag_name, brand_id,tag_order FROM product_brand_tag
        <where>
            <if test="id != null and id != ''">
                AND id = #{id}
            </if>
            <if test="tagName != null and tagName != ''">
                AND tag_name LIKE CONCAT('%', #{tagName}, '%')
            </if>
            <if test="brandId != null and brandId != ''">
                AND brand_id = #{brandId}
            </if>
        </where>
        ORDER BY tag_order
    </select>

    <!-- Select records from product_brand_tag table by Brand IDs -->
    <select id="selectProductBrandTagListByBrandIds" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductBrandTagPo">
        SELECT id, tag_name, brand_id,tag_order FROM product_brand_tag
        WHERE brand_id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- Update an existing record in product_brand_tag table -->
    <update id="updateProductBrandTag" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProductBrandTagPo">
        UPDATE product_brand_tag
        <set>
            <if test="tagName != null">tag_name = #{tagName},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="tagOrder != null">tag_order = #{tagOrder}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- Delete a record from product_brand_tag table by ID -->
    <delete id="deleteProductBrandTagById" parameterType="String">
        DELETE FROM product_brand_tag WHERE id = #{id}
    </delete>

    <!-- Delete a record from product_brand_tag table by Brand ID -->
    <delete id="deleteProductBrandTagByBrandId" parameterType="String">
        DELETE FROM product_brand_tag WHERE brand_id = #{brandId}
    </delete>

    <!-- Batch delete records from product_brand_tag table by IDs -->
    <delete id="deleteProductBrandTagByIds" parameterType="java.util.List">
        DELETE FROM product_brand_tag WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

        <!-- Ended by AICoder, pid:o6c3fb9c1co56df1409009d660f1668494861817 -->