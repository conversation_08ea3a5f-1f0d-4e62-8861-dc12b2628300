<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.RequirementDashboardMapper">

    <resultMap id="dashboardMap" type="com.zte.uedm.dcdigital.infrastructure.repository.po.RequirementDashboardPo" autoMapping="true">
        <result column="resource_confirmation_mode" property="resourceConfirmationMode" typeHandler="org.apache.ibatis.type.ArrayTypeHandler"/>
    </resultMap>

    <resultMap id="requirementDashboardMap" type="com.zte.uedm.dcdigital.interfaces.web.product.vo.RequirementDashboardVo" autoMapping="true">
        <result column="resource_confirmation_mode" property="resourceConfirmationModeList" typeHandler="org.apache.ibatis.type.ArrayTypeHandler"/>
    </resultMap>


    <select id="selectByProjectIdAndCategoryId"
            resultMap="dashboardMap">
        SELECT * FROM requirement_dashboard WHERE product_category_id = #{productCategoryId} and project_id = #{projectId}
    </select>
    <select id="queryByProjectAndCondition"
            resultMap="requirementDashboardMap">
        SELECT
        ROW_NUMBER() OVER (ORDER BY pc.product_name) AS rowNum,  -- 添加序号列
        rd.*,
        pc.id AS subcategoryId,
        pc.product_name AS subcategory,
        pc.parent_id as productCategory,
        pce.material_type as materialType
        FROM
        product_category pc
        LEFT JOIN
        product_category_extra_info pce ON pce.product_category_id = pc.id
        LEFT JOIN
        requirement_dashboard rd ON rd.product_category_id = pc.id AND rd.project_id = #{projectId}
        WHERE
        pc.id IN
        <foreach collection="productCategoryIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND (
        rd.id IS NULL  -- 确保包含那些在requirement_dashboard表中没有匹配记录的产品分类
        OR (rd.id IS NOT NULL AND rd.status = #{status})  -- 当requirement_dashboard中有匹配的数据时
        )
        ORDER BY pc.product_name
    </select>
    <select id="queryByCancelCondition"
            resultMap="requirementDashboardMap">
        SELECT
        ROW_NUMBER() OVER (ORDER BY pc.product_name) AS rowNum,  -- 添加序号列
        rd.*,
        pc.id AS subcategoryId,
        pc.product_name AS subcategory,
        pc.parent_id as productCategory,
        pce.material_type as materialType
        FROM
        product_category pc
        INNER JOIN
        product_category_extra_info pce ON pce.product_category_id = pc.id
        INNER JOIN
        requirement_dashboard rd ON rd.product_category_id = pc.id AND rd.status = #{status} AND rd.project_id = #{projectId}
        WHERE
        pc.id IN
        <foreach collection="productCategoryIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY pc.product_name
    </select>
    <select id="queryRequirementDashboardDetailById"
            resultMap="dashboardMap">
        SELECT * FROM requirement_dashboard WHERE id = #{id}

    </select>
</mapper>