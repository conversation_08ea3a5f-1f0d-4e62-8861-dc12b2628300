DROP TABLE IF EXISTS oth_info;
CREATE TABLE oth_info
(
  id text NOT NULL,
  name text NOT NULL UNIQUE,
  sales_code text NULL,
  production_code text NULL,
  sales_status text,
  unit text,
  description text,
  PRIMARY KEY (id)
);

COMMENT ON TABLE oth_info IS '非pdm信息表';

COMMENT ON COLUMN oth_info.id IS 'id';
COMMENT ON COLUMN oth_info.name IS '自定义名称';
COMMENT ON COLUMN oth_info.sales_code IS '销售代码';
COMMENT ON COLUMN oth_info.production_code IS '生产代码';
COMMENT ON COLUMN oth_info.sales_status IS '销售状态';
COMMENT ON COLUMN oth_info.unit IS '单位';
COMMENT ON COLUMN oth_info.description IS '描述';

ALTER TABLE public.material ALTER COLUMN pdm_info_id DROP NOT NULL;
ALTER TABLE public.material ADD oth_info_id text NULL;
COMMENT ON COLUMN material.oth_info_id IS '非pdm的id';

ALTER TABLE public.pdm_info ALTER COLUMN name DROP NOT NULL;

ALTER TABLE public.material ADD recommended_level text NULL;
ALTER TABLE public.material ADD unit text NULL;
ALTER TABLE public.material ADD description text NULL;
COMMENT ON COLUMN material.recommended_level IS '推荐等级';
COMMENT ON COLUMN material.unit IS '单位';
COMMENT ON COLUMN material.description IS '描述';

ALTER TABLE public.material_historical ADD recommended_level text NULL;
ALTER TABLE public.material_historical ADD unit text NULL;
ALTER TABLE public.material_historical ADD description text NULL;
ALTER TABLE public.material_historical ADD production_code text NULL;
ALTER TABLE public.material_historical ADD sales_status text NULL;
COMMENT ON COLUMN material_historical.recommended_level IS '推荐等级';
COMMENT ON COLUMN material_historical.unit IS '单位';
COMMENT ON COLUMN material_historical.description IS '描述';
COMMENT ON COLUMN material_historical.production_code IS '生产代码';
COMMENT ON COLUMN material_historical.sales_status IS '销售状态';

ALTER TABLE public.material_temporary ADD recommended_level text NULL;
ALTER TABLE public.material_temporary ADD unit text NULL;
ALTER TABLE public.material_temporary ADD description text NULL;
COMMENT ON COLUMN material_temporary.recommended_level IS '推荐等级';
COMMENT ON COLUMN material_temporary.unit IS '单位';
COMMENT ON COLUMN material_temporary.description IS '描述';

ALTER TABLE public.product_category_extra_info ADD extended_warranty_factor DECIMAL NULL;
COMMENT ON COLUMN product_category_extra_info.extended_warranty_factor IS '延保系数';

--修改product_brand表中procurement_mode的数据格式
ALTER TABLE product_brand
ALTER COLUMN procurement_mode TYPE text USING procurement_mode::text;
--增加product_brand_tag表中字段
ALTER TABLE product_brand_tag
ADD COLUMN tag_order int NOT NULL DEFAULT 0;

ALTER TABLE public.product_category_extra_info ADD product_no text NULL;
COMMENT ON COLUMN product_category_extra_info.product_no IS '产品编号';

--2025-04-07 增加需求管理建表sql脚本
DROP TABLE IF EXISTS demand_management;
CREATE TABLE demand_management (
    id TEXT NOT NULL, -- 主键ID
    project_id TEXT NOT NULL, -- 项目/商机id
    bill_quantity_id TEXT NOT NULL, -- 工程量清单id
    product_category_id TEXT NOT NULL, -- 产品小类id
    create_user TEXT NOT NULL, -- 创建人
    exp_time_complet TEXT , -- 期望完成时间
    act_time_complet TEXT , -- 实际完成时间
    processor TEXT NOT NULL, -- 处理人
    demand_type TEXT NOT NULL, -- 状态
    create_by TEXT NOT NULL, -- 创建用户ID
    create_time TEXT NOT NULL, -- 创建时间
    update_by TEXT NOT NULL, -- 更新用户ID
    update_time TEXT NOT NULL -- 更新时间
);

-- 添加注释
COMMENT ON TABLE demand_management IS '需求管理表';
COMMENT ON COLUMN demand_management.id IS '主键ID';
COMMENT ON COLUMN demand_management.project_id IS '项目/商机id';
COMMENT ON COLUMN demand_management.bill_quantity_id IS '工程量清单id';
COMMENT ON COLUMN demand_management.product_category_id IS '产品小类id';
COMMENT ON COLUMN demand_management.create_user IS '创建人';
COMMENT ON COLUMN demand_management.exp_time_complet IS '期望完成时间';
COMMENT ON COLUMN demand_management.act_time_complet IS '实际完成时间';
COMMENT ON COLUMN demand_management.processor IS '处理人';
COMMENT ON COLUMN demand_management.demand_type IS '状态';
COMMENT ON COLUMN demand_management.create_by IS '创建用户ID';
COMMENT ON COLUMN demand_management.create_time IS '创建时间';
COMMENT ON COLUMN demand_management.update_by IS '更新用户ID';
COMMENT ON COLUMN demand_management.update_time IS '更新时间';


DROP TABLE IF EXISTS procurement_cost;
CREATE TABLE  procurement_cost (
    id TEXT NOT NULL PRIMARY KEY,
    lectotype_id TEXT NOT NULL,
    lectotype_type TEXT NOT NULL,
    negotiated_price TEXT ,
    datum_target_price TEXT ,
    challenge_target_price TEXT ,
    open_tender_price TEXT ,
    set_bid_price TEXT ,
    bid_issuing_time TEXT ,
    bid_opening_time TEXT ,
    create_time TEXT NOT NULL,
    update_time TEXT NOT NULL,
    create_by TEXT NOT NULL,
    update_by TEXT NOT NULL
);
-- 添加 procurement_cost 表注释
COMMENT ON TABLE procurement_cost IS '招采成本表';
-- 添加 procurement_cost 字段注释
COMMENT ON COLUMN procurement_cost.id IS '自定义排序ID主键';
COMMENT ON COLUMN procurement_cost.lectotype_id IS '归属选型单id';
COMMENT ON COLUMN procurement_cost.lectotype_type IS '归属选型单类型';
COMMENT ON COLUMN procurement_cost.negotiated_price IS '洽谈价';
COMMENT ON COLUMN procurement_cost.datum_target_price IS '基准目标价';
COMMENT ON COLUMN procurement_cost.challenge_target_price IS '挑战目标价';
COMMENT ON COLUMN procurement_cost.open_tender_price IS '开标价';
COMMENT ON COLUMN procurement_cost.set_bid_price IS '定标价';
COMMENT ON COLUMN procurement_cost.bid_issuing_time IS '发标时间';
COMMENT ON COLUMN procurement_cost.bid_opening_time IS '开标时间';
COMMENT ON COLUMN procurement_cost.create_time IS '创建时间';
COMMENT ON COLUMN procurement_cost.update_time IS '更新时间';
COMMENT ON COLUMN procurement_cost.create_by IS '创建用户';
COMMENT ON COLUMN procurement_cost.update_by IS '更新用户';

DROP TABLE IF EXISTS procurement_price_history;
CREATE TABLE  procurement_price_history (
    id TEXT NOT NULL PRIMARY KEY,
    lectotype_id TEXT NOT NULL,
    lectotype_type TEXT NOT NULL,
    price_category TEXT ,
    price TEXT ,
    create_time TEXT NOT NULL,
    update_time TEXT NOT NULL,
    create_by TEXT NOT NULL,
    update_by TEXT NOT NULL
);
-- 添加 procurement_cost 表注释
COMMENT ON TABLE procurement_price_history IS '招采成本价格历史表';
-- 添加 procurement_cost 字段注释
COMMENT ON COLUMN procurement_price_history.id IS '自定义排序ID主键';
COMMENT ON COLUMN procurement_price_history.lectotype_id IS '归属选型单id';
COMMENT ON COLUMN procurement_price_history.lectotype_type IS '归属选型单类型';
COMMENT ON COLUMN procurement_price_history.price_category IS '价格类型';
COMMENT ON COLUMN procurement_price_history.price IS '价格';
COMMENT ON COLUMN procurement_price_history.create_time IS '创建时间';
COMMENT ON COLUMN procurement_price_history.update_time IS '更新时间';
COMMENT ON COLUMN procurement_price_history.create_by IS '创建用户';
COMMENT ON COLUMN procurement_price_history.update_by IS '更新用户';

-- Started by AICoder, pid:le897ne2226c6311431a0a5710de203ec928d511
DROP TABLE IF EXISTS demand_management_lectotype;

-- 创建 demand_management_lectotype 表
CREATE TABLE demand_management_lectotype (
    lectotype_id   TEXT      NOT NULL PRIMARY KEY,
    demand_id      TEXT      NOT NULL,
    lectotype_name TEXT      NOT NULL,
    lectotype_type TEXT      NOT NULL,
    lectotype_status INTEGER,
    send_bid_time  TEXT,
    end_time       TEXT,
    open_bid_time  TEXT,
    bid_url        TEXT,
    file_ids       TEXT,
    create_by      TEXT,
    create_time    TEXT,
    update_user      TEXT,
    update_by      TEXT,
    update_time    TEXT
);

-- 添加 demand_management_lectotype 表注释
COMMENT ON TABLE demand_management_lectotype IS '需求选型单';

-- 添加 demand_management_lectotype 字段注释
COMMENT ON COLUMN demand_management_lectotype.lectotype_id IS '需求选型ID';
COMMENT ON COLUMN demand_management_lectotype.demand_id IS '需求ID';
COMMENT ON COLUMN demand_management_lectotype.lectotype_name IS '选型名称';
COMMENT ON COLUMN demand_management_lectotype.lectotype_type IS '选型类型';
COMMENT ON COLUMN demand_management_lectotype.lectotype_status IS '选型状态';
COMMENT ON COLUMN demand_management_lectotype.send_bid_time IS '发标时间';
COMMENT ON COLUMN demand_management_lectotype.end_time IS '完成时间';
COMMENT ON COLUMN demand_management_lectotype.open_bid_time IS '开标时间';
COMMENT ON COLUMN demand_management_lectotype.bid_url IS '招采申请url';
COMMENT ON COLUMN demand_management_lectotype.file_ids IS '文件id列表';
COMMENT ON COLUMN demand_management_lectotype.create_by IS '创建用户';
COMMENT ON COLUMN demand_management_lectotype.create_time IS '创建时间';
COMMENT ON COLUMN demand_management_lectotype.update_by IS '更新用户';
COMMENT ON COLUMN demand_management_lectotype.update_time IS '更新时间';

-- Ended by AICoder, pid:le897ne2226c6311431a0a5710de203ec928d511

-- Started by AICoder, pid:vb9f0c68b8ue3ef14f0d0bdd30e586111397f452
DROP TABLE IF EXISTS demand_management_lectotype_material;
CREATE TABLE demand_management_lectotype_material (
    lectotype_id   TEXT       NOT NULL,
    material_id    TEXT       NOT NULL,
    create_time    TEXT,
    PRIMARY KEY (lectotype_id, material_id)
);
COMMENT ON TABLE demand_management_lectotype_material IS '需求选型单关联物料';
COMMENT ON COLUMN demand_management_lectotype_material.lectotype_id IS '需求选型ID';
COMMENT ON COLUMN demand_management_lectotype_material.material_id IS '物料ID';
COMMENT ON COLUMN demand_management_lectotype_material.create_time IS '创建时间';
-- Ended by AICoder, pid:vb9f0c68b8ue3ef14f0d0bdd30e586111397f452


-- 2025-04-21 产品小类额外信息表 新增字段  材料类别
ALTER TABLE product_category_extra_info ADD material_type text NULL;
COMMENT ON COLUMN product_category_extra_info.material_type IS '材料类别';


-- 20250423
ALTER TABLE public.material ADD name_en text NULL;
COMMENT ON COLUMN public.material.name_en IS '物料名称英文';
ALTER TABLE public.material ADD unit_en text NULL;
COMMENT ON COLUMN public.material.unit_en IS '单位名称英文';
ALTER TABLE public.material_historical  ADD name_en text NULL;
COMMENT ON COLUMN public.material_historical.name_en IS '物料名称英文';
ALTER TABLE public.material_historical ADD unit_en text NULL;
COMMENT ON COLUMN public.material_historical.unit_en IS '单位名称英文';
ALTER TABLE public.material_temporary  ADD name_en text NULL;
COMMENT ON COLUMN public.material_temporary.name_en IS '物料名称英文';
ALTER TABLE public.material_temporary ADD unit_en text NULL;
COMMENT ON COLUMN public.material_temporary.unit_en IS '单位名称英文';
-- 2025-04-25
 DROP TABLE IF EXISTS requirement_dashboard;
CREATE TABLE requirement_dashboard (
    id TEXT NOT NULL,  -- 主表id
    product_category_id TEXT NOT NULL,  -- 产品小类id
    project_id TEXT NOT NULL,  -- 项目id
    resource_confirmation_mode TEXT[],  -- 资源确认模式：可多个：招标、自研、战采、框标、独家、生态、取消
    demand_confirmation_time TEXT,  -- 需求确认时间：时间选择，精确到天
    expect_bid_open_time TEXT,  -- 期望招标开标时间：时间选择，精确到天
    status INTEGER DEFAULT 0,  -- 状态：0-正常，1-取消，默认正常
    tender_launch_time TEXT,  -- 招标TS发起时间：时间选择，精确到天
    bidding_close_time TEXT,  -- 招标TS关闭时间：时间选择，精确到天
    actual_bid_opening_time TEXT,  -- 实际开标时间：时间选择，精确到天
    code_status TEXT,  -- 代码状态：1-待提交审批，2-上架审批中，3-已上架
    remarks TEXT,  -- 备注
    bidding_brand text NULL,
    create_by TEXT NOT NULL,  -- 创建人
    create_time TEXT NOT NULL,  -- 创建时间
    update_by TEXT NOT NULL,  -- 更新人
    update_time TEXT NOT NULL,  -- 更新时间
    PRIMARY KEY (id),  -- 设置主键
    CONSTRAINT unique_product_category_per_project UNIQUE (product_category_id, project_id)  -- 项目下产品小类只有一个
);
-- 添加注释
COMMENT ON TABLE requirement_dashboard IS '需求看板表';
COMMENT ON COLUMN requirement_dashboard.id IS '主键ID';
COMMENT ON COLUMN requirement_dashboard.project_id IS '项目/商机id';
COMMENT ON COLUMN requirement_dashboard.product_category_id IS '产品小类id';
COMMENT ON COLUMN requirement_dashboard.resource_confirmation_mode IS '资源确认模式：可多个：招标、自研、战采、框标、独家、生态、取消';
COMMENT ON COLUMN requirement_dashboard.demand_confirmation_time IS ' 需求确认时间';
COMMENT ON COLUMN requirement_dashboard.expect_bid_open_time IS '期望招标开标时间';
COMMENT ON COLUMN requirement_dashboard.status IS '状态：0-正常，1-取消，默认正常';
COMMENT ON COLUMN requirement_dashboard.tender_launch_time IS '招标TS发起时间';
COMMENT ON COLUMN requirement_dashboard.bidding_close_time IS '招标TS关闭时间';
COMMENT ON COLUMN requirement_dashboard.actual_bid_opening_time IS '实际开标时间';
COMMENT ON COLUMN requirement_dashboard.code_status IS '代码状态：1-待提交审批，2-上架审批中，3-已上架';
COMMENT ON COLUMN requirement_dashboard.remarks IS '备注';
COMMENT ON COLUMN requirement_dashboard.bidding_brand IS '投标品牌';
COMMENT ON COLUMN requirement_dashboard.create_by IS '创建用户ID';
COMMENT ON COLUMN requirement_dashboard.create_time IS '创建时间';
COMMENT ON COLUMN requirement_dashboard.update_by IS '更新用户ID';
COMMENT ON COLUMN requirement_dashboard.update_time IS '更新时间';

--2025-04-28  去除物料历史表pdm_info_id 必填
ALTER TABLE public.material_historical ALTER COLUMN pdm_info_id DROP NOT NULL;

--2025-04-28  修改品牌表selection_attribute字段类型
ALTER TABLE public.product_brand ALTER COLUMN selection_attribute TYPE int USING selection_attribute::int;

--2025-05-19  价格历史表新增字段 remark
ALTER TABLE public.procurement_price_history ADD remark varchar NULL;
COMMENT ON COLUMN public.procurement_price_history.remark IS '备注';

--2025-05-21 物料表新增字段 specification_remark
ALTER TABLE public.material ADD specification_remark varchar NULL;
COMMENT ON COLUMN public.material.specification_remark IS '物料规格书备注';

--2025-05-21 物料暂存表新增字段 specification_remark
ALTER TABLE public.material_temporary ADD specification_remark varchar NULL;
COMMENT ON COLUMN public.material_temporary.specification_remark IS '物料规格书备注';


-- 20250523
DROP TABLE IF EXISTS strong_echo;
CREATE TABLE strong_echo (
	id text NOT NULL,
	category_id text NOT NULL, -- 分类ID
	title text NULL, -- 标题
	create_time text NULL, -- 创建时间
	update_time text NULL, -- 更新时间
	create_by text NULL, -- 创建人
	update_by text NULL, -- 更新人
	"content" text NULL, -- 内容
	contact text NULL -- 需通知的用户ID，多个英文逗号分隔
);
COMMENT ON TABLE strong_echo IS '回音强';

COMMENT ON COLUMN strong_echo.category_id IS '分类ID';
COMMENT ON COLUMN strong_echo.title IS '标题';
COMMENT ON COLUMN strong_echo.create_time IS '创建时间';
COMMENT ON COLUMN strong_echo.update_time IS '更新时间';
COMMENT ON COLUMN strong_echo.create_by IS '创建人';
COMMENT ON COLUMN strong_echo.update_by IS '更新人';
COMMENT ON COLUMN strong_echo."content" IS '内容';
COMMENT ON COLUMN strong_echo.contact IS '需通知的用户ID，多个英文逗号分隔';

DROP TABLE IF EXISTS strong_echo_comment;
CREATE TABLE strong_echo_comment (
	id text NOT NULL,
	echo_id text NOT NULL, -- 问题ID
	"comment" text NULL, -- 评论内容
	create_time text NULL, -- 创建时间
	update_time text NULL,
	create_by text NULL,
	update_by text NULL, -- 更新时间
	p_id text NULL, -- 回复评论id
	contact text NULL -- 通知人ID,英文逗号分隔
);
COMMENT ON COLUMN strong_echo_comment.echo_id IS '问题ID';
COMMENT ON COLUMN strong_echo_comment."comment" IS '评论内容';
COMMENT ON COLUMN strong_echo_comment.create_time IS '创建时间';
COMMENT ON COLUMN strong_echo_comment.update_by IS '更新时间';
COMMENT ON COLUMN strong_echo_comment.p_id IS '回复评论id';
COMMENT ON COLUMN strong_echo_comment.contact IS '通知人ID,英文逗号分隔';

--2025-06-10 新增需求表字段
ALTER TABLE public.demand_management ADD bill_quantitie_time varchar NULL;
COMMENT ON COLUMN public.demand_management.bill_quantitie_time IS '工程量清单条目发生变化时间';

-- 向product_category表中添加sort_order列
ALTER TABLE product_category ADD COLUMN sort_order INTEGER NOT NULL DEFAULT 0;

-- 为sort_order列添加注释
COMMENT ON COLUMN product_category.sort_order IS '定义产品分类的显示顺序';



