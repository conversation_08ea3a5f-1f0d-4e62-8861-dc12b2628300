sales.code.empty=销售代码为空
sales.code.repeat=销售代码不能重复
sales.code.exist=销售代码已存在
sales.code.not.exist=pdm无对应销售代码
pdm.material.name.empty=PDM物料名称为空
pdm.material.repeat=PDM物料重复，销售代码及品牌联合唯一
sales.code.already.used=销售代码已被{0}物料使用
brand.empty=品牌为空
group.L1.empty=分组L1为空
group.L1.length.limit.exceeded=分组L1长度超过50个字符
group.L1.not.exist=分组L1{0}不存在
group.L2.empty=分组L2为空
group.L2.not.exist=分组L2{0}不存在
group.L2.length.limit.exceeded=分组L2长度超过50个字符
group.not.exist=产品小类{0}下不存在分组{1}
non.leaf.node.group=非叶子节点分组，不能挂载物料
brand.limit.exceeded=品牌长度超过200个字符
supplier.empty=供应商为空
supplier.limit.exceeded=供应商长度超过200个字符
service.Length.limit.exceeded=服务长度超过200个字符
specification.model.Length.limit.exceeded=规格型号长度超过100个字符
unit.Length.limit.exceeded=单位长度超过10个字符
description.Length.limit.exceeded=描述长度超过1000个字符
product.category.name.empty=产品小类名称为空
product.category.name.limit.exceeded=产品小类名称长度超过50个字符
product.category.name.error=产品小类:{0} 有误,请修改为:{1}
material.name.empty=物料名称为空
material.name.exceeded.limit=物料名称长度超过200个字符
material.name.has.been.used=物料名称已被使用
material.deletes=删除物料
duplicate.material.name=物料名称重复
unit.empty=单位为空
description.empty=描述为空
purchase.mode.empty=采购模式为空
purchase.mode.unknown=未知的采购模式,采购模式限:自研、战采、项采、框采、生态
expiration.date.empty=失效日期为空
expiration.date.format.error=失效日期格式错误,正确格式为yyyy/mm/dd，如2024/12/30
expiration.date.expired=失效日期已过期
warranty.period.empty=质保期为空
warranty.limit.exceeded=质保期超出限制范围,质保期在0-120月
recommended.Level.error=推荐等级错误,从A、B、C三个等级中选择其一
product.core.param.line=校验失败行:
product.core.param.main.param=主要性能参数不能超过3000字符
product.core.param.main.param.blank=主要性能参数为空
product.core.param.paramL1=产品分组l1不能超过50字符
product.core.param.paramL2=产品分组l2不能超过50字符
product.core.param.paramRemark=参数备注不能超过1000字符
operation.log.details=操作日志详情:{0}
operation.fail.log=操作失败日志:{0}
operation.kanban.board=编辑产品需求看板看板
echo.del=回音墙删除
echo.add=回音墙新增
echo.upd=回音墙编辑
echo.add.com=回音墙评论新增
echo.upd.com=回音墙评论编辑
echo.del.com=回音墙评论删除
brand.detail=品牌详情
material.detail=物料详情
module.product.manager=产品管理
