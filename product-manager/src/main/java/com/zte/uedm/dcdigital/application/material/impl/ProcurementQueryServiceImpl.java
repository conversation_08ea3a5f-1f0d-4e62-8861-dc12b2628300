/* Started by AICoder, pid:c591f1e21ew711414873098f3062425dd8d9ec0f */
package com.zte.uedm.dcdigital.application.material.impl;

import com.zte.uedm.dcdigital.application.material.ProcurementQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.service.ProcurementDomainService;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.PriceHistoryQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.ProcurementQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.SelectionFormQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.PriceHistoryVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.ProcurementCostVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.SelectionFormVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 采购查询服务实现类。
 * 提供对采购成本和价格历史记录的查询功能。
 */
@Slf4j
@Service
public class ProcurementQueryServiceImpl implements ProcurementQueryService {

    @Autowired
    private ProcurementDomainService procurementDomainService;

    /**
     * 根据选型单信息查询采购成本。
     *
     * @param queryDto 包含查询条件的数据传输对象
     * @return 采购成本视图对象，包含查询结果
     */
    @Override
    public ProcurementCostVo queryByModelSelection(ProcurementQueryDto queryDto) {
        return procurementDomainService.queryByModelSelection(queryDto);
    }

    /**
     * 查询价格历史记录。
     *
     * @param queryDto 包含查询条件的数据传输对象
     * @return 分页的价格历史记录视图对象列表
     */
    @Override
    public PageVO<PriceHistoryVo> queryPriceHistory(PriceHistoryQueryDto queryDto) {
        return procurementDomainService.queryPriceHistory(queryDto);
    }

    /**
     * 根据条件查询选型单。
     *
     * @param queryDto 包含查询条件的数据传输对象
     * @return 分页的选型单视图对象列表
     */
    @Override
    public PageVO<SelectionFormVo> querySelectionFormByCondition(SelectionFormQueryDto queryDto) {
        return procurementDomainService.querySelectionFormByCondition(queryDto);
    }
}
/* Ended by AICoder, pid:c591f1e21ew711414873098f3062425dd8d9ec0f */