package com.zte.uedm.dcdigital.interfaces.web.product.dto;
/* Started by AICoder, pid:g7282p1252h4aba149c108c840c5b74cbd42e2ef */
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductCoreParamStatusCode;
import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import com.zte.uedm.dcdigital.security.annotation.DcResourceField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;

@Setter
@Getter
@ToString
@Slf4j
public class ProductCoreParamAddDto {

    /**
     * 产品小类id
     */
    @NotBlank(message = "productCategoryId必填,productCategoryId is required")
    @LogMark(range = {OperationMethodEnum.ADD})
    @DcResourceField
    private String productCategoryId;

    /**
     * 产品分组L1
     */
    private String paramGroupL1;

    /**
     * 产品分组L2
     */
    private String paramGroupL2;

    /**
     * 主要性能参数
     */
    @NotBlank(message = "mainParam必填,mainParam is required")
    @LogMark(range = {OperationMethodEnum.ADD})
    private String mainParam;

    /**
     * 参数备注
     */
    private String paramRemark;

    public void parameterVerification() throws BusinessException {
        ValidResult validResult = ValidateUtils.validateObj(this);
        // 参数不能为空
        if (validResult.isError()) {
            log.error("[ProductCategoryEditDto check param] Invalid parameter: {}", validResult.getErrorMessage());
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
        if(paramGroupL1.trim().length() > GlobalConstants.NAME_LENGTH){
            log.error("[ProductCoreParamAddDto check paramGroupL1] Invalid parameter: {}", "The paramGroupL1 cannot be more than 50 characters");
            throw new BusinessException(ProductCoreParamStatusCode.PARAM_GROUP_L1_LENGTH);
        }
        if(paramGroupL2.trim().length() > GlobalConstants.NAME_LENGTH){
            log.error("[ProductCoreParamAddDto check paramGroupL2] Invalid parameter: {}", "The paramGroupL2 cannot be more than 50 characters");
            throw new BusinessException(ProductCoreParamStatusCode.PARAM_GROUP_L2_LENGTH);
        }
        if(mainParam.trim().length() > GlobalConstants.MAIN_PARAM_LENGTH){
            log.error("[ProductCoreParamAddDto check mainParam] Invalid parameter: {}", "The mainParam cannot be more than 3000 characters");
            throw new BusinessException(ProductCoreParamStatusCode.MAIN_PARAM_LENGTH);
        }
        if(paramRemark.trim().length() > GlobalConstants.DESCRIPTION_LENGTH){
            log.error("[ProductCoreParamAddDto check paramRemark] Invalid parameter: {}", "The paramRemark cannot be more than 1000 characters");
            throw new BusinessException(ProductCoreParamStatusCode.PARAM_REMARK_LENGTH);
        }
    }
}
/* Ended by AICoder, pid:g7282p1252h4aba149c108c840c5b74cbd42e2ef */