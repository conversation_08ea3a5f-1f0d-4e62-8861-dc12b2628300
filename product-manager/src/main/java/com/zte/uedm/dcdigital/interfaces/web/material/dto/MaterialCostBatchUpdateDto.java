/* Started by AICoder, pid:26270c686edfc79141140a68802d7a78ecf38f0f */
package com.zte.uedm.dcdigital.interfaces.web.material.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 物料成本批量更新数据传输对象 (DTO)。
 * 用于封装物料成本批量更新请求的参数，并提供参数校验功能。
 */
@Getter
@Setter
@ToString
@Slf4j
public class MaterialCostBatchUpdateDto {

    /**
     * 物料ID，不能为空。
     *
     * @NotBlank 确保该字段不能为空。
     */
    @NotBlank(message = "物料ID不能为空")
    private String id;

    /**
     * 物料成本，不能为空。
     *
     * @NotBlank 确保该字段不能为空。
     */
    @NotBlank(message = "物料成本不能为空")
    private String cost;

    /**
     * 校验参数是否合法。
     *
     * 1. 使用ValidateUtils.validateObj方法校验对象字段是否为空。
     * 2. 如果校验失败，记录错误日志并抛出BusinessException。
     * 3. 尝试将字符串转换为BigDecimal，以处理非常大或非常小的数值。
     * 4. 检查成本是否不小于0。
     * 5. 如果转换过程中发生异常，说明输入不是有效的数字表示，记录错误日志并抛出BusinessException。
     */
    public void validate() {
        // 校验bean字段
        ValidResult validResult = ValidateUtils.validateObj(this);
        if (validResult.isError()) {
            log.error("[check param] Parameter is blank. {}", validResult.getErrorMessage());
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
        try {
            // 尝试将字符串转换为BigDecimal，以处理非常大或非常小的数值
            BigDecimal price = new BigDecimal(cost);
            // 检查成本是否不小于0
            if (price.compareTo(BigDecimal.ZERO) < 0) {
                throw new BusinessException(StatusCode.INVALID_PARAMETER);
            }
        } catch (Exception e) {
            // 如果转换过程中发生异常，说明输入不是有效的数字表示
            log.error("[check param] Cost is not a valid number. {}", cost);
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
    }
}
/* Ended by AICoder, pid:26270c686edfc79141140a68802d7a78ecf38f0f */