/* Started by AICoder, pid:e57e8hda16m81751442d08316032a417a1376b5a */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.MaterialQueryBean;
import com.zte.uedm.dcdigital.infrastructure.repository.po.MaterialTemporaryPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物料临时数据访问对象（Mapper）接口。
 * 该接口继承自MyBatis Plus的BaseMapper，用于对MaterialTemporaryPo实体进行数据库操作。
 * 它提供了基本的CRUD（创建、读取、更新、删除）功能，并且可以通过MyBatis Plus的扩展方法实现更复杂的查询和操作。
 *
 * <AUTHOR>
 */
@Mapper
public interface MaterialTemporaryMapper extends BaseMapper<MaterialTemporaryPo> {


    List<MaterialQueryBean> unionQueryByApprovalId(@Param("approvalId") String approvalId);

    void batchUpdateApprovalIdById(@Param("list") List<String> list, @Param("approvalId") String approvalId);

    void saveBatch(@Param("list") List<MaterialTemporaryPo> list);

    MaterialTemporaryPo selectByMaterialId(String materialId);

    List<MaterialTemporaryPo> selectTempMaterialByApprovalId(@Param("approvalId") String approvalId);

    /* Started by AICoder, pid:b7ca337c93sb1bc14db70b4080f8650cf408abdf */
    /**
     * 批量更新临时物料信息。
     *
     * 此方法接收一个临时物料信息的列表，并批量更新这些记录。
     *
     * @param materialTemporaryPos 临时物料信息列表
     */
    void updateBatchBy(@Param("list") List<MaterialTemporaryPo> materialTemporaryPos);
    /* Ended by AICoder, pid:b7ca337c93sb1bc14db70b4080f8650cf408abdf */
}
/* Ended by AICoder, pid:e57e8hda16m81751442d08316032a417a1376b5a */