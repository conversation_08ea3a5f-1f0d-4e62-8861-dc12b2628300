package com.zte.uedm.dcdigital.interfaces.web.product.dto;
/* Started by AICoder, pid:u10bc3ebb46384b1413508db808544543d66f457 */
import lombok.Getter;
import lombok.Setter;

/**
 * StrongEchoPo 类用于表示强回声的数据持久化对象。
 * 该类包含多个字段，用于描述强回声的各种属性。
 */
@Getter
@Setter
public class StrongEchoAddDto {

    /**
     * id 属性表示记录的唯一标识符。
     */
    private String id;

    /**
     * categoryId 属性表示类别 ID。
     */
    private String categoryId;

    /**
     * title 属性表示标题。
     */
    private String title;

    /**
     * content 属性表示内容。
     */
    private String content;

    /**
     * contact 属性表示联系信息。
     */
    private String contact;

    /**
     * createBy 属性表示创建者。
     */
    private String createBy;

    /**
     * createTime 属性表示创建时间。
     */
    private String createTime;

    /**
     * updateBy 属性表示更新者。
     */
    private String updateBy;

    /**
     * updateTime 属性表示更新时间。
     */
    private String updateTime;
}
/* Ended by AICoder, pid:u10bc3ebb46384b1413508db808544543d66f457 */