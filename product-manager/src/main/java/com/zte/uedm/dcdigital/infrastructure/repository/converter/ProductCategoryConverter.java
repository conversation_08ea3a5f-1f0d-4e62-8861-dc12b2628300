/* Started by AICoder, pid:9b7b15afe5x2fc91486b08ec10f3f82d6f0014f1 */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductSubcategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryTreeVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProductCategoryConverter {
    // 实例变量命名优化（使用单例模式标准写法）
    ProductCategoryConverter INSTANCE = Mappers.getMapper(ProductCategoryConverter.class);

    /**
     * PO列表转VO树结构列表
     */
    List<ProductCategoryTreeVo> productCategoryPoListToProductCategoryTreeVoList(
            List<ProductCategoryPo> productCategoryPoList);

    /**
     * PO列表转VO结构列表
     */
    List<ProductSubcategoryVo> productCategoryPoListToProductCategoryVoList(
            List<ProductCategoryPo> productCategoryPoList);
}

/* Ended by AICoder, pid:9b7b15afe5x2fc91486b08ec10f3f82d6f0014f1 */