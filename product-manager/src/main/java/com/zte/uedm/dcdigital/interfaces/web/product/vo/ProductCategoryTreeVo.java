/* Started by AICoder, pid:39575l80d2q93f214b2d0ab3602c9b55cbe6e4fc */
package com.zte.uedm.dcdigital.interfaces.web.product.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品小类树形结构对象
 */
@Getter
@Setter
@ToString
public class ProductCategoryTreeVo {
    /**
     * ID，产品小类的唯一标识符。
     */
    private String id;

    /**
     * 名称，产品小类的名称。
     */
    private String productName;

    /**
     * 父ID，父级产品分类的ID。
     */
    private String parentId;

    /**
     * 类型，产品分类的类型(1:产品线;2:产品大类,3:产品小类)。
     */
    private Integer nodeType;

    /**
     * 路径ID，产品分类的路径ID。
     */
    private String pathId;

    /**
     * 路径名称，产品分类的路径名称。
     */
    private String pathName;

    /**
     * 排序。
     */
    private Integer sortOrder;

    /**
     * 产品编码。
     */
    private String productNo;

    /**
     * 产品等级（可选），值为'2'的时候显示灰色。
     */
    private String productLevel;

    /**
     * 子集列表
     */
    private List<ProductCategoryTreeVo> children = new ArrayList<>();
}

/* Ended by AICoder, pid:39575l80d2q93f214b2d0ab3602c9b55cbe6e4fc */