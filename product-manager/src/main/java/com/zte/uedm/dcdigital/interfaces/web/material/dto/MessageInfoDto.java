package com.zte.uedm.dcdigital.interfaces.web.material.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * iCenter消息通知info参数DTO
 * 用于封装消息通知的通用参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
/* Started by AICoder, pid:93c493e3b5j3bc5144530b5e40c2ab3126393225 */
public class MessageInfoDto {

    /**
     * 目标id列表
     * single, officialAccount, ordinaryBusiness 会话类型 targetIds填工号
     * group 群聊 targetIds填群组id 例如：sip:<EMAIL>
     */
    @ApiModelProperty(value = "目标id列表", required = true, notes = "单次最大发送人数20")
    @NotEmpty(message = "目标id列表不能为空")
    @Size(max = 20, message = "单次最大发送人数20")
    private List<String> targetIds;

    /**
     * 发送者工号
     * 会话类型为单聊，群聊时 传递 发送人工号（必传）
     * 其他类型，暂不需要传值
     */
    @ApiModelProperty(value = "发送者工号", notes = "会话类型为单聊、群聊时必传")
    private String fromId;

    /**
     * 会话类型
     * 默认为 ordinaryBusiness，发送工作通知
     * single 单聊
     * group 群聊
     * officialAccount 公众号
     * ordinaryBusiness 普通业务号 如工作通知、会议等会话类型
     */
    @ApiModelProperty(value = "会话类型", notes = "默认为ordinaryBusiness")
    private String sessionType = "ordinaryBusiness";

    /**
     * 是否允许转发
     * 0：允许转发(默认允许转发)
     * 1：不允许转发
     */
    @ApiModelProperty(value = "是否允许转发", notes = "0：允许转发(默认)，1：不允许转发")
    private Integer forward = 0;
}
/* Ended by AICoder, pid:93c493e3b5j3bc5144530b5e40c2ab3126393225 */
