package com.zte.uedm.dcdigital.interfaces.web.material.controller;

import com.zte.uedm.dcdigital.common.web.BaseResult;
import org.springframework.stereotype.Controller;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;

@Controller
@Path("/aportal/material")
public class MaterialAportalController {

    @GET
    @Path("get-by-id")
    @Produces({ MediaType.APPLICATION_JSON })
    public BaseResult<Object> getById(@QueryParam("id") String id) {
        return BaseResult.success();
    }

}
