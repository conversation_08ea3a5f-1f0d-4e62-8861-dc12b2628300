package com.zte.uedm.dcdigital.interfaces.web.material.controller;

import com.zte.uedm.dcdigital.application.material.PageShareService;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.EmailShareRequestDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MessageShareRequestDto;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.ShareResponseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

/**
 * 页面分享控制器
 * 提供页面分享到不同渠道的REST接口
 *
 * <AUTHOR>
 */
@Controller
@Path("/uportal/page-share")
@Api(value = "页面分享接口", tags = {"页面分享"})
@Slf4j
/* Started by AICoder, pid:i194cse2a70ccf1149010bd0309dc46d8055b07f */
public class PageShareController {

    @Autowired
    private PageShareService pageShareService;

    /**
     * 分享页面到iCenter消息
     * 支持个人用户和群聊的消息分享
     *
     * @param requestDto 消息分享请求参数
     * @return 分享结果
     */
    @POST
    @Path("/message")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "分享页面到iCenter消息", notes = "支持个人用户和群聊的消息分享", httpMethod = "POST")
    public BaseResult<ShareResponseVo> shareToMessage(MessageShareRequestDto requestDto) {
        log.info("Received request to share page to message");

        try {
            ShareResponseVo result = pageShareService.shareToMessage(requestDto);

            if (result.isSuccess()) {
                return BaseResult.success(result);
            } else {
                return BaseResult.failed(result.getMessage());
            }

        } catch (Exception e) {
            log.error("Error occurred while sharing page to message", e);
            return BaseResult.failed("System error occurred while sharing to message");
        }
    }

    /**
     * 分享页面到邮件
     * 支持邮件分享
     *
     * @param requestDto 邮件分享请求参数
     * @return 分享结果
     */
    @POST
    @Path("/email")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "分享页面到邮件", notes = "支持邮件分享", httpMethod = "POST")
    public BaseResult<ShareResponseVo> shareToEmail(EmailShareRequestDto requestDto) {
        log.info("Received request to share page to email");

        try {
            ShareResponseVo result = pageShareService.shareToEmail(requestDto);

            if (result.isSuccess()) {
                return BaseResult.success(result);
            } else {
                return BaseResult.failed(result.getMessage());
            }

        } catch (Exception e) {
            log.error("Error occurred while sharing page to email", e);
            return BaseResult.failed("System error occurred while sharing to email");
        }
    }
}
/* Ended by AICoder, pid:i194cse2a70ccf1149010bd0309dc46d8055b07f */
