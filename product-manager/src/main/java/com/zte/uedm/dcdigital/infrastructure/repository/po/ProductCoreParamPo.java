package com.zte.uedm.dcdigital.infrastructure.repository.po;

/* Started by AICoder, pid:0699eufe29z19301460509091009fb7ab9c652d6 */
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.ToString;



@Getter
@Setter
@ToString
@NoArgsConstructor
@TableName("product_core_param")
public class ProductCoreParamPo extends Model<ProductCoreParamPo> {

    /**
     * id
     */
    private String id;

    /**
     * 产品小类id
     */
    @TableField("product_category_id")
    private String productCategoryId;

    /**
     * 产品分组L1
     */
    @TableField("param_group_l1")
    private String paramGroupL1;

    /**
     * 产品分组L2
     */
    @TableField("param_group_l2")
    private String paramGroupL2;

    /**
     * 主要性能参数
     */
    @TableField("main_param")
    private String mainParam;

    /**
     * 参数备注
     */
    @TableField("param_remark")
    private String paramRemark;

    /**
     * 删除标识
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建用户
     */
    private String createBy;

    /**
     * 更新用户
     */
    private String updateBy;
}
/* Ended by AICoder, pid:0699eufe29z19301460509091009fb7ab9c652d6 */
