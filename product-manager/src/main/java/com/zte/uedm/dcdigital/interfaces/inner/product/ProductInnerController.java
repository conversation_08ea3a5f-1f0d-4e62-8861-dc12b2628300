package com.zte.uedm.dcdigital.interfaces.inner.product;

import com.zte.uedm.dcdigital.application.brand.ProductBrandQueryService;
import com.zte.uedm.dcdigital.application.category.ProductCategoryQueryService;
import com.zte.uedm.dcdigital.common.bean.brand.ProductBrandInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryAndBrandInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.MaterialStatisticsQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductTreeVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.MaterialStatisticsVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductBrandVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductSubcategoryWithCategoryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Set;

@Path("/product-inner")
@Api(value = "Internal product calling interface")
@Controller
@Slf4j
public class ProductInnerController {

    @Autowired
    private ProductCategoryQueryService productCategoryQueryService;

    @Autowired
    private ProductBrandQueryService brandQueryService;

    @GET
    @Path("/select-by-ids")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据id列表查询", notes = "根据id列表查询", httpMethod = "GET")
    public BaseResult<List<ProductCategoryInfoVo>> queryByIds(@QueryParam("ids") List<String> ids) {
        log.info("id=={}",ids);
        List<ProductCategoryInfoVo> result = productCategoryQueryService.queryByIds(ids);
        log.info("result=={}",result);
        return BaseResult.success(result);
    }

    @GET
    @Path("/select-tree-by-ids")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据id列表查询", notes = "根据id列表查询", httpMethod = "GET")
    public BaseResult<List<ProductTreeVo>> queryTreeByIds(@QueryParam("ids") List<String> ids) {
        log.info("id=={}",ids);
        List<ProductTreeVo> result = productCategoryQueryService.queryTreeByIds(ids);
        log.info("result=={}",result);
        return BaseResult.success(result);
    }

    @POST
    @Path("/select-category-brand-by-ids")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "据产品小类id列表查询产品小类以及品牌信息", notes = "据产品小类id列表查询产品小类以及品牌信息", httpMethod = "POST")
    public BaseResult<List<ProductCategoryAndBrandInfoVo>> selectProductCategoryAndBrandList(List<String> ids) {
        log.info("id=={}",ids);
        List<ProductCategoryAndBrandInfoVo> result = productCategoryQueryService.selectProductCategoryAndBrandList(ids);
        log.info("result=={}",result);
        return BaseResult.success(result);
    }

    @POST
    @Path("/select-all-category-brand")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询所有产品小类及其品牌信息", notes = "查询所有产品小类及其品牌信息", httpMethod = "POST")
    public BaseResult<List<ProductCategoryAndBrandInfoVo>> selectALLProductCategoryAndBrandList() {
        List<ProductCategoryAndBrandInfoVo> result = productCategoryQueryService.selectALLProductCategoryAndBrandList();
        log.info("result=={}",result);
        return BaseResult.success(result);
    }

    @GET
    @Path("/select-brand-by-idName")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据产品小类id+品牌名称查询品牌信息", notes = "根据产品小类id+品牌名称查询品牌信息", httpMethod = "GET")
    public BaseResult<ProductBrandInfoVo> selectProductBrandInfo(@QueryParam("productCategoryId") String productCategoryId,@QueryParam("brandName") String brandName) {
        log.info("productCategoryId:{},brandName:{}",productCategoryId,brandName);
        ProductBrandInfoVo productBrandInfoVo = brandQueryService.selectProductBrandInfo(productCategoryId,brandName);
        log.info("result=={}",productBrandInfoVo);
        return BaseResult.success(productBrandInfoVo);
    }
    /* Started by AICoder, pid:fb4dafdcfd5f7931411e09beb039f3113d801eae */
    @GET
    @Path("/select-brand-list-by-id")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据产品小类id查询品牌列表信息", notes = "根据产品小类id查询品牌列表信息", httpMethod = "GET")
    public BaseResult<List<ProductBrandInfoVo>> selectProductBrandListByCategoryId(@QueryParam("productCategoryId") String productCategoryId) {
        log.info("selectProductBrandListByCategoryId productCategoryId:{}", productCategoryId);
        List<ProductBrandInfoVo> productBrandInfoVos = brandQueryService.selectProductBrandListByCategoryId(productCategoryId);
        log.info("result=={}", productBrandInfoVos);
        return BaseResult.success(productBrandInfoVos);
    }

    /* Ended by AICoder, pid:fb4dafdcfd5f7931411e09beb039f3113d801eae */
    @POST
    @Path("/query-by-path")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据路径查询产品小类", notes = "根据路径查询产品小类", httpMethod = "POST")
    public BaseResult<List<ProductCategoryInfoVo>> queryByPath(@QueryParam("pathList") Set<String> pathList) {
        log.info("id=={}",pathList);
        List<ProductCategoryInfoVo> result = productCategoryQueryService.queryByPath(pathList);
        return BaseResult.success(result);
    }

    @GET
    @Path("/select-productSe-by-categoryId")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据产品小类id查询对应产品se信息", notes = "根据产品小类id查询对应产品se信息", httpMethod = "GET")
    public BaseResult<List<String>> selectProductSeByCategoryId(@QueryParam("productCategoryId") String productCategoryId) {
        log.info("productCategoryId:{}",productCategoryId);
        List<String> productSeIds = productCategoryQueryService.selectProductSeByCategoryId(productCategoryId);
        log.info("result=={}",productSeIds);
        return BaseResult.success(productSeIds);
    }

    @GET
    @Path("/get-all-tree")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "树形结构展示所有的产品小类", notes = "树形结构展示所有的产品小类", httpMethod = "GET")
    public BaseResult<List<ProductTreeVo>> queryAllProductSubcategoryTree() {
        List<ProductTreeVo> result = productCategoryQueryService.getAllProductSubcategoryTree();
        return BaseResult.success(result);
    }
    /* Started by AICoder, pid:if153vdbc908ccb1475b09e9d06a5d1e88853f9f */
    @GET
    @Path("/selectByCategoryId")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据产品小类id查询所有下级", notes = "根据产品小类id查询所有下级", httpMethod = "GET")
    public BaseResult<List<String>> selectByCategoryId(@QueryParam("productCategoryId") String productCategoryId) {
        /**
         * 根据产品小类ID查询所有下级类别ID。
         *
         * @param productCategoryId 产品小类ID
         * @return 包含所有下级类别ID的 BaseResult 对象
         */
        List<String> productSeIds = productCategoryQueryService.selectByCategoryId(productCategoryId);
        productSeIds.add(productCategoryId); // 将当前类别ID添加到结果列表中
        return BaseResult.success(productSeIds);
    }
    /* Ended by AICoder, pid:if153vdbc908ccb1475b09e9d06a5d1e88853f9f */

    @GET
    @Path("/selectCategoryById")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据产品小类id查询小类", notes = "根据产品小类id查询小类", httpMethod = "GET")
    public BaseResult<ProductCategoryInfoVo> selectCategoryById(@QueryParam("productCategoryId") String productCategoryId) {

        ProductCategoryVo vo = productCategoryQueryService.queryProductCategoryById(productCategoryId);
        ProductCategoryInfoVo infoVo = new ProductCategoryAndBrandInfoVo();
        BeanUtils.copyProperties(vo, infoVo);
        return BaseResult.success(infoVo);
    }

    @GET
    @Path("/selectBrandById")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据品牌ID查询品牌", notes = "根据品牌ID查询品牌", httpMethod = "GET")
    public BaseResult<ProductBrandInfoVo> selectBrandById(@QueryParam("brandId") String brandId) {
        ProductBrandVo vo =  brandQueryService.getBrandAndTagDetail(brandId);
        ProductBrandInfoVo infoVo = new ProductBrandInfoVo();
        BeanUtils.copyProperties(vo, infoVo);
        return BaseResult.success(infoVo);
    }

    @POST
    @Path("/select-brand-list-by-ids")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据品牌ID列表查询品牌", notes = "根据品牌ID列表查询品牌", httpMethod = "POST")
    public BaseResult<List<ProductBrandInfoVo>> selectBrandListByIds(@QueryParam("brandIds") List<String> brandIds) {
        log.info("selectBrandListByIds ids=={}",brandIds);
        List<ProductBrandInfoVo> result = brandQueryService.selectBrandListByIds(brandIds);
        log.info("result=={}",result);
        return BaseResult.success(result);
    }

    /* Started by AICoder, pid:m45a8l08afv047214a260a399093563c7db33739 */
    @GET
    @Path("/selectByName")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据小类名称查询小类", notes = "根据小类名称查询小类", httpMethod = "GET")
    public BaseResult<List<ProductCategoryInfoVo>> selectByName(@QueryParam("categoryName") String categoryName) {
        /**
         * 根据小类名称查询小类。
         *
         * @param categoryName 小类名称
         * @return 包含小类信息的 ProductCategoryInfoVo 对象列表
         */
        List<ProductCategoryInfoVo> result = productCategoryQueryService.selectByName(categoryName);
        return BaseResult.success(result);
    }

    @GET
    @Path("/selectBrandByCategoryIdName")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据产品小类id品牌名称查询品牌信息,不存在则返回分数最低的品牌",
            notes = "根据产品小类id品牌名称查询品牌信息,不存在则返回分数最低的品牌", httpMethod = "GET")
    public BaseResult<List<ProductBrandInfoVo>> selectBrandByCategoryIdName(
            @QueryParam("productCategoryId") String productCategoryId,
            @QueryParam("brandName") String brandName) {
        /**
         * 根据产品小类ID和品牌名称查询品牌信息，如果不存在则返回分数最低的品牌。
         *
         * @param productCategoryId 产品小类ID
         * @param brandName         品牌名称
         * @return 包含品牌信息的 ProductBrandInfoVo 对象列表
         */
        List<ProductBrandInfoVo> productBrandInfoVo = brandQueryService.selectBrandByCategoryIdName(productCategoryId, brandName);
        return BaseResult.success(productBrandInfoVo);
    }
    /* Ended by AICoder, pid:m45a8l08afv047214a260a399093563c7db33739 */

    /* Started by AICoder, pid:6f68co39f5ld8d4147710ada405b3011d5b4198b */
    @GET
    @Path("/selectByNodeType")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据节点类型查询小类", notes = "根据节点类型查询小类", httpMethod = "GET")
    public BaseResult<List<ProductCategoryInfoVo>> selectByNodeType(@QueryParam("nodeType") String nodeType) {
        /**
         * 根据节点类型查询小类。
         *
         * @param nodeType 节点类型
         * @return 包含小类信息的 ProductCategoryInfoVo 对象列表
         */
        List<ProductCategoryInfoVo> result = productCategoryQueryService.selectByNodeType(nodeType);
        return BaseResult.success(result);
    }
    /* Ended by AICoder, pid:6f68co39f5ld8d4147710ada405b3011d5b4198b */
    /* Started by AICoder, pid:9bc03scde5ge9e91476d09b1d055114ca3c2ad4c */
    @POST
    @Path("/query-by-parentId")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据parentId获取其下级小类id", notes = "根据parentId获取其下级小类id", httpMethod = "POST")
    public BaseResult<List<ProductCategoryInfoVo>> getIdsByParentId(@QueryParam("parentId") String parentId) {
        List<ProductCategoryInfoVo> list = productCategoryQueryService.getIdsByParentId(parentId);
        return BaseResult.success(list);
    }

    @POST
    @Path("/getProductSubcategoryId")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取node_type为3的小类id", notes = "获取node_type为3的小类id", httpMethod = "POST")
    public BaseResult<List<String>> getProductSubcategoryId() {
        List<String> ids = productCategoryQueryService.getProductSubcategoryId();
        return BaseResult.success(ids);
    }
    /* Ended by AICoder, pid:9bc03scde5ge9e91476d09b1d055114ca3c2ad4c */

    @GET
    @Path("/query-subcategories-by-parent")
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据产品线id或产品大类id查询其底下所有子孙产品小类id及其名称",
                  notes = "根据产品线id或产品大类id查询其底下所有子孙产品小类id及其名称，响应包含小类id、小类名称、大类id、大类名称",
                  httpMethod = "GET")
    public BaseResult<List<ProductSubcategoryWithCategoryVo>> querySubcategoriesWithCategoryByParentId(@QueryParam("parentId") String parentId) {
        List<ProductSubcategoryWithCategoryVo> result = productCategoryQueryService.querySubcategoriesWithCategoryByParentId(parentId);
        return BaseResult.success(result);
    }

    /**
     * 根据产品小类ID列表和时间节点范围，统计每个产品小类在指定时间范围内的物料数据指标
     *
     * @param queryDto 查询参数，包含产品小类ID列表、时间范围和时间类型
     * @return 包含物料统计指标的结果列表
     */
    @POST
    @Path("/material-statistics-by-time-range")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据产品小类和时间范围统计物料数据指标",
                  notes = "根据产品小类ID列表和时间节点范围，统计每个产品小类在指定时间范围内的物料数据指标",
                  httpMethod = "POST")
    public BaseResult<List<MaterialStatisticsVo>> queryMaterialStatisticsByTimeRange(MaterialStatisticsQueryDto queryDto) {
        List<MaterialStatisticsVo> result = productCategoryQueryService.queryMaterialStatisticsByTimeRange(queryDto);
        return BaseResult.success(result);
    }
}
