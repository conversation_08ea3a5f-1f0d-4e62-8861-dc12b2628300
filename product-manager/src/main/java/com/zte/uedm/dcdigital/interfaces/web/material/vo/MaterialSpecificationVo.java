package com.zte.uedm.dcdigital.interfaces.web.material.vo;
/* Started by AICoder, pid:kf6abe8e779aa02144750af7005c494103e5777e */
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 物料详情支持规格书，用于存储物料的详细信息及其相关规格文件。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class MaterialSpecificationVo {

    /**
     * 生产编码，唯一标识一个物料。
     */
    private String productionCode;

    /**
     * 品牌名称，表示物料的品牌。
     */
    private String brandName;

    /**
     * 规格Word文件名，表示Word格式的规格文件名称。
     */
    private String specwordFileName;

    /**
     * 规格Word文件内容，存储Word格式的规格文件内容。
     */
    private String specwordFile;

    /**
     * 规格PDF文件名，表示PDF格式的规格文件名称。
     */
    private String specpdfFileName;

    /**
     * 规格PDF文件内容，存储PDF格式的规格文件内容。
     */
    private String specpdfFile;

}
/* Ended by AICoder, pid:kf6abe8e779aa02144750af7005c494103e5777e */