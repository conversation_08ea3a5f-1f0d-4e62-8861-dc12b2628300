/* Started by AICoder, pid:teb61b068abffae148380b1e30e67b256ec836be */
package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 用户数据传输对象，用于在不同层之间传递用户相关信息。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class UserDto {

    /**
     * 角色代码，表示用户的角色。
     */
    private String roleCode;

    /**
     * 用户ID集合，包含多个用户的ID。
     */
    private List<ManagerDto> userIds;

    /**
     * 用户工号
     */
    private String employeeId;
}
/* Ended by AICoder, pid:teb61b068abffae148380b1e30e67b256ec836be */