/* Started by AICoder, pid:z568ccecc3d473514a9a082041f16e5246f50f57 */
package com.zte.uedm.dcdigital.interfaces.web.material.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.enums.LectotypeTypeEnums;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductCategoryStatusCode;
import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 采购成本编辑数据传输对象 (DTO)。
 * 用于封装采购成本编辑请求的参数，并提供参数校验功能。
 */
@Getter
@Setter
@ToString
@Slf4j
public class ProcurementCostEditDto {

    /**
     * 选型单id，不能为空。
     *
     * @LogMark 注解标记此字段在添加和更新操作中需要记录日志。
     */
    @NotBlank(message = "选型单id不能为空")
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String lectotypeId;

    /**
     * 选型单类型，不能为空。1-招标 2-指定。
     *
     * @LogMark 注解标记此字段在添加和更新操作中需要记录日志。
     */
    @NotBlank(message = "选型单类型不能为空")
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String lectotypeType;

    /**
     * 洽谈价格，保留两位小数。
     * 仅在选型单类型为“指定”时更新。
     */
    private BigDecimal negotiatedPrice;

    /**
     * 基准目标价格，保留两位小数。
     * 仅在选型单类型为“招标”时更新。
     */
    private BigDecimal datumTargetPrice;

    /**
     * 挑战目标价价格，保留两位小数。
     * 仅在选型单类型为“招标”时更新。
     */
    private BigDecimal challengeTargetPrice;

    /**
     * 开标价，保留两位小数。
     * 仅在选型单类型为“招标”时更新。
     */
    private BigDecimal openTenderPrice;

    /**
     * 定标价，保留两位小数。
     * 仅在选型单类型为“招标”时更新。
     */
    private BigDecimal setBidPrice;

    /**
     * 发标时间。
     */
    private String bidIssuingTime;

    /**
     * 开标时间。
     */
    private String bidOpeningTime;

    /**
     * 参数校验方法。
     * 校验传入的参数是否合法，包括必填字段、选型单类型的有效性以及价格和时间格式的校验。
     */
    public void parameterVerification() {
        // 校验bean字段NotEmpty
        ValidResult validResult = ValidateUtils.validateObj(this);
        if (validResult.isError()) {
            log.error("[ProcurementCostAddDto check param] Invalid parameter: {}", validResult.getErrorMessage());
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }

        // 校验lectotypeType字段是否为1或2
        if (!LectotypeTypeEnums.contains(lectotypeType)) {
            log.error("Invalid parameter:{}: lectotypeType must be 1 or 2", lectotypeType);
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
        // 指定选型单时，更新洽谈价
        negotiatedPrice = verifyAndObtain(negotiatedPrice);
        if (LectotypeTypeEnums.ONE.type.equalsIgnoreCase(lectotypeType)) {
            // 招标选型单时更新其他价格
            datumTargetPrice = verifyAndObtain(datumTargetPrice);
            challengeTargetPrice = verifyAndObtain(challengeTargetPrice);
            openTenderPrice = verifyAndObtain(openTenderPrice);
            setBidPrice = verifyAndObtain(setBidPrice);
        }
        checkTime(bidIssuingTime);
        checkTime(bidOpeningTime);
    }

    /**
     * 检查时间格式是否有效。
     *
     * @param targetTime 需要检查的时间字符串
     */
    private void checkTime(String targetTime) {
        if (StringUtils.isNotBlank(targetTime) && !DateTimeUtils.isValidDateFormat(targetTime)) {
            log.error("targetTime:{} is not the agreed format.", targetTime);
            throw new BusinessException(ProductCategoryStatusCode.DATE_FORMAT_ERROR);
        }
    }

    /**
     * 检查并获取规定小数点的价格。
     *
     * @param tartPrice 需要检查的价格
     * @return 校验后的价格，保留两位小数
     */
    public BigDecimal verifyAndObtain(BigDecimal tartPrice) {
        // 校验价格
        if (tartPrice != null) {
            // 价格不小于零，且保留两位小数点
            if (tartPrice.compareTo(BigDecimal.ZERO) < 0) {
                log.error("ProcurementCostAddDto Invalid parameter: price must be greater than or equal to zero");
                throw new BusinessException(StatusCode.INVALID_PARAMETER);
            }
            // 价格保留两位小数
            return tartPrice.setScale(2, RoundingMode.HALF_UP);
        }
        return null;
    }
}
/* Ended by AICoder, pid:z568ccecc3d473514a9a082041f16e5246f50f57 */