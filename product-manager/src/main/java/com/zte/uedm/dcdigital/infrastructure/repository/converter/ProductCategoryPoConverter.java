/* Started by AICoder, pid:ra8d9bd0dem36c9145e50a69308e1781d5d54d4f */
package com.zte.uedm.dcdigital.infrastructure.repository.converter;

import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryExtraInfoEntity;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryExtraInfoPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * ProductCategory 数据转换器，用于在实体（Entity）和持久化对象（PO）之间进行转换。
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProductCategoryPoConverter {

    /**
     * 将ProductCategoryEntity转换为ProductCategoryPo，用于数据库存储。
     *
     * @param entity 原始数据实体
     * @return 转换后的持久化对象，如果输入为空，则返回null。
     */
    public static ProductCategoryPo convertToProductCategoryPo(ProductCategoryEntity entity) {
        if (entity == null) {
            return null;
        }
        ProductCategoryPo categoryPo = new ProductCategoryPo();
        BeanUtils.copyProperties(entity, categoryPo);
        log.debug("Converted ProductCategoryEntity to ProductCategoryPo: {}", categoryPo);
        return categoryPo;
    }

    /**
     * 将ProductCategoryEntity转换为ProductCategoryExtraInfoPo，用于数据库存储。
     *
     * @param entity 原始数据实体
     * @return 转换后的持久化对象，如果输入为空，则返回null。
     */
    public static ProductCategoryExtraInfoPo convertToProductCategoryExtraInfoPo(ProductCategoryEntity entity) {
        if (entity == null) {
            return null;
        }
        ProductCategoryExtraInfoPo extraInfoPo = new ProductCategoryExtraInfoPo();
        extraInfoPo.setProductCategoryId(entity.getId());
        extraInfoPo.setProductLevel(entity.getProductLevel());
        extraInfoPo.setProductComponent(entity.getProductComponent());
        log.debug("Converted ProductCategoryEntity to ProductCategoryExtraInfoPo: {}", extraInfoPo);
        return extraInfoPo;
    }

    /**
     * 将ProductCategoryPo转换为ProductCategoryEntity，从数据库读取数据时使用。
     *
     * @param categoryPo 原始持久化对象
     * @return 转换后的实体对象，如果输入为空，则返回null。
     */
    public static ProductCategoryEntity convertToProductCategoryEntity(ProductCategoryPo categoryPo) {
        if (categoryPo == null) {
            return null;
        }
        ProductCategoryEntity entity = new ProductCategoryEntity();
        BeanUtils.copyProperties(categoryPo, entity);
        log.debug("Converted ProductCategoryPo to ProductCategoryEntity: {}", entity);
        return entity;
    }

    /**
     * 将ProductCategoryExtraInfoPo转换为ProductCategoryExtraInfoEntity，从数据库读取数据时使用。
     *
     * @param extraInfoPo 原始持久化对象
     * @return 转换后的实体对象，如果输入为空，则返回null。
     */
    public static ProductCategoryExtraInfoEntity convertToProductCategoryExtraInfoEntity(ProductCategoryExtraInfoPo extraInfoPo) {
        if (extraInfoPo == null) {
            return null;
        }
        ProductCategoryExtraInfoEntity entity = new ProductCategoryExtraInfoEntity();
        BeanUtils.copyProperties(extraInfoPo, entity);
        log.debug("Converted ProductCategoryExtraInfoPo to ProductCategoryExtraInfoEntity: {}", entity);
        return entity;
    }
}
/* Ended by AICoder, pid:ra8d9bd0dem36c9145e50a69308e1781d5d54d4f */