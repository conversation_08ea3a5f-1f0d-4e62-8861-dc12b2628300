/* Started by AICoder, pid:29d53v52a3w262e1419f0bd6b04ce02dda4110d1 */
package com.zte.uedm.dcdigital.domain.repository;

import com.zte.uedm.dcdigital.domain.model.material.entity.MaterialTemporaryEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.MaterialQueryBean;

import java.util.List;

/**
 * 临时物料信息的仓储接口。
 * 该接口定义了对临时物料信息进行查询的操作。
 *
 * <AUTHOR>
 */
public interface MaterialTemporaryRepository {

    /* Started by AICoder, pid:7e67fg47dde2ab6147e20b2ed0a5c621a001eb5e */
    /**
     * 根据物料IDs查询临时物料信息。
     *
     * @param materialIds 物料ID列表，用于查询具体的物料信息
     * @return 包含具体物料信息的实体对象列表，如果未找到任何记录，则返回空列表
     */
    List<MaterialTemporaryEntity> queryMaterialByIds(List<String> materialIds);

    /**
     * 通过临时物料ids删除临时表信息。
     *
     * @param tempMaterialIds 要删除的物料ID列表
     */
    void deleteByMaterialIds(List<String> tempMaterialIds);

    /**
     * 通过临时表ids删除临时表信息。
     *
     * @param tempIds 要删除的物料ID列表
     */
    void deleteByIds(List<String> tempIds);


    /**
     * 连表查询物料详情
     * @param approvalId 审批id
     * @return
     */
    List<MaterialQueryBean> queryTempMaterialByApprovalId(String approvalId);



    /* Started by AICoder, pid:ue566ba2f88ca9a148c2083cb07fd40850573c9d */
    /**
     * 根据审批ID查询临时物料信息。
     *
     * @param approvalId 审批ID，不能为空或空字符串。
     * @return 包含临时物料信息的列表。如果未找到相关记录，返回一个空列表。
     */
    List<MaterialTemporaryEntity> queryMaterialTemporaryByApprovalId(String approvalId);
    /* Ended by AICoder, pid:ue566ba2f88ca9a148c2083cb07fd40850573c9d */

    /* Started by AICoder, pid:re526if851v6d6a14d85088e400ae30134a776d2 */
    /**
     * 检查给定的销售代码是否存在于临时物料信息中。
     *
     * @param pdmInfoId 需要检查的销售代码
     * @return 如果销售代码存在于临时物料中，返回true；否则返回false。
     */
    MaterialTemporaryEntity queryByPdmInfoId(String pdmInfoId);
    /* Ended by AICoder, pid:re526if851v6d6a14d85088e400ae30134a776d2 */

    void addTempMaterials(MaterialTemporaryEntity temporaryEntity);

    MaterialTemporaryEntity selectByMaterialId(String materialId);

    void editTempMaterials(MaterialTemporaryEntity temporaryEntity);
    /* Ended by AICoder, pid:7e67fg47dde2ab6147e20b2ed0a5c621a001eb5e */
    /* Started by AICoder, pid:n6f52zd9027919e1423c0ba5209abf3642d6f6d3 */
    /**
     * 根据材料ID查询临时材料记录。
     *
     * @param id 材料ID
     * @return MaterialTemporaryEntity 临时材料记录实体
     */
    MaterialTemporaryEntity queryTempMaterialByMaterialId(String id);

    /**
     * 添加一个新的临时材料记录。
     *
     * @param temporary 临时材料记录实体
     */
    void addTempMaterial(MaterialTemporaryEntity temporary);

    /**
     * 更新现有的临时材料记录。
     *
     * @param temporaryEntity 需要更新的临时材料记录实体
     */
    void updateTempMaterial(MaterialTemporaryEntity temporaryEntity);

    /**
     * 批量更新指定ID列表的临时材料记录的审批ID。
     *
     * @param tempIds 临时材料记录ID列表
     * @param approvalId 新的审批ID
     */
    void batchUpdateApprovalIdById(List<String> tempIds, String approvalId);

    /**
     * 批量添加多个临时材料记录。
     *
     * @param temporaryEntities 临时材料记录实体列表
     */
    void batchAddMaterialTemporary(List<MaterialTemporaryEntity> temporaryEntities);
    /* Ended by AICoder, pid:n6f52zd9027919e1423c0ba5209abf3642d6f6d3 */

    /* Started by AICoder, pid:q54d9effc6jcbee14c20095e003d490d14b74a91 */
    /**
     * 根据组ID查询临时物料信息。
     *
     * @param oldGroupId 组ID
     * @return 包含临时物料信息的列表
     */
    List<MaterialTemporaryEntity> queryByGroupId(String oldGroupId);
    /* Ended by AICoder, pid:q54d9effc6jcbee14c20095e003d490d14b74a91 */

    /* Started by AICoder, pid:cca785a4b7if04f148d608c6f0e6200cd656de80 */
    /**
     * 批量更新临时物料信息。
     *
     * @param materialTempEntities 临时物料信息列表
     */
    void updateBatchMaterials(List<MaterialTemporaryEntity> materialTempEntities);

    /* Started by AICoder, pid:5c51am667fsaeac147dd0a96e0a1f004b7277f70 */
    /**
     * 根据提供的PDM信息ID列表批量查询材料临时实体。
     *
     * @param pdmInfoIds 一个包含PDM信息ID的列表，用于指定要查询的记录。
     * @return List<MaterialTemporaryEntity> 返回匹配的材料临时实体列表，如果没有找到任何记录则返回空列表。
     */
    List<MaterialTemporaryEntity> batchQueryByPdmInfoIds(List<String> pdmInfoIds);

    /* Started by AICoder, pid:e0433lc4a6w150814a940a34c083a40badd9ea2e */
    /**
     * 根据PDM信息ID、品牌和物料ID查询临时物料实体列表。
     *
     * @param id         PDM信息ID，用于标识特定的PDM信息。
     * @param brand      品牌名称，用于过滤特定品牌的记录。
     * @param materialId 物料ID，用于进一步筛选具体的物料记录。
     * @return List<MaterialTemporaryEntity> 包含符合条件的临时物料实体列表。
     */
    List<MaterialTemporaryEntity> queryByPdmInfoIdAndBrand(String id, String brand, String materialId);
    /* Ended by AICoder, pid:e0433lc4a6w150814a940a34c083a40badd9ea2e */
    /* Ended by AICoder, pid:5c51am667fsaeac147dd0a96e0a1f004b7277f70 */
    /* Ended by AICoder, pid:cca785a4b7if04f148d608c6f0e6200cd656de80 */
}
/* Ended by AICoder, pid:29d53v52a3w262e1419f0bd6b04ce02dda4110d1 */