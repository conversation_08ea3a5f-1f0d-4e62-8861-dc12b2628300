package com.zte.uedm.dcdigital.interfaces.web.material.vo;

/* Started by AICoder, pid:fdf861e297393b8147a4081aa058858a2962dec2 */

import com.zte.uedm.dcdigital.common.bean.document.FileInfoVo;
import com.zte.uedm.dcdigital.domain.common.enums.LectotypeStatusEnums;
import com.zte.uedm.dcdigital.domain.common.enums.LectotypeTypeEnums;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * DemandManagementLectotypePo 类表示需求管理的定标类型持久化对象。
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class DemandManagementLectotypeVo {

    /**
     * lectotypeId 属性表示定标类型的唯一标识符。
     */
    private String lectotypeId;

    /**
     * demandId 属性表示需求的ID。
     */
    private String demandId;

    /**
     * lectotypeName 属性表示定标类型的名称。
     */
    private String lectotypeName;

    /**
     * lectotypeType 属性表示定标类型的类型。
     */
    private String lectotypeType;
    private String lectotypeTypeName;

    /**
     * lectotypeStatus 属性表示定标类型的状态。
     */
    private Integer lectotypeStatus;
    private String lectotypeStatusName;

    /**
     * sendBidTime 属性表示发送投标的时间。
     */
    private String sendBidTime;

    /**
     * endTime 属性表示结束时间。
     */
    private String endTime;

    /**
     * open_bidTime 属性表示开标时间。
     */
    private String openBidTime;

    /**
     * bidUrl 属性表示投标的URL。
     */
    private String bidUrl;

    /**
     * 洽谈价格，保留两位小数。
     * 仅在选型单类型为“指定”时更新。
     */
    private BigDecimal negotiatedPrice;

    /**
     * 基准目标价格，保留两位小数。
     * 仅在选型单类型为“招标”时更新。
     */
    private BigDecimal datumTargetPrice;

    /**
     * 挑战目标价价格，保留两位小数。
     * 仅在选型单类型为“招标”时更新。
     */
    private BigDecimal challengeTargetPrice;

    /**
     * 开标价，保留两位小数。
     * 仅在选型单类型为“招标”时更新。
     */
    private BigDecimal openTenderPrice;

    /**
     * 定标价，保留两位小数。
     * 仅在选型单类型为“招标”时更新。
     */
    private BigDecimal setBidPrice;

    /**
     * createBy 属性表示创建者。
     */
    private String createBy;
    private String createByName;

    /**
     * createTime 属性表示创建时间。
     */
    private String createTime;

    /**
     * updateBy 属性表示更新者。
     */
    private String updateBy;
    private String updateByName;

    /**
     * updateTime 属性表示更新时间。
     */
    private String updateTime;

    private String fileIds;
    private List<FileInfoVo> fileList;



    public void setLectotypeType(String lectotypeType) {
        this.lectotypeType = lectotypeType;
        if (lectotypeType != null) {
            LectotypeTypeEnums lectotypeTypeEnums = LectotypeTypeEnums.lookFor(lectotypeType);
            this.lectotypeTypeName = lectotypeTypeEnums == null ? "" : lectotypeTypeEnums.name;
        }
    }

    public void setLectotypeStatus(Integer lectotypeStatus) {
        this.lectotypeStatus = lectotypeStatus;
        if (lectotypeStatus != null) {
            LectotypeStatusEnums lectotypeStatusEnums = LectotypeStatusEnums.lookFor(lectotypeStatus);
            this.lectotypeStatusName = lectotypeStatusEnums == null ? "" : lectotypeStatusEnums.name;
        }
    }
}
/* Ended by AICoder, pid:fdf861e297393b8147a4081aa058858a2962dec2 */
