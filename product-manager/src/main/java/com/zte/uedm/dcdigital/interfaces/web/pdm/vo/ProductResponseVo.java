package com.zte.uedm.dcdigital.interfaces.web.pdm.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

/* Started by AICoder, pid:yb3d7fa2a6yb45c14dd908ea2147a41294b2e421 */
@Getter
@Setter
@ToString
public class ProductResponseVo {

    private Code code;

    @JsonDeserialize(using = BoDeserializer.class)
    private Object bo;

    private Other other;

    @Getter
    @Setter
    @ToString
    public static class Code {
        private String code;
        private String msgId;
        private String msg;
    }

    public List<BO> getBoList() {
        if (bo instanceof List) {
            return (List<BO>) bo;
        }
        return Collections.emptyList();
    }

    @Getter
    @Setter
    @ToString
    public static class BO {
        private String memo;
        private String companyNos;
        private String tenantId;
        private String orgId;
        private String description;
        private String ownerDomain;
        private String innerClassId;
        private String innerClass;
        private String busiCtlSW;
        private String prodState;
        private String parentNo;
        private String code;
        private String marketingName;
        @JsonProperty("ITEM_NO")
        private String itemNo;
        @JsonProperty("CN_Name")
        private String cnName;
        @JsonProperty("ITEM_LEVEL")
        private Integer itemLevel;
        @JsonProperty("STATUS")
        private String status;
        @JsonProperty("ENABLED_FLAG")
        private String enabledFlag;
        @JsonProperty("EN_NAME")
        private String enName;
        @JsonProperty("EN_NAME_AB")
        private String enNameAb;
        @JsonProperty("CREATED_NAME")
        private String createdName;
        @JsonProperty("CREATED_BY")
        private String createdBy;
        @JsonProperty("CREATION_DATE")
        private String creationDate;
        @JsonProperty("LAST_UPDATED_BY")
        private String lastUpdatedBy;
        @JsonProperty("LAST_UPDATE_DATE")
        private String lastUpdateDate;
        @JsonProperty("PRODUCT_MODEL")
        private String productModel;
        @JsonProperty("INNER_MODEL")
        private String innerModel;
        @JsonProperty("PROD_PURPOSE_CODE")
        private String prodPurposeCode;
        @JsonProperty("PROD_PURPOSE_NAME")
        private String prodPurposeName;
        @JsonProperty("IS_LIKE_MOBILE")
        private String isLikeMobile;
        @JsonProperty("INNER_CLASS_EN_AB")
        private String innerClassEnAb;
    }

    public static class BoDeserializer extends JsonDeserializer<List<BO>> {

        @Override
        public List<ProductResponseVo.BO> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            JsonNode node = p.getCodec().readTree(p);
            ObjectMapper objectMapper = (ObjectMapper) p.getCodec();

            if (node.isArray()) {
                // 如果是数组，使用 readValue 方法并传递 TypeReference
                return objectMapper.readValue(node.traverse(), new TypeReference<List<ProductResponseVo.BO>>() {});
            } else if (node.isObject()) {
                // 如果是单个对象，创建一个包含该对象的列表
                ProductResponseVo.BO bo = objectMapper.treeToValue(node, ProductResponseVo.BO.class);
                return Collections.singletonList(bo);
            }
            return Collections.emptyList();
        }
    }

    @Getter
    @Setter
    @ToString
    public static class Other {
        private int current;
        private int pageCount;
        private int total;
        private int pageSize;
    }
}
/* Ended by AICoder, pid:yb3d7fa2a6yb45c14dd908ea2147a41294b2e421 */