package com.zte.uedm.dcdigital.application.category.impl;


import com.zte.uedm.common.exception.UedmException;
import com.zte.uedm.common.util.JsonUtils;
import com.zte.uedm.component.kafka.producer.constants.KafkaTopicOptional;
import com.zte.uedm.component.kafka.producer.service.KafkaSenderService;
import com.zte.uedm.dcdigital.application.category.RequirementDashboardCommandService;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.utils.OperationLogUtils;
import com.zte.uedm.dcdigital.domain.service.RequirementDashboardDomainService;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.RequirementDashboardAddDto;
import com.zte.uedm.dcdigital.log.domain.bean.OperationLogBean;
import com.zte.uedm.dcdigital.log.domain.bean.OperationResultOptional;
import com.zte.uedm.dcdigital.log.domain.bean.OperationTypeOptional;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationLogRankEnum;
import com.zte.uedm.dcdigital.sdk.process.service.ProcessService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RequirementDashboardCommandServiceImpl implements RequirementDashboardCommandService {

    //看板信息状态"关闭"权限码
    private static final Integer CLOSED_STATUS = 1;
    @Autowired
    private RequirementDashboardDomainService dashboardDomainService;

    @Autowired
    private KafkaSenderService kafkaSenderService;

    @Autowired
    private AuthService authService;

    @Autowired
    private ProcessService processService;

    @Override
    public void editDashboard(RequirementDashboardAddDto dashboardAddDto) {
        String result = OperationResultOptional.OPERATION_RESULT_SUCCESS.getId();
       try {
           dashboardDomainService.editDashboard(dashboardAddDto);
           senLogMessage(dashboardAddDto,StringUtils.EMPTY,result);
       } catch (Exception e) {
           result = OperationResultOptional.OPERATION_RESULT_FAIL.getId();
           senLogMessage(dashboardAddDto,e.getMessage(),result);
       }
       if (CLOSED_STATUS.equals(dashboardAddDto.getStatus())){
           processService.cancelMktApproval(dashboardAddDto.getProjectId(), dashboardAddDto.getProductCategoryId());
       }
    }

    private void senLogMessage(RequirementDashboardAddDto dashboardAddDto, String errorMessage, String result) {
        try{
            String userId = authService.getUserId();
            String detail = buildLogDetail(dashboardAddDto);
            OperationLogBean operationLogBean = OperationLogUtils.buildOperationLogBean(GlobalConstants.OPERATION_KANBAN_BOARD, OperationTypeOptional.OPERATION_TYPE_UPDATE,
                    userId, result, detail, OperationLogRankEnum.IMPORTANT.getId());
            log.info("operation log:{}",operationLogBean);
            if (OperationResultOptional.OPERATION_RESULT_SUCCESS.getId().equals(result)) {
                sendKafkaMessage(operationLogBean);
            } else {
                sendFailLogMessage(operationLogBean, errorMessage);
            }
        } catch (Exception e) {
            log.error("send log message failed", e);
        }
    }
    private void sendFailLogMessage(OperationLogBean operationLogBean, String errorMessage) {
        String[] args = new String[]{errorMessage};
        operationLogBean.setFailReason(I18nUtil.getI18nWithArgs(GlobalConstants.OPERATION_FAIL_LOG, args));
        sendKafkaMessage(operationLogBean);
    }

    @Override
    public void sendKafkaMessage(OperationLogBean operationLogBean) {
        try {
            log.debug("batch operation, :operationLogBean={}!", operationLogBean);
            kafkaSenderService.send(KafkaTopicOptional.KAFKA_TOPIC_OTCP_LOG_MANAGE, operationLogBean);
        } catch (Exception e) {
            log.error("log Message send failed for ", e);
        }
    }
    public String buildLogDetail(RequirementDashboardAddDto object) {
        JsonUtils jsonUtils = JsonUtils.getInstance();
        try {
            return jsonUtils.objectToJson(object);
        } catch (UedmException e) {
            log.error("convert error",e);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public void sendLog(String operation, OperationTypeOptional operationType,   String detail ){
        try{
            String userId = authService.getUserId();
            OperationLogBean operationLogBean = OperationLogUtils.buildOperationLogBean(operation,
                   operationType,
                    userId, OperationResultOptional.OPERATION_RESULT_SUCCESS.getId(), detail, OperationLogRankEnum.IMPORTANT.getId());
            sendKafkaMessage(operationLogBean);
        } catch (Exception e) {
            log.error("send log message failed", e);
        }
    }
}
