package com.zte.uedm.dcdigital.interfaces.web.pdm.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/* Started by AICoder, pid:j91fc95721u74891463c0a3120f1c051e580cf44 */
@Getter
@Setter
@ToString
public class MaterialVo {


    /**
     * PDM物料编号
     */
    private String partNo;

    /**
     * PDM物料名称
     */
    private String partCnName;

    /**
     * 销售代码
     */
    private String salesCode;

    /**
     * 生产代码
     */
    private String productionCode;

    /**
     * 销售状态
     */
    private String salesStatus;

    /**
     * 单位
     */
    private String unit;

    /**
     * 描述
     */
    private String description;

    /**
     * 成本
     */
    private String cost;

    public MaterialVo(){}

    public MaterialVo(String partNo, String partCnName, String salesCode, String productionCode, String salesStatus, String unit, String description, String cost) {
        this.partNo = partNo;
        this.partCnName = partCnName;
        this.salesCode = salesCode;
        this.productionCode = productionCode;
        this.salesStatus = salesStatus;
        this.unit = unit;
        this.description = description;
        this.cost = cost;
    }
}
/* Ended by AICoder, pid:j91fc95721u74891463c0a3120f1c051e580cf44 */
