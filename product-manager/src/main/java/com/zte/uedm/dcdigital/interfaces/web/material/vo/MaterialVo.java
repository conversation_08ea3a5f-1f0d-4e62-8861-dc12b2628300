/* Started by AICoder, pid:r53833c5963e5a4144000a7d11fc442a4d51c425 */
package com.zte.uedm.dcdigital.interfaces.web.material.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 物料信息传输对象，用于在Web层传输物料的详细信息。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class MaterialVo {

    /**
     * 物料ID，唯一标识每个物料。
     */
    private String id;

    /**
     * 审批表ID，关联特定的审批流程。
     */
    private String approvalId;

    /**
     * 物料名称/PDM名称，描述物料的名称。
     */
    private String name;
    private String nameEn;

    /**
     * 品牌，表示物料的品牌信息。
     */
    private String brand;

    /**
     * 供应商，表示提供物料的供应商信息。
     */
    private String supplier;

    /**
     * 采购模式，表示物料的采购方式。
     */
    private String purchaseMode;
    /**
     * 采购模式的枚举id。
     */
    private String purchaseModeId;

    /**
     * 失效日期，表示物料的有效期截止日期。
     */
    private String expirationDate;

    /**
     * 保质期，表示物料的保质期限。
     */
    private String warrantyPeriod;

    /**
     * 成本费用，表示物料的成本价格。
     */
    private String cost;

    /**
     * 交期，表示物料的交付时间。
     */
    private String deliveryDays;

    /**
     * 产品小类ID，用于将物料分类管理。
     */
    private String productId;

    /**
     * 产品小类路径，表示产品小类的完整路径。
     */
    private String productPathName;

    /**
     * 分组ID，用于将物料分组管理。
     */
    private String groupId;

    /**
     * 分组路径，表示分组的完整路径。
     */
    private String groupPathName;

    /**
     * PDM的ID，关联PDM系统中的物料信息。
     */
    private String pdmInfoId;

    /**
     * 非PDM的id，关联非PDM系统中的物料信息。
     */
    private String othInfoId;

    /**
     * 物料状态，表示物料的当前状态（例如：草稿、已上架等）。
     */
    private String materialStatus;
    /**
     * 物料状态名称。
     */
    private String materialStatusName;
    /**
     * 关联的文档id
     */
    private String[] documentIds;

    /**
     * 版本，表示物料的版本信息。
     */
    private String version;

    /**
     * 销售代码，表示物料的销售代码。
     */
    private String salesCode;

    /**
     * 生产代码，表示物料的生产代码。
     */
    private String productionCode;

    /**
     * 销售状态，表示物料的销售状态。
     */
    private String salesStatus;

    /**
     * 单位，表示物料的计量单位。
     */
    private String unit;
    private String unitEn;

    /**
     * 描述，表示物料的详细描述。
     */
    private String description;

    /**
     * 描述，关联选型单的状态。
     */
    private String lectotypeStatus;

    /**
     * 服务
     */
    private String service;

    /**
     * 规格型号：表示产品的具体规格或型号，例如产品版本或技术规格。
     */
    private String specificationModel;

    /**
     * 推荐等级：从A、B、C三个等级中选择其一
     */
    private String recommendedLevel;


    /**
     * 物料规格书备注。
     */
    private String specificationRemark;

    private String updateTime;

}
/* Ended by AICoder, pid:r53833c5963e5a4144000a7d11fc442a4d51c425 */