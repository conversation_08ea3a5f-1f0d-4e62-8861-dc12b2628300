/* Started by AICoder, pid:o1786ue6a36c5c114b320b7e805b366fd796e507 */
package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * DashboardConditionQueryDto 类用于表示仪表板条件查询的数据传输对象。
 * 该类包含项目ID、状态、分页参数等字段，并提供验证方法以确保数据的有效性。
 */
@Getter
@Setter
@ToString
public class DashboardConditionQueryDto {

    /**
     * 项目ID，标识具体的项目。此字段是必需的。
     */
    @NotBlank(message = "项目ID是必须的")
    private String projectId;

    /**
     * 状态，表示项目的当前状态。0正常，1-取消
     */
    private Integer status;

    /**
     * 分页参数：页码，默认为1。
     */
    private Integer pageNum;

    /**
     * 分页参数：每页大小，默认为10。
     */
    private Integer pageSize;

    /**
     * 验证方法，确保所有必需字段都已正确填写。
     * 如果验证失败，将抛出业务异常。
     * 同时，设置默认的分页参数值。
     */
    public void validate() {
        // 校验bean字段
        ValidResult validResult = ValidateUtils.validateObj(this);
        if (validResult.isError()) {
            throw new BusinessException(StatusCode.INVALID_PARAMETER.getCode(), validResult.getErrorMessage());
        }
        // 设置默认分页参数
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        if (!GlobalConstants.ONE.equals(status)) {
            status = GlobalConstants.ZERO;
        }
    }
}
/* Ended by AICoder, pid:o1786ue6a36c5c114b320b7e805b366fd796e507 */