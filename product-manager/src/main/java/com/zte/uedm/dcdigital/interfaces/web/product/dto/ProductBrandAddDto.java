package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import com.zte.uedm.dcdigital.security.annotation.DcResourceField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
@Getter
@Setter
@ToString
@ApiModel(description = "新增产品品牌数据传输对象")
public class ProductBrandAddDto {

    /**
     * 品牌主键。
     */
    @ApiModelProperty(value = "品牌主键", example = "123e4567")
    private String id;

    /**
     * 产品小类主键。
     */
    @NotNull(message = "产品小类id不能为空")
    @ApiModelProperty(value = "产品小类主键", example = "123e4567-e89b-12d3-a456-426655440000")
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    @DcResourceField
    private String productCategoryId;

    /**
     * 品牌名称。
     */
    @NotNull(message = "品牌名称不能为空")
    @ApiModelProperty(value = "品牌名称", example = "Apple")
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    private String brandName;

    /**
     * 标签名称。
     */
    @ApiModelProperty(value = "标签名称", example = "High Quality")
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    private String tagName;

    /**
     * 选型属性，0表示优选，1表示其它。
     */
    @NotNull(message = "选型属性不能为空")
    @ApiModelProperty(value = "选型属性，0表示优选，1表示其它", allowableValues = "0,1")
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    private Integer selectionAttribute;

    /**
     * 选型评分(1-10,保留一位小数)。
     */
    @NotNull(message = "选型评分不能为空")
    @DecimalMin(value = "1.0", inclusive = true, message = "选型评分不能小于1.0")
    @DecimalMax(value = "10.0", inclusive = true, message = "选型评分不能大于10.0")
    @Digits(integer = 2, fraction = 1, message = "选型评分只能有一位小数")
    @ApiModelProperty(value = "选型评分", example = "10.0")
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    private BigDecimal selectionScore;

    /**
     * 采购模式，1:自研、2:战略采购、3:项目采购、4:框架采购。
     */
    @ApiModelProperty(value = "采购模式，1:自研、2:战略采购、3:项目采购、4:框架采购")
    private String procurementMode;

    /**
     * 失效日期，格式为YYYY-MM-DD。
     */
    @ApiModelProperty(value = "失效日期，格式为YYYY-MM-DD")
    private String expiryDate;

    /**
     * 被引用的业务ID，关联到具体的业务。
     */
    @ApiModelProperty(value = "被引用的业务ID")
    private String quoteId;

    /**
     * 创建时间，记录品牌创建的时间。
     */
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    /**
     * 更新时间，记录品牌最后更新的时间。
     */
    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    /**
     * 创建用户ID，记录创建品牌的用户。
     */
    @ApiModelProperty(value = "创建用户ID")
    private String createBy;

    /**
     * 更新用户ID，记录最后更新品牌的用户。
     */
    @ApiModelProperty(value = "更新用户ID")
    private String updateBy;

    /**
     * 文档id。
     */
    @ApiModelProperty(value = "文档id", example = "1,2,9")
    private String documentIds;

    /* Started by AICoder, pid:pb11fob8a6o194a141ff0a14408cb30e92f821ad */
    public void validate() {
        // 校验bean字段
        ValidResult validResult = ValidateUtils.validateObj(this);

        if (validResult.isError()) {
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
    }
    /* Ended by AICoder, pid:pb11fob8a6o194a141ff0a14408cb30e92f821ad */
}
