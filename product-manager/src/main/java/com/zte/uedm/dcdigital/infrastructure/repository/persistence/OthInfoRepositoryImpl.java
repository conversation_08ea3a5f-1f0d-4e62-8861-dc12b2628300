/* Started by AICoder, pid:72c9di79b7h143d149da0af9a020b96b17a2a9c2 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.dcdigital.domain.model.material.entity.OthInfoEntity;
import com.zte.uedm.dcdigital.domain.repository.OthInfoRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.OthInfoConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.OthInfoMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.OthInfoPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * Implementation of the OthInfoRepository interface.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OthInfoRepositoryImpl extends ServiceImpl<OthInfoMapper, OthInfoPo> implements OthInfoRepository {

    @Override
    public OthInfoEntity queryById(String id) {
        OthInfoPo othInfoPo = baseMapper.selectById(id);
        return OthInfoConvert.INSTANCE.othInfoPoToOthSalesCodeEntity(othInfoPo);
    }

    @Override
    public void deleteOthSalesCodeById(String othId) {
        this.removeById(othId);
    }

    @Override
    public void batchAdd(List<OthInfoEntity> othSalesCodeEntityList) {
        if (CollectionUtils.isNotEmpty(othSalesCodeEntityList)) {
            List<OthInfoPo> othInfoPos = OthInfoConvert.INSTANCE.othInfoEntityListToOthInfoPoList(othSalesCodeEntityList);
            this.saveBatch(othInfoPos); // Use saveBatch for batch insertion
        }
    }

    @Override
    public List<OthInfoEntity> queryOthSalesCodeBySalesCodeList(List<String> salesCodeList) {
        if (CollectionUtils.isEmpty(salesCodeList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OthInfoPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OthInfoPo::getSalesCode, salesCodeList);
        List<OthInfoPo> othInfoPos = this.baseMapper.selectList(wrapper);
        return OthInfoConvert.INSTANCE.othInfoPoListToOthInfoEntityList(othInfoPos);
    }

    @Override
    public void batchDeleteByIds(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            this.removeByIds(ids);
        }
    }

    /* Started by AICoder, pid:9c0cdaabc1ld20b1458309f2601ce93691b030d8 */
    @Override
    public OthInfoEntity queryBySalesCode(String salesCode) {
        LambdaQueryWrapper<OthInfoPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OthInfoPo::getSalesCode,salesCode);
        OthInfoPo othInfoPo = baseMapper.selectOne(wrapper);
        return OthInfoConvert.INSTANCE.othInfoPoToOthSalesCodeEntity(othInfoPo);
    }

    @Override
    public void addOthInfo(OthInfoEntity othInfoEntity) {
        OthInfoPo othInfoPo = OthInfoConvert.INSTANCE.othInfoEntityToOthInfoPo(othInfoEntity);
        baseMapper.insert(othInfoPo);
    }

    @Override
    public void editOthInfo(OthInfoEntity othInfoEntity) {
        OthInfoPo othInfoPo = OthInfoConvert.INSTANCE.othInfoEntityToOthInfoPo(othInfoEntity);
        baseMapper.updateById(othInfoPo);
    }

    @Override
    public OthInfoEntity queryOthByName(String name) {
        LambdaQueryWrapper<OthInfoPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OthInfoPo::getName,name);
        OthInfoPo othInfoPo = baseMapper.selectOne(wrapper);
        return OthInfoConvert.INSTANCE.othInfoPoToOthSalesCodeEntity(othInfoPo);
    }
    /* Ended by AICoder, pid:9c0cdaabc1ld20b1458309f2601ce93691b030d8 */
}
/* Ended by AICoder, pid:72c9di79b7h143d149da0af9a020b96b17a2a9c2 */