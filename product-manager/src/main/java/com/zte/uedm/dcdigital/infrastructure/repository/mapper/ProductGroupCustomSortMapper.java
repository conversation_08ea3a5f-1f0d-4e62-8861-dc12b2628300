/* Started by AICoder, pid:7ec34x1c4c57f5c148b20a5af0c9324234c95d82 */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupCustomSortPo;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface ProductGroupCustomSortMapper extends BaseMapper<ProductGroupCustomSortPo> {

    /**
     * 根据父节点ID查询排序信息
     *
     * @param parentId 父节点ID
     * @return 排序信息列表
     */
    List<ProductGroupCustomSortPo> findByParentId(@Param("parentId") String parentId);

    /**
     * 更新分组的排序序号
     *
     * @param id          分组自定义排序记录ID
     * @param serialNum   新的序号
     * @param updateTime  更新时间
     * @param updateBy    更新者
     */
    void updateSerialNumById(@Param("id") String id,
                             @Param("serialNum") Integer serialNum,
                             @Param("updateTime") String updateTime,
                             @Param("updateBy") String updateBy);

    /**
     * 批量插入新的排序记录
     *
     * @param productGroupCustomSortPos 排序信息PO对象列表
     */
    void batchInsertProductGroupCustomSort(@Param("list") List<ProductGroupCustomSortPo> productGroupCustomSortPos);

    /**
     * 删除指定父节点下的所有排序记录（用于重新排序等情况）
     *
     * @param parentId 父节点ID
     */
    void deleteByParentId(@Param("parentId") String parentId);

    /**
     *  根据分组id查询排序信息
     * @param groupIds 分组id列表
     * */
    List<ProductGroupCustomSortPo> findCustomSortByGroupIds(@Param("list") List<String> groupIds);
}

/* Ended by AICoder, pid:7ec34x1c4c57f5c148b20a5af0c9324234c95d82 */