package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupEntity;
import com.zte.uedm.dcdigital.domain.repository.ProductGroupRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ProductGroupConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductGroupMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupQueryDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.core.toolkit.StringPool.SINGLE_QUOTE;

@Slf4j
@Repository
public class ProductGroupRepositoryImpl extends ServiceImpl<ProductGroupMapper, ProductGroupPo> implements ProductGroupRepository {

    @Autowired
    private ProductGroupMapper productGroupMapper;

    @Override
    public ProductGroupEntity selectProductGroupById(String id) {
        ProductGroupPo productGroupPo = productGroupMapper.selectById(id);
        return ProductGroupConvert.INSTANCE.productGroupPoToProductGroupEntity(productGroupPo);
    }

    @Override
    public List<ProductGroupEntity> selectByNameAndProductCategoryId(String name,String productCategoryId) {
        List<ProductGroupPo> productGroupPos = productGroupMapper.selectByNameAndProductCategoryId(name,productCategoryId);
        return ProductGroupConvert.INSTANCE.listProductGroupPoToEntity(productGroupPos);
    }

    @Override
    public ProductGroupEntity saveOrUpdateProductGroup(ProductGroupEntity productGroupEntity, boolean isUpdate) {
        ProductGroupPo productGroupPo = ProductGroupConvert.INSTANCE.productGroupEntityToPo(productGroupEntity);
        log.info("ProductGroupRepositoryImpl saveOrUpdateProductGroup: productGroupPo = {}", JSON.toJSONString(productGroupPo));
        int result = 0;
        /* Started by AICoder, pid:93facn73e0f12fa144ed0983809d7b18ba729e6d */
        try {
            if (isUpdate) {
                result = productGroupMapper.updateById(productGroupPo);
            } else {
                result = productGroupMapper.insert(productGroupPo);
            }
            if (result > 0) {
                return ProductGroupConvert.INSTANCE.productGroupPoToProductGroupEntity(productGroupPo);
            }
        } catch (Exception e) {
            throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
        }
        /* Ended by AICoder, pid:93facn73e0f12fa144ed0983809d7b18ba729e6d */

        return null;
    }

    @Override
    public List<ProductGroupEntity> selectProductGroupByParentId(String id) {
        List<ProductGroupPo> productGroupPos = productGroupMapper.selectByParentId(id);
        return ProductGroupConvert.INSTANCE.listProductGroupPoToEntity(productGroupPos);
    }

    /* Started by AICoder, pid:t4775qf70ct30441406e0a7d3034c606fe08cb05 */
    @Override
    public int deleteProductGroupById(String id) {
        try {
            return productGroupMapper.deleteById(id);
        } catch (Exception e) {
            throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
        }
    }
    /* Ended by AICoder, pid:t4775qf70ct30441406e0a7d3034c606fe08cb05 */

    @Override
    public List<ProductGroupEntity> queryPorductGroups(ProductGroupQueryDto queryDto) {
        List<ProductGroupPo> productGroupPos = productGroupMapper.queryProductGroups(queryDto);
        return ProductGroupConvert.INSTANCE.listProductGroupPoToEntity(productGroupPos);
    }

    @Override
    public List<ProductGroupEntity> selectByIds(List<String> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids))
        {
            log.error("selectByIds ids null");
            return new ArrayList<>();
        }
        List<ProductGroupPo> productGroupPos = productGroupMapper.selectBatchIds(ids);
        return ProductGroupConvert.INSTANCE.listProductGroupPoToEntity(productGroupPos);
    }

    /* Started by AICoder, pid:5a057q1d4bp829c14e6708c18058420d5da5f0d5 */
    @Override
    public List<ProductGroupEntity> selectProductGroupAndMaterialByGroupId(String id) {
        List<ProductGroupPo> productGroupPos = productGroupMapper.selectProductGroupAndMaterialByGroupId(id);
        return ProductGroupConvert.INSTANCE.listProductGroupPoToEntity(productGroupPos);
    }
    /* Ended by AICoder, pid:5a057q1d4bp829c14e6708c18058420d5da5f0d5 */

    /* Started by AICoder, pid:k3eace50eb75cd11416d09d920eb2213d01155b4 */
    /**
     * 查询产品分组的详细信息。
     *
     * @param id 产品分组的唯一标识符
     * @return 包含产品分组详细信息的实体对象
     */
    @Override
    public ProductGroupEntity queryProductGroupDetail(String id) {
        ProductGroupPo productGroupPo = productGroupMapper.selectById(id);
        return ProductGroupConvert.INSTANCE.productGroupPoToProductGroupEntity(productGroupPo);
    }
    /* Ended by AICoder, pid:k3eace50eb75cd11416d09d920eb2213d01155b4 */

    @Override
    public ProductGroupEntity queryMaterialByGroupId(String parentId) {
        ProductGroupPo productGroupPo = productGroupMapper.selectMaterialApproveByGroupId(parentId);
        return ProductGroupConvert.INSTANCE.productGroupPoToProductGroupEntity(productGroupPo);
    }

    /* Started by AICoder, pid:j0e13rf8a100bdd141bb0952f04ad70abbc57d28 */
    @Override
    public List<ProductGroupEntity> selectCurentNodeAllChildNode(String id) {
        List<ProductGroupPo> productGroupPos = productGroupMapper.selectNodeAllChildNode(id);
        return ProductGroupConvert.INSTANCE.listProductGroupPoToEntity(productGroupPos);
    }
    /* Ended by AICoder, pid:j0e13rf8a100bdd141bb0952f04ad70abbc57d28 */

    @Override
    public List<ProductGroupEntity> selectChildNodeByParentId(String id) {
        List<ProductGroupPo> productGroupPos = productGroupMapper.selectChildNodeByParentId(id);
        return ProductGroupConvert.INSTANCE.listProductGroupPoToEntity(productGroupPos);
    }
    /* Started by AICoder, pid:iac33s0f80h80dd143500a55b0aa681e25102a5b */
    @Override
    public List<ProductGroupEntity> selectChildNodeByParentIdList(List<String> ids){
        List<ProductGroupPo> productGroupPos = productGroupMapper.selectChildNodeByParentIdList(ids);
        return ProductGroupConvert.INSTANCE.listProductGroupPoToEntity(productGroupPos);
    }

    @Override
    public ProductGroupEntity selectProductThreadNodeById(String id){
        return productGroupMapper.selectProductNodeById(id);
    }
    /* Ended by AICoder, pid:iac33s0f80h80dd143500a55b0aa681e25102a5b */
    /* Started by AICoder, pid:7a5389662fw5f2f141b20b86801082095d95b43e */
    @Override
    public void updateBatch(List<ProductGroupEntity> productGroupList) {
        List<ProductGroupPo> productGroupPos = ProductGroupConvert.INSTANCE.productGroupEntityListToPo(productGroupList);
        try{
            this.updateBatchById(productGroupPos);
        }catch (Exception e){
            throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
        }

    }
    /* Ended by AICoder, pid:7a5389662fw5f2f141b20b86801082095d95b43e */


    /* Started by AICoder, pid:704f3xc983dc580143610b2d0017f7067867363d */
    @Override
    public List<String> selectByProductCategoryId(String categoryId) {
        if (StringUtils.isBlank(categoryId)) {
            return Collections.emptyList();
        }
        return productGroupMapper.selectGroupIdByProductCategoryId(categoryId);
    }
    /* Started by AICoder, pid:n94588e113r5986148940aea801db511f4571a26 */
    @Override
    public ProductGroupEntity selectByNameAndLevel(String groupL1,String categoryId, Integer groupLevel) {
        LambdaQueryWrapper<ProductGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductGroupPo::getName,groupL1)
                .eq(ProductGroupPo::getProductCategoryId,categoryId)
                .eq(ProductGroupPo::getGroupLevel,groupLevel);
        ProductGroupPo productGroupPo = productGroupMapper.selectOne(queryWrapper);
        return ProductGroupConvert.INSTANCE.productGroupPoToProductGroupEntity(productGroupPo);
    }

    /* Started by AICoder, pid:o6dbfj3e20i5f80142e409e7003b6010c8e71207 */
    @Override
    public ProductGroupEntity selectByParentNameAndNameAndLevel(String groupL1, String groupL2, String categoryId,Integer groupLevel2) {
        if (StringUtils.isAnyBlank(groupL1,groupL2)) {
            return null;
        }
        String pathName = groupL1.concat(GlobalConstants.FORWARD_SLASH).concat(groupL2);
        LambdaQueryWrapper<ProductGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductGroupPo::getName,groupL2)
                .eq(ProductGroupPo::getProductCategoryId,categoryId)
                .eq(ProductGroupPo::getPathName,pathName)
                .eq(ProductGroupPo::getGroupLevel,groupLevel2);
        ProductGroupPo productGroupPo = productGroupMapper.selectOne(queryWrapper);
        return ProductGroupConvert.INSTANCE.productGroupPoToProductGroupEntity(productGroupPo);
    }
    /* Started by AICoder, pid:b32f4w6ad081170144270b16a080a10540b749d6 */
    @Override
    public List<String> selectCurrentNodesAllChildNode(List<String> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        return productGroupMapper.selectAllChildNodeId(groupIds);
    }

    @Override
    public List<String> queryingLeafNodeGroups(String categoryId) {
        if (StringUtils.isBlank(categoryId)) {
            return Collections.emptyList();
        }
        return productGroupMapper.queryingLeafNodeGroups(categoryId);
    }

    @Override
    public List<String> queryingLeafAllNodeGroups() {
        return productGroupMapper.queryingLeafAllNodeGroups();
    }

    /* Ended by AICoder, pid:b32f4w6ad081170144270b16a080a10540b749d6 */
    /* Ended by AICoder, pid:o6dbfj3e20i5f80142e409e7003b6010c8e71207 */
    /* Ended by AICoder, pid:n94588e113r5986148940aea801db511f4571a26 */
    /* Ended by AICoder, pid:704f3xc983dc580143610b2d0017f7067867363d */
}
