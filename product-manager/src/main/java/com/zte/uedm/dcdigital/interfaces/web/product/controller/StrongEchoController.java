package com.zte.uedm.dcdigital.interfaces.web.product.controller;
/* Started by AICoder, pid:32dbax71a6bbfdf141fa0a05d127ae554fb30d19 */
import com.zte.uedm.dcdigital.application.material.StrongEchoService;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

/**
 * StrongEchoController 类用于处理与强回声相关的 REST API。
 * 该类包含添加、更新、删除强回声及其评论的方法，以及查询强回声列表和评论树的方法。
 */
@Path("uportal/echo")
@Api(value = "echo")
@Controller
@Slf4j
public class StrongEchoController {

    @Autowired
    private StrongEchoService strongEchoService;

    /**
     * 添加强回声。
     *
     * @param strongEchoAddDto 包含强回声信息的 DTO 对象
     * @return 操作结果的 BaseResult 对象
     */
    @POST
    @Path("/add")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "回音强新增", notes = "回音强新增", httpMethod = "POST")
    public BaseResult<Object> addDemand(StrongEchoAddDto strongEchoAddDto) {
        strongEchoService.add(strongEchoAddDto); // 调用服务层方法添加需求
        return BaseResult.success(); // 返回成功结果
    }

    /**
     * 更新强回声。
     *
     * @param strongEchoUpdDto 包含强回声更新信息的 DTO 对象
     * @return 操作结果的 BaseResult 对象
     */
    @POST
    @Path("/upd")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "回音强编辑", notes = "回音强编辑", httpMethod = "POST")
    public BaseResult<Object> upd(StrongEchoUpdDto strongEchoUpdDto) {
        strongEchoService.upd(strongEchoUpdDto); // 调用服务层方法更新需求
        return BaseResult.success(); // 返回成功结果
    }

    /**
     * 删除强回声。
     *
     * @param id 强回声的唯一标识符
     * @return 操作结果的 BaseResult 对象
     */
    @POST
    @Path("/del")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "回音强删除", notes = "回音强删除", httpMethod = "POST")
    public BaseResult<Object> del(@QueryParam("id") String id) {
        strongEchoService.del(id); // 调用服务层方法删除需求
        return BaseResult.success(); // 返回成功结果
    }

    /**
     * 添加强回声评论。
     *
     * @param strongEchoCommentAddDto 包含评论信息的 DTO 对象
     * @return 操作结果的 BaseResult 对象
     */
    @POST
    @Path("/addComment")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "评论新增", notes = "评论新增", httpMethod = "POST")
    public BaseResult<Object> addComment(StrongEchoCommentAddDto strongEchoCommentAddDto) {
        strongEchoService.addComment(strongEchoCommentAddDto); // 调用服务层方法添加评论
        return BaseResult.success(); // 返回成功结果
    }

    /**
     * 更新强回声评论。
     *
     * @param strongEchoCommentUpdDto 包含评论更新信息的 DTO 对象
     * @return 操作结果的 BaseResult 对象
     */
    @POST
    @Path("/updComment")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "评论编辑", notes = "评论编辑", httpMethod = "POST")
    public BaseResult<Object> updComment(StrongEchoCommentUpdDto strongEchoCommentUpdDto) {
        strongEchoService.updComment(strongEchoCommentUpdDto); // 调用服务层方法更新评论
        return BaseResult.success(); // 返回成功结果
    }

    /**
     * 删除强回声评论。
     *
     * @param id 评论的唯一标识符
     * @return 操作结果的 BaseResult 对象
     */
    @POST
    @Path("/delComment")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "评论删除", notes = "评论删除", httpMethod = "POST")
    public BaseResult<Object> delComment(@QueryParam("id") String id) {
        strongEchoService.delComment(id); // 调用服务层方法删除评论
        return BaseResult.success(); // 返回成功结果
    }

    /**
     * 分页获取强回声列表。
     *
     * @param pageEchoDto 分页查询参数的 DTO 对象
     * @return 包含强回声列表的 BaseResult 对象
     */
    @POST
    @Path("/list")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "分页获取回音", notes = "分页获取回音", httpMethod = "POST")
    public BaseResult<Object> list(@Validated PageEchoDto pageEchoDto) {
        return BaseResult.success(strongEchoService.list(pageEchoDto)); // 返回成功结果
    }

    /**
     * 获取强回声的评论树。
     *
     * @param id 强回声的唯一标识符
     * @return 包含评论树的 BaseResult 对象
     */
    @GET
    @Path("/treeComment")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取评论树", notes = "获取评论树", httpMethod = "GET")
    public BaseResult<Object> treeComment(@QueryParam("id") String id) {
        return BaseResult.success(strongEchoService.treeComment(id)); // 返回成功结果
    }
}
/* Ended by AICoder, pid:32dbax71a6bbfdf141fa0a05d127ae554fb30d19 */