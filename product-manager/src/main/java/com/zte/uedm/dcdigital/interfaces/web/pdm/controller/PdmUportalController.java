package com.zte.uedm.dcdigital.interfaces.web.pdm.controller;


import com.zte.uedm.dcdigital.application.pdm.PdmApiQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.CategoryDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.MaterialDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.SpecModelDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.CategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.SpecModelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("/uportal/pdm")
@Controller
@Slf4j
@Api(tags = {"Pdm接口"})
public class PdmUportalController {


    /* Started by AICoder, pid:b8e95acc13x4a0d144380b4c20eb642a8de92516 */
    @Autowired
    private PdmApiQueryService pdmApiQueryService;

    @POST
    @Path("/product-large-category")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "查询PDM产品分类", notes = "查询PDM产品分类", httpMethod = "POST")
    public BaseResult<List<CategoryVo>> selectProductLargeCategory(CategoryDto dto) {
        List<CategoryVo> largeCategoryList = pdmApiQueryService.getLargeCategoryList(dto);
        return BaseResult.success(largeCategoryList);
    }

    @POST
    @Produces({MediaType.APPLICATION_JSON})
    @Path("/group/product-specification-model")
    @ApiOperation(value = "PDM分组型号规格", notes = "PDM分组型号规格", httpMethod = "POST")
    public BaseResult<PageVO<SpecModelVo>> selectSpecificationModel(SpecModelDto dto) {
        PageVO<SpecModelVo> pageVO = pdmApiQueryService.getSpecificationModel(dto);
        return BaseResult.success(pageVO);
    }



    @POST
    @Produces({MediaType.APPLICATION_JSON})
    @Path("/material/product-specification-model")
    @ApiOperation(value = "PDM物料型号规格", notes = "PDM物料型号规格", httpMethod = "POST")
    public BaseResult<PageVO<MaterialVo>> selectMaterialList(MaterialDto dto) {
        PageVO<MaterialVo> materialList = pdmApiQueryService.getMaterialList(dto);
        return BaseResult.success(materialList);
    }


    /* Ended by AICoder, pid:b8e95acc13x4a0d144380b4c20eb642a8de92516 */
}
