/* Started by AICoder, pid:je394vfc3a2662514c20082840703d1172d86454 */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductCategoryPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductSubcategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductSubcategoryWithCategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.MaterialStatisticsQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.MaterialStatisticsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品分类数据访问对象（Mapper）接口。
 * 该接口继承自MyBatis Plus的BaseMapper，用于对ProductCategoryPo实体进行数据库操作。
 * 它提供了基本的CRUD（创建、读取、更新、删除）功能，并且可以通过MyBatis Plus的扩展方法实现更复杂的查询和操作。
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductCategoryMapper extends BaseMapper<ProductCategoryPo> {

    List<ProductCategoryPo> selectByCondition(@Param("categoryName") String categoryName, @Param("nodeType") Integer nodeType, @Param("parentId") String parentId);

    List<ProductCategoryVo> queryProductSubclasses(@Param("ids") List<String> ids,@Param("nodeType") Integer nodeType);

    List<ProductSubcategoryVo> queryUserProductSubcategory(@Param("ids") List<String> ids, @Param("nodeType") Integer nodeType);

    /**
     * 根据当前id查询当前节点节点子子节点数据
     * @param currentNodeId 当前节点id
     * @return
     */
    List<ProductCategoryEntity> selectCurentNodeAllChildNode(String currentNodeId);

    int queryByIdAndName(@Param("id") String id, @Param("name") String name);

    List<ProductSubcategoryVo> queryAllProductSubcategory(@Param("nodeType") Integer nodeType);
    /* Started by AICoder, pid:hc86c8873bh6c8e1411f090ac0c5a109d9874bb4 */
    /**
     * 根据产品类别ID查询品牌信息。
     *
     * @param productCategoryId 产品类别ID
     * @return 品牌信息列表
     */
    List<String> selectByCategoryId(@Param("productCategoryId") String productCategoryId);
    /* Ended by AICoder, pid:hc86c8873bh6c8e1411f090ac0c5a109d9874bb4 */

    /* Started by AICoder, pid:nd147k3b2fg517314ca00a3d70155f08f2e83d6c */
    /**
     * 根据类别名称和节点类型查询子类别信息。
     *
     * @param categoryName 类别名称
     * @param nodeType     节点类型
     * @return 包含子类别信息的 ProductSubcategoryVo 对象列表
     */
    List<ProductSubcategoryVo> selectByName(@Param("categoryName") String categoryName, @Param("nodeType") Integer nodeType);

    /**
     * 查询所有产品分类列表。
     *
     * @return 包含所有产品分类信息的 ProductCategoryPo 对象列表
     */
    List<ProductCategoryPo> queryAllProductCategoryList();
    /* Ended by AICoder, pid:nd147k3b2fg517314ca00a3d70155f08f2e83d6c */

    /* Started by AICoder, pid:qe121o39detb3c714a0e0842602d1006b8f77b7a */
    /**
     * 根据节点类型查询产品子类别信息。
     *
     * @param nodeType 节点类型
     * @return 包含产品子类别信息的 ProductSubcategoryVo 对象列表
     */
    List<ProductSubcategoryVo> selectByNodeType(@Param("nodeType") Integer nodeType);

    /**
     * 根据产品线编码查询所有产品大类ID
     * @param productLineNo 产品线编码
     * */
    List<ProductCategoryPo> getAllBigProductCategoryByLineNo(String productLineNo);

    /**
     * 根据产品大类id列表查询所有产品小类信息(产品编号)
     * @param bigCategoryIds 产品大类id
     * */
    List<ProductCategoryPo> getAllSmallProductNoByBigId(List<String> bigCategoryIds);
    /* Ended by AICoder, pid:qe121o39detb3c714a0e0842602d1006b8f77b7a */
    /**
     * 根据parentID查询下级id列表。
     *
     * @param parentId parentID
     * @return 对象id列表
     */
    List<ProductCategoryInfoVo> selectIdsByParentId(@Param("parentId") String parentId);

    /**
     * 获取node_type为3的小类id
     * @return
     */
    List<String> getProductSubcategoryId();

    /**
     * 根据父级ID查询所有子孙产品小类及其所属大类信息
     *
     * @param parentId 父级ID（产品线ID或产品大类ID）
     * @return 产品小类及其所属大类信息列表
     */
    List<ProductSubcategoryWithCategoryVo> querySubcategoriesWithCategoryByParentId(@Param("parentId") String parentId);

    /**
     * 根据产品小类ID列表和时间节点范围，统计每个产品小类在指定时间范围内的物料数据指标
     *
     * @param queryDto 查询参数，包含产品小类ID列表、时间范围和时间类型
     * @param timePoints 完整的时间点列表
     * @return 包含物料统计指标的结果列表
     */
    List<MaterialStatisticsVo> queryMaterialStatisticsByTimeRange(
            @Param("queryDto") MaterialStatisticsQueryDto queryDto,
            @Param("timePoints") List<String> timePoints);
}
/* Ended by AICoder, pid:je394vfc3a2662514c20082840703d1172d86454 */