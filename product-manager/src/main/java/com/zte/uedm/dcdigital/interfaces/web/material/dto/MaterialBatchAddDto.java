/* Started by AICoder, pid:z69236350cb1f791430409afa05ead5a1e83a5ae */
package com.zte.uedm.dcdigital.interfaces.web.material.dto;

import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import javax.validation.constraints.NotNull;

/**
 * MaterialBatchAddDto 类用于表示批量添加材料时的数据传输对象（DTO）。
 * 该类包含材料的各种属性，并使用 Lombok 注解来自动生成 getter 和 setter 方法以及 toString 方法。
 */
@Getter
@Setter
@ToString
public class MaterialBatchAddDto {

    /**
     * 物料名称：表示产品的物料名称。
     */
    @NotNull(message = "物料名称不能为空")
    private String materialName;

    /**
     * 销售代码
     */
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    private String salesCode;

    /**
     * 生产代码：表示产品的生产代码。
     */
    private String productionCode;
    /**
     * 采购模式
     */
    @NotNull(message = "采购模式不能为空")
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    private String purchaseMode;

    /**
     * 过期日期
     */
    private String expirationDate;

    /**
     * 保修期
     */
    private String warrantyPeriod;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 组ID
     */
    @NotNull(message = "分组id不能为空")
    private String groupId;

    /* Started by AICoder, pid:y7d201962a09e6514ccf090480eb800096f99f22 */
    /**
     * 规格型号：表示产品的具体规格或型号，例如产品版本或技术规格。
     */
    private String specificationModel;

    /**
     * 服务：表示与产品相关的服务信息，例如售后服务、保修服务等。
     */
    private String service;
    /* Ended by AICoder, pid:y7d201962a09e6514ccf090480eb800096f99f22 */

    /**
     * 推荐等级：从A、B、C三个等级中选择其一
     */
    private String recommendedLevel;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 单位：表示产品的单位。
     */
    @NotNull(message = "单位不能为空")
    private String unit;

    /**
     * 描述：表示产品的描述信息。
     */
    @NotNull(message = "描述不能为空")
    private String description;
}
/* Ended by AICoder, pid:z69236350cb1f791430409afa05ead5a1e83a5ae */