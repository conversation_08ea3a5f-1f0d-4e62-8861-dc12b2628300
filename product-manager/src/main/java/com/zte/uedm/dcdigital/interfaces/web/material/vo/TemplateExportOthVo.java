package com.zte.uedm.dcdigital.interfaces.web.material.vo;


/* Started by AICoder, pid:q792evd2ea65fc914aeb0ae2928f9a098ea518aa */
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * TemplateExportOthVo 类用于表示导出模板的数据视图对象。
 * 该类包含多个字段，用于在 Excel 表格中显示不同的物料信息。
 */
@Getter
@Setter
@ToString
public class TemplateExportOthVo {

    /**
     * 分类，表示物料的分类信息。
     */
    @ExcelProperty(value = "分类*", index = 0)
    private String categoryName;

    /**
     * 物料名称（中文），表示物料的中文名称。
     */
    @ExcelProperty(value = "物料名称(中文)*", index = 1)
    private String name;

    /**
     * 物料名称（英文），表示物料的英文名称。
     */
    @ExcelProperty(value = "物料名称(英文)*", index = 2)
    private String nameEn;

    /**
     * 单位（中文），表示物料的中文单位。
     */
    @ExcelProperty(value = "单位(中文)*", index = 3)
    private String unit;

    /**
     * 单位（英文），表示物料的英文单位。
     */
    @ExcelProperty(value = "单位(英文)*", index = 4)
    private String unitEn;

    /**
     * 备注（中文），表示物料的备注信息。
     */
    @ExcelProperty(value = "备注(中文)", index = 5)
    private String remark;

    /**
     * 技术参数（中文），表示物料的技术参数。
     */
    @ExcelProperty(value = "技术参数(中文)*", index = 6)
    private String tecParam;

    /**
     * 项目名称，表示物料所属的项目名称。
     */
    @ExcelProperty(value = "项目名称*", index = 7)
    private String productName;

    /**
     * 成本无税 RMB，表示物料的成本价格（不含税）。
     */
    @ExcelProperty(value = "成本无税RMB", index = 8)
    private String cost;

    /**
     * 数字化系统物料标识，表示物料在数字化系统中的唯一标识。
     */
    @ExcelProperty(value = "数字化系统物料标识", index = 9)
    private String id;

    /**
     * OTH 代码，表示物料的 OTH 代码。
     */
    @ExcelProperty(value = "OTH代码", index = 10)
    private String othCode;

    /**
     * 检查结果，表示物料的检查结果。该字段不会导出到 Excel。
     */
    @ExcelIgnore
    private String checkResult;

    /**
     * 错误原因，表示物料的错误原因。该字段不会导出到 Excel。
     */
    @ExcelIgnore
    private String errorReason;

    /**
     * 默认构造函数。
     */
    public TemplateExportOthVo() {
    }

    // Getters and Setters

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnitEn() {
        return unitEn;
    }

    public void setUnitEn(String unitEn) {
        this.unitEn = unitEn;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTecParam() {
        return tecParam;
    }

    public void setTecParam(String tecParam) {
        this.tecParam = tecParam;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCost() {
        return cost;
    }

    public void setCost(String cost) {
        this.cost = cost;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOthCode() {
        return othCode;
    }

    public void setOthCode(String othCode) {
        this.othCode = othCode;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public String getErrorReason() {
        return errorReason;
    }

    public void setErrorReason(String errorReason) {
        this.errorReason = errorReason;
    }
}
/* Ended by AICoder, pid:q792evd2ea65fc914aeb0ae2928f9a098ea518aa */