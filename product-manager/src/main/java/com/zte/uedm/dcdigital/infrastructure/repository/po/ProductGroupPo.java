package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import lombok.*;

/* Started by AICoder, pid:cc9cc267316882914c840a5b809f386bb8f98e76 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@TableName("product_group")
public class ProductGroupPo extends Model<ProductGroupPo> {

    /**
     * id
     */
    @LogMark(range = {OperationMethodEnum.DELETE})
    private String id;

    /**
     * 分组名称
     */
    @LogMark(range = {OperationMethodEnum.DELETE})
    private String name;

    /**
     * pdm型号规格
     */
    @TableField("pdm_model_spec")
    private String pdmModelSpec;

    /**
     * 产品小类id
     */
    @TableField("product_category_id")
    @LogMark(range = {OperationMethodEnum.DELETE})
    private String productCategoryId;

    /**
     * 分组父id
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 路径
     */
    @TableField("path_name")
    private String pathName;

    /**
     * 路径id
     */
    @TableField("path_id")
    private String pathId;

    /**
     * 分组层级
     */
    @TableField("group_level")
    private Integer groupLevel;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建用户
     */
    private String createBy;

    /**
     * 更新用户
     */
    private String updateBy;
}
/* Ended by AICoder, pid:cc9cc267316882914c840a5b809f386bb8f98e76 */
