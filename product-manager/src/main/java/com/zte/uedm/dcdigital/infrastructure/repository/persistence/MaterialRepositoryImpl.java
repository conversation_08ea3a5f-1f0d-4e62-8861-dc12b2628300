/* Started by AICoder, pid:uba98ke00dg725d14bdd0b70106b6e60b5a29100 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.basis.util.base.i18n.I18nUtils;
import com.zte.uedm.common.bean.log.OperationLogBean;
import com.zte.uedm.component.kafka.producer.constants.KafkaTopicOptional;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.document.DocumentCitedVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.common.web.i18n.I18nUtil;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.model.material.entity.MaterialEntity;
import com.zte.uedm.dcdigital.domain.model.material.entity.MaterialWithExtendInfoEntity;
import com.zte.uedm.dcdigital.domain.repository.MaterialRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.MaterialConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.*;
import com.zte.uedm.dcdigital.infrastructure.repository.po.MaterialPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.MaterialQueryBean;
import com.zte.uedm.dcdigital.infrastructure.repository.po.PdmInfoPo;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.LectotypeMaterialVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialFuzzyVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 物料操作的实现类。
 * 该类实现了MaterialRepository接口，提供了对物料信息进行查询的操作。
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MaterialRepositoryImpl extends ServiceImpl<MaterialMapper,MaterialPo> implements MaterialRepository {

    @Autowired
    private MaterialMapper materialMapper;

    @Autowired
    private DemandMapper demandMapper;

    @Override
    public List<MaterialQueryBean> queryMaterialDetailsByApprovalId(String approvalId) {
        try {
            //TODO 联表查询，通过审批id查询
            List<MaterialQueryBean> materialQueryBeans = materialMapper.unionQueryByApprovalId(approvalId);
            if (CollectionUtils.isEmpty(materialQueryBeans)) {
                log.info("Material information does not exist or has been deleted.");
                return new ArrayList<>();
            }
            return materialQueryBeans;
        } catch (Exception e) {
            log.error("Database operation error.", e);
            throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
        }
    }
    /* Started by AICoder, pid:x8a3aqd035660a7145490bc6a0cd9a52c7c3d6bf */
    @Override
    public List<MaterialEntity> queryMaterialByIds(List<String> materialIds) {
        log.debug("Query material information based on materialIds :{}",materialIds);
        if (CollectionUtils.isEmpty(materialIds)) {
            return Collections.emptyList();
        }
        try {
            //查询主表数据
            LambdaQueryWrapper<MaterialPo> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(CollectionUtils.isNotEmpty(materialIds), MaterialPo::getId, materialIds);
            wrapper.orderByAsc(MaterialPo::getName);
            List<MaterialPo> materialPos = materialMapper.selectList(wrapper);
            if (CollectionUtils.isEmpty(materialPos)) {
                log.info("Material information does not exist or has been deleted.");
                return new ArrayList<>();
            }
            return MaterialConvert.INSTANCE.listMaterialPoToMaterialEntity(materialPos);
        } catch (Exception e) {
            log.error("Database operation error.", e);
            throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
        }
    }

    @Override
    public void batchUpdateMaterials(List<MaterialEntity> modifiedList) {
        log.debug("update main material information modifiedList:{}",modifiedList);
        if (CollectionUtils.isEmpty(modifiedList)) {
            return;
        }
        try {
            List<MaterialPo> materialPos = MaterialConvert.INSTANCE.listMaterialEntityToMaterialPo(modifiedList);
            this.updateBatchById(materialPos);
        } catch (Exception e) {
            log.error("Database operation error.", e);
            throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
        }
    }

    /* Started by AICoder, pid:8aa21j3939980b314b8e08a3b0a263281230cdc5 */
    @Override
    public void batchAdd(List<MaterialEntity> materialEntityList) {
        if (materialEntityList == null || materialEntityList.isEmpty()) {
            log.warn("batchAdd: Input list is null or empty.");
            return;
        }
        List<MaterialPo> materialPos = MaterialConvert.INSTANCE.listMaterialEntityToMaterialPo(materialEntityList);
        this.saveBatch(materialPos);
    }

    @Override
    public List<MaterialVo> queryMaterialByCondition(MaterialConditionQueryDto dto) {
        return materialMapper.queryMaterialByCondition(dto,dto.getMaterialStatus(),dto.getGroupId(),dto.getSalesStatus(),dto.getPurchaseMode());
    }


    @Override
    public List<DocumentCitedVo> selectByIds(List<String> ids) {
        return materialMapper.selectByIds(ids);
    }
    /* Ended by AICoder, pid:8aa21j3939980b314b8e08a3b0a263281230cdc5 */

    @Override
    public List<MaterialEntity> queryByApprovalId(String approvalId) {
        log.debug("Query material information based on approvalId :{}",approvalId);
        if (StringUtils.isEmpty(approvalId)) {
            return Collections.emptyList();
        }
        try {
            //查询主表数据
            LambdaQueryWrapper<MaterialPo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MaterialPo::getApprovalId,approvalId);
            wrapper.orderByAsc(MaterialPo::getName);
            List<MaterialPo> materialPos = materialMapper.selectList(wrapper);
            if (CollectionUtils.isEmpty(materialPos)) {
                log.info("Material information does not exist or has been deleted.");
                return new ArrayList<>();
            }
            return MaterialConvert.INSTANCE.listMaterialPoToMaterialEntity(materialPos);
        } catch (Exception e) {
            log.error("Database operation error.", e);
            throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
        }
    }
    /* Ended by AICoder, pid:x8a3aqd035660a7145490bc6a0cd9a52c7c3d6bf */

    /* Started by AICoder, pid:117f3ef761w5b0914c2d0b3d806302493914156d */

    /**
     * 添加物料。
     *
     * @param entity 要添加的物料实体
     */
    @Override
    public void addMaterial(MaterialEntity entity) {
        MaterialPo po = MaterialConvert.INSTANCE.materialEntityToMaterialPo(entity);
        materialMapper.insert(po);
    }

    /**
     * 编辑物料。
     *
     * @param entity 要编辑的物料实体
     */
    @Override
    public void editMaterial(MaterialEntity entity) {
        MaterialPo po = MaterialConvert.INSTANCE.materialEntityToMaterialPo(entity);
        materialMapper.updateById(po);
    }

    /**
     * 删除物料。
     *
     * @param id 要删除的物料的ID
     */
    @Override
    public void deleteMaterial(String id) {
        materialMapper.deleteById(id);
    }

    /**
     * 根据ID查询物料。
     *
     * @param id 要查询的物料的ID
     * @return 查询到的物料实体，如果没有找到则返回null
     */
    @Override
    public MaterialEntity queryMaterialById(String id) {
        MaterialPo materialPo = materialMapper.selectById(id);
        return materialPo != null ? MaterialConvert.INSTANCE.materialPoToMaterialEntity(materialPo) : null;
    }
    /* Started by AICoder, pid:l24faj2043n0243148ba0ac550460e59dd909091 */
    @Override
    public boolean existPdmInfoId(String pdmInfoId) {
        LambdaQueryWrapper<MaterialPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialPo::getPdmInfoId,pdmInfoId);
        return materialMapper.exists(wrapper);
    }
    /* Started by AICoder, pid:760c0k006c76b2014e32093ef0a2b91d02846100 */
    @Override
    public MaterialVo multiTableQueryById(String id) {
        return materialMapper.queryLatestMaterialById(id);
    }
    /* Ended by AICoder, pid:760c0k006c76b2014e32093ef0a2b91d02846100 */

    /* Ended by AICoder, pid:l24faj2043n0243148ba0ac550460e59dd909091 */
    /* Ended by AICoder, pid:117f3ef761w5b0914c2d0b3d806302493914156d */

    /* Started by AICoder, pid:z9630e4ec24788614ef408c590e0772b18912f03 */
    /**
     * 物料模糊查询
     * @param materialFuzzyDto
     * @return
     */
    @Override
    public List<MaterialFuzzyVo> selectMaterialFuzzy(MaterialFuzzyDto materialFuzzyDto) {
        return materialMapper.selectFuzzyMaterial(materialFuzzyDto);
    }

    /**
     * 物料精确查询
     * @param materialAccurateDto
     * @return
     */
    @Override
    public List<MaterialFuzzyVo> selectMaterialAccurate(MaterialAccurateDto materialAccurateDto) {
        return materialMapper.selectAccurateMaterial(materialAccurateDto);
    }

    @Override
    public int selectByIdAndName(String id, String name) {
        return materialMapper.selectByIdAndName(id, name);
    }
    /* Ended by AICoder, pid:z9630e4ec24788614ef408c590e0772b18912f03 */

    @Override
    public List<MaterialEntity> queryByGroupId(String oldGroupId) {
        LambdaQueryWrapper<MaterialPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialPo::getGroupId,oldGroupId);
        List<MaterialPo> materialPos = materialMapper.selectList(wrapper);
        return MaterialConvert.INSTANCE.listMaterialPoToMaterialEntity(materialPos);
    }

    /* Started by AICoder, pid:3e233vc3c3bb2ac14858090a80c091016f59cf61 */
    @Override
    public void updateBatchMaterials(List<MaterialEntity> entity) {
        List<MaterialPo> materialPos = MaterialConvert.INSTANCE.listMaterialEntityToMaterialPo(entity);
        try {
            materialMapper.updateBatchGroupIdById(materialPos);
        } catch (Exception e) {
            throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
        }
    }
    /* Ended by AICoder, pid:3e233vc3c3bb2ac14858090a80c091016f59cf61 */

    /* Started by AICoder, pid:h9bb4775533f40614b0c088f4079c1125552b515 */
    @Override
    public List<MaterialEntity> batchQueryByPdmInfoIds(List<String> pdmInfoIds) {
        // 使用LambdaQueryWrapper构建查询条件，筛选出pdmInfoId在给定列表中的记录。
        LambdaQueryWrapper<MaterialPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MaterialPo::getPdmInfoId, pdmInfoIds);

        // 执行查询并获取结果列表。
        List<MaterialPo> materialPos = materialMapper.selectList(wrapper);

        // 将查询结果转换为MaterialEntity列表并返回。
        return MaterialConvert.INSTANCE.listMaterialPoToMaterialEntity(materialPos);
    }

    /* Started by AICoder, pid:zd6f1i48b4h1e5b1469d09e570aaf3278b24e3e0 */
    @Override
    public List<MaterialEntity> batchQueryByOthIds(List<String> othIdList, String materialStatus) {
        if (CollectionUtils.isEmpty(othIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialPo::getMaterialStatus,materialStatus)
                .in(MaterialPo::getOthInfoId, othIdList);
        List<MaterialPo> materialPos = materialMapper.selectList(wrapper);
        return MaterialConvert.INSTANCE.listMaterialPoToMaterialEntity(materialPos);
    }

    @Override
    public List<MaterialEntity> queryMaterialByOthName(List<String> nameList, String materialStatus) {
        if (CollectionUtils.isEmpty(nameList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialPo::getMaterialStatus,materialStatus)
                .isNull(MaterialPo::getPdmInfoId)
                .in(MaterialPo::getName, nameList);
        List<MaterialPo> materialPos = materialMapper.selectList(wrapper);
        return MaterialConvert.INSTANCE.listMaterialPoToMaterialEntity(materialPos);
    }
    /* Started by AICoder, pid:b6476l5cd5132b314969098230a92431d2832184 */
    @Override
    public List<MaterialEntity> queryByPdmInfoIdAndBrand(String id, String brand, String materialId) {
        // 使用 Optional 处理品牌为空的情况
        LambdaQueryWrapper<MaterialPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialPo::getPdmInfoId, id)
                .ne(StringUtils.isNotBlank(materialId),MaterialPo::getId,materialId)
                .eq(StringUtils.isNotBlank(brand), MaterialPo::getBrand, brand);
        if (StringUtils.isBlank(brand)) {
            wrapper.and(
                    wrapper1 -> wrapper1.isNull(MaterialPo::getBrand)
                            .or()
                            .eq(MaterialPo::getBrand, "")
            );
        }
        //pdm的id和brand 联合唯一
        List<MaterialPo> materialPos = materialMapper.selectList(wrapper);
        return MaterialConvert.INSTANCE.listMaterialPoToMaterialEntity(materialPos);
    }

    @Override
    public boolean checkNonPdmMaterialName(String name, String id) {
        LambdaQueryWrapper<MaterialPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(StringUtils.isNotBlank(id),MaterialPo::getId,id)
                .eq(MaterialPo::getName,name)
                .isNull(MaterialPo::getPdmInfoId);
        return materialMapper.exists(wrapper);
    }

    @Override
    public void updateDraftMaterialRelation(String id,String pdmInfoId,String othInfoId) {
        //草稿变更需要清除关联的pdm id 或者 oth id
        materialMapper.updateDraftMaterialRelation(id,pdmInfoId,othInfoId);
    }
    /* Started by AICoder, pid:j18a5t277ccb4661433909ca0055a31d1d634e1f */
    @Override
    public List<MaterialEntity> queryByPdmInfoIdAndStatus(String pdmInfoId, String status) {
        //查询主表数据
        LambdaQueryWrapper<MaterialPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialPo::getPdmInfoId,pdmInfoId)
                .eq(MaterialPo::getMaterialStatus,status);
        List<MaterialPo> materialPos = materialMapper.selectList(wrapper);
        return MaterialConvert.INSTANCE.listMaterialPoToMaterialEntity(materialPos);
    }
    /* Ended by AICoder, pid:j18a5t277ccb4661433909ca0055a31d1d634e1f */
    /* Ended by AICoder, pid:b6476l5cd5132b314969098230a92431d2832184 */
    /* Ended by AICoder, pid:zd6f1i48b4h1e5b1469d09e570aaf3278b24e3e0 */
    /* Ended by AICoder, pid:h9bb4775533f40614b0c088f4079c1125552b515 */

    @Override
    public List<MaterialWithExtendInfoEntity> queryMaterialWithExtendInfoByIds(List<String> ids) {
        return materialMapper.queryMaterialWithExtendInfoByIds(ids);
    }

    @Override
    public PageInfo<MaterialVo> fuzzyQuery(MaterialFuzzyQueryDto queryDto) {
        //兼容选型单查询
        List<String> materialIdList = Collections.emptyList();
        if (StringUtils.isNotBlank(queryDto.getLectotypeId())){
            //是查询选型单的物料
            materialIdList = queryAssociationMaterial(queryDto.getLectotypeId());
            if (CollectionUtils.isEmpty(materialIdList)) {
                return PageInfo.emptyPageInfo();
            }
        }
        String sortField = getDbSortField(queryDto.getSortField());
        queryDto.setSortField(sortField);
        queryDto.setMaterialIdList(materialIdList);
        String sortOrder = GlobalConstants.DESC_ORDER.equalsIgnoreCase(queryDto.getSortOrder()) ? GlobalConstants.DESC_ORDER : GlobalConstants.ASC_ORDER;
        queryDto.setSortOrder(sortOrder);
        PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        return new PageInfo<>(materialMapper.fuzzyQuery(queryDto));
    }

    @Override
    public List<MaterialVo> openApiFuzzyQuery(MaterialFuzzyQueryDto queryDto) {
        return materialMapper.fuzzyQuery(queryDto);
    }

    @Override
    public List<String> queryAssociationMaterial(String lectotypeId) {
        return demandMapper.getLectotypeMaterialByLtc(lectotypeId);
    }

    @Override
    public List<MaterialVo> queryAssociatedMaterial(List<String> materialIdList, SelectionFormMaterialQueryDto queryDto) {
        if (CollectionUtils.isEmpty(materialIdList)) {
            return Collections.emptyList();
        }
        String sortField = getDbSortField(queryDto.getSortField());
        return materialMapper.queryAssociatedMaterial(materialIdList,queryDto.getFuzzyName(),sortField,queryDto.getSortOrder());
    }

    @Override
    public PageVO<MaterialVo> selectionAssociationConditions(MaterialConditionQueryDto dto) {

        if (dto.getPageNum() == null || dto.getPageSize() == null) {
            dto.setPageNum(GlobalConstants.ONE);
            dto.setPageSize(GlobalConstants.TEN);
        }
        String sortField = getDbSortField(dto.getSortField());
        dto.setSortField(sortField);
        Page<MaterialVo> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<MaterialVo> materialVos = materialMapper.selectionAssociationConditions(dto, dto.getMaterialStatus(), dto.getMaterialIdList(), dto.getSalesStatus(), dto.getPurchaseMode());
        return new PageVO<>(page.getTotal(), materialVos);
    }

    @Override
    public List<LectotypeMaterialVo> querySelectionFormByMaterialIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return demandMapper.getLectotypeMaterialByMaterialIds(ids);
    }

    @Override
    public void batchDeleteMaterialByIds(List<String> materialIdList) {
        if (CollectionUtils.isEmpty(materialIdList)) {
            return;
        }
        baseMapper.deleteBatchIds(materialIdList);
    }

    @Override
    public boolean queryMaterialByOthId(String othInfoId, String id) {
        LambdaQueryWrapper<MaterialPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(StringUtils.isNotBlank(id),MaterialPo::getId,id)
                .eq(MaterialPo::getOthInfoId,othInfoId)
                .isNull(MaterialPo::getPdmInfoId);
        return materialMapper.exists(wrapper);
    }

    /* Started by AICoder, pid: */
    @Override
    public void updateMaterial(MaterialEntity materialEntity) {
        MaterialPo materialPo = MaterialConvert.INSTANCE.materialEntityToMaterialPo(materialEntity);
        materialMapper.updateById(materialPo);
    }

//    private void updateChangeParameterAndSendLog(List<MaterialEntity> billOfQuantityEntityList, String newProductSub,
//                                                 Set<String> productSubcategoryIds, Set<String> otherExist, String userId, String projectId) {
//        String detail = buildLogDetail(billOfQuantityEntityList);
//        //构建日志记录
//        OperationLogBean operationLogBean = OperationLogUtils.buildOperationLogBean(GlobalConstants.BOQ_BATCH_EDIT, OperationTypeOptional.OPERATION_TYPE_UPDATE, userId);
//        try {
//            //所涉及的产品小类,去掉有交集的
//            productSubcategoryIds.removeAll(otherExist);
//            //删除任务
//            cancelToDoTasks(projectId, new ArrayList<>(productSubcategoryIds), GlobalConstants.BOQ_CHANGE_SUBCATEGORY);
//            //更新数据
//            billOfQuantityRepository.updateOptionsForBills(billOfQuantityEntityList);
//            //上送日志
//            sendSuccessLogMessage(operationLogBean, detail);
//        } catch (Exception e) {
//            log.error("Batch update of bill of quantities failed.", e);
//            sendFailLogMessage(operationLogBean, e.getMessage(), detail);
//            throw new BusinessException(StatusCode.DATABASE_OPERATION_EXCEPTION);
//        }
//    }

//    private void sendSuccessLogMessage(com.zte.uedm.dcdigital.log.domain.bean.OperationLogBean operationLogBean, String detail) {
//        sendKafkaMessage(operationLogBean, OperationResultOptional.OPERATION_RESULT_SUCCESS, detail);
//    }
//
//    private void sendFailLogMessage(com.zte.uedm.dcdigital.log.domain.bean.OperationLogBean operationLogBean, String errorMessage, String detail) {
//        String[] args = new String[]{errorMessage};
//        operationLogBean.setFailReason(I18nUtil.getI18nWithArgs(GlobalConstants.OPERATION_FAIL_REASON, args));
//        sendKafkaMessage(operationLogBean, OperationResultOptional.OPERATION_RESULT_FAIL, detail);
//    }
//
//    private void sendKafkaMessage(com.zte.uedm.dcdigital.log.domain.bean.OperationLogBean operationLogBean, OperationResultOptional optional, String detail) {
//
//        //设置结束时间
//        operationLogBean.setLogEndDate(DateTimeUtils.getCurrentTime());
//        operationLogBean.setOperateResult(optional.getId());
//        String[] args = new String[]{detail};
//        String detailZh = I18nUtil.getI18nWithArgs(GlobalConstants.BOQ_BATCH_OPERATION_DETAIL, args, Locale.CHINA);
//        String detailEn = I18nUtil.getI18nWithArgs(GlobalConstants.BOQ_BATCH_OPERATION_DETAIL, args, Locale.ENGLISH);
//        operationLogBean.setDetail(I18nUtils.toI18nJson(detailZh, detailEn));
//        try {
//            log.debug("batch operation, :operationLogBean={}!", operationLogBean);
//            kafkaSenderService.send(KafkaTopicOptional.KAFKA_TOPIC_OTCP_LOG_MANAGE, operationLogBean);
//        } catch (Exception e) {
//            log.error("log Message send failed for ", e);
//        }
//    }

    /* Ended by AICoder, pid: */

    private String getDbSortField(String sortField) {
        //物料排序字段，默认安装物料名称排序
        if (StringUtils.isNotBlank(sortField)) {
            // 获取实体类信息
            TableInfo tableInfo = TableInfoHelper.getTableInfo(MaterialPo.class);
            // 假设fieldName是实体类中的属性名，例如 "expirationDate"
            String fieldName = tableInfo.getFieldList().stream()
                    .filter(fieldInfo -> fieldInfo.getProperty().equals(sortField))
                    .findFirst()
                    .map(TableFieldInfo::getColumn)
                    .orElse(StringUtils.EMPTY); // 如果找不到匹配项，则默认设置为空
            if (StringUtils.isNotBlank(fieldName)) {
                return fieldName;
            }
            return getDbSortFieldFromPdm(sortField);
        }
        return StringUtils.EMPTY;
    }
    private String getDbSortFieldFromPdm(String sortField) {
        //物料排序字段，默认安装物料名称排序
        if (StringUtils.isNotBlank(sortField)) {
            // 获取实体类信息
            TableInfo tableInfo = TableInfoHelper.getTableInfo(PdmInfoPo.class);
            // 假设fieldName是实体类中的属性名，例如 "expirationDate"
            return tableInfo.getFieldList().stream()
                    .filter(fieldInfo -> fieldInfo.getProperty().equals(sortField))
                    .findFirst()
                    .map(TableFieldInfo::getColumn)
                    .orElse(StringUtils.EMPTY); // 如果找不到匹配项，则默认设置为空
        }
        return StringUtils.EMPTY;
    }
}
/* Ended by AICoder, pid:uba98ke00dg725d14bdd0b70106b6e60b5a29100 */