package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductGroupStatusCode;
import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import com.zte.uedm.dcdigital.security.annotation.DcResourceField;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;

/* Started by AICoder, pid:o5861r825cv9e4e141890b03a05ea32d81b74d48 */
@Getter
@Setter
@Slf4j
public class ProductGroupAddDto {

    /**
     * 分组名称
     */
    @NotBlank(message = "name必填,name is required")
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    private String name;

    /**
     * 型号规格
     */
//    @NotBlank(message = "pdmModelSpec必填,pdmModelSpec is required")
    private String pdmModelSpec;

    /**
     * 产品小类Id
     */
    @NotBlank(message = "productCategoryId必填,productCategoryId is required")
    @LogMark(range = {OperationMethodEnum.ADD, OperationMethodEnum.UPDATE})
    @DcResourceField
    private String productCategoryId;

    /**
     * 父分组Id
     */
    private String parentId;

    /* Started by AICoder, pid:e5ff4h0ef4p7c9d14f840b8460d55e1b9a36e1d4 */
    /**
     * 验证参数的有效性。
     *
     * @throws BusinessException 当参数无效或名称长度超过限制时抛出。
     */
    public void parameterVerification() throws BusinessException {
        ValidResult validResult = ValidateUtils.validateObj(this);
        if (validResult.isError()) {
            log.error("[ProductCategoryEditDto check param] Invalid parameter: {}", validResult.getErrorMessage());
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
        if (name.trim().length() > GlobalConstants.NAME_LENGTH) {
            log.error("Name length exceeds the limit.");
            throw new BusinessException(ProductGroupStatusCode.GROUP_NAME_LENGTH_EXCEEDS_LIMIT);
        }
    }
    /* Ended by AICoder, pid:e5ff4h0ef4p7c9d14f840b8460d55e1b9a36e1d4 */
}
/* Ended by AICoder, pid:o5861r825cv9e4e141890b03a05ea32d81b74d48 */
