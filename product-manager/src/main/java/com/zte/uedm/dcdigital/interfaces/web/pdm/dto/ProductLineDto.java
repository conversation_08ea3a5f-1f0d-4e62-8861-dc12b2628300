package com.zte.uedm.dcdigital.interfaces.web.pdm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/* Started by AICoder, pid:c2d093cb20x2de2144eb0988b006c836b4a12e04 */
@Getter
@Setter
@ToString
public class ProductLineDto {

    /**
     * 产品线编号
     */
    @ApiModelProperty(value = "产品线编号")
    private String productLineNo;

    /**
     * 产品线名称
     */
    @ApiModelProperty(value = "产品线名称")
    private String productLineName;

    /**
     * 页码，必填。
     */
    @NotNull(message = "pageNum cannot be null")
    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    /**
     * 每页数量，必填。
     */
    @NotNull(message = "pageSize cannot be null")
    @ApiModelProperty(value = "每页数量")
    private Integer pageSize;
}
/* Ended by AICoder, pid:c2d093cb20x2de2144eb0988b006c836b4a12e04 */
