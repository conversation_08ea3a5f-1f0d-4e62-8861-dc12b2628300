/* Started by AICoder, pid:wb918s0482f861f14a7e09b610d952519f480629 */
package com.zte.uedm.dcdigital.interfaces.web.material.vo;

import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.BasicQueryResponseVo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * AI结果响应VO，用于封装AI处理的结果信息
 */
@Getter
@Setter
@ToString
public class AIResultVo {

    /**
     * 响应代码，表示请求的处理状态
     */
    private BasicQueryResponseVo.Code code;

    /**
     * 业务对象，包含具体的AI处理结果
     */
    private Bo bo;

    /**
     * 响应规则，描述响应的具体规则或逻辑
     */
    private String responseRule;

    /**
     * 业务对象类，包含AI处理的具体结果信息
     */
    @Getter
    @Setter
    @ToString
    public static class Bo {
        /**
         * 聊天UUID，唯一标识一次聊天会话
         */
        private String chatUuid;

        /**
         * 文档ID，标识相关的文档
         */
        private String docId;

        /**
         * 完成原因，描述处理完成的具体原因
         */
        private String finishReason;

        /**
         * 处理结果，包含AI处理的具体结果内容
         */
        private String result;
    }
}
/* Ended by AICoder, pid:wb918s0482f861f14a7e09b610d952519f480629 */