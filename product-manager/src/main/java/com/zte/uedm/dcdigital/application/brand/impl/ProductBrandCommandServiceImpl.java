package com.zte.uedm.dcdigital.application.brand.impl;

import cn.hutool.core.text.StrSplitter;
import com.zte.uedm.basis.util.base.time.DateTimeUtils;
import com.zte.uedm.dcdigital.application.brand.ProductBrandCommandService;
import com.zte.uedm.dcdigital.application.brand.ProductBrandQueryService;
import com.zte.uedm.dcdigital.common.bean.document.DocumentInfoVo;
import com.zte.uedm.dcdigital.common.bean.enums.system.PermissionEnum;
import com.zte.uedm.dcdigital.common.enums.DocumentRelateResourceTypeEnum;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.common.enums.SelectionAttributeEnums;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductBrandStatusCode;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductBrandMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductBrandTagMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductBrandPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductBrandTagPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductBrandAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductBrandVo;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.security.util.PermissionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProductBrandCommandServiceImpl implements ProductBrandCommandService {
    @Autowired
    private ProductBrandMapper brandMapper;
    @Autowired
    private ProductBrandTagMapper tagMapper;

    @Autowired
    private AuthService authService;

    @Autowired
    private ProductBrandQueryService queryService;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private PermissionUtil permissionUtil;

    @Override
    public int addBrandAndTag(ProductBrandAddDto brandDto) {

        if (brandDto.getTagName()!=null){
            // 检查品牌名称+标签组合是否存在
            if (!queryService.judgingConditionBrandName(brandDto.getProductCategoryId(), brandDto.getBrandName(),brandDto.getTagName())) {
                log.error("Brand name already exists: {}", brandDto.getBrandName());
                throw new BusinessException(ProductBrandStatusCode.BRAND_NAME_ALREADY_EXISTS);
            }
        }else {
            // 检查品牌名称+空标签是否存在

        }

        //开始添加品牌
        ProductBrandPo productBrandPo = new ProductBrandPo();
        BeanUtils.copyProperties(brandDto, productBrandPo);
        //存储品牌信息
        String id = UUID.randomUUID().toString();
        productBrandPo.setId(id);
        String userId = authService.getUserId();
        productBrandPo.setCreateBy(userId);
        productBrandPo.setCreateTime(DateTimeUtils.getCurrentTime());
        productBrandPo.setUpdateBy(userId);
        productBrandPo.setUpdateTime(DateTimeUtils.getCurrentTime());
        int result = brandMapper.insertProductBrand(productBrandPo);
        //添加对应的标签信息
        addBrandTag(productBrandPo.getId(), brandDto.getTagName());
        //判断是否传递文档id
        if (!StringUtils.isBlank(brandDto.getDocumentIds())){
            addBrandAssociatedDocument(id, brandDto.getDocumentIds());
        }
        return result;
    }

    /**
     * 判断当前选型评分是否符合新增/修改要求
     *
     * @param productCategoryId 产品小类id
     * @param score             选型评分
     * @param selectionType     优选类型
     */
    private boolean judgingConditionScore(String productCategoryId, BigDecimal score, Integer selectionType) {
        BigDecimal ruleScore = queryService.getNewSelectScore(productCategoryId, selectionType);
        int zero = ruleScore.compareTo(BigDecimal.ZERO);
        //todo 魔法值修改
        if (zero == 0) {
            return true;
        }
        int result = score.compareTo(ruleScore);
        if (SelectionAttributeEnums.PREFERENCE.getId() == selectionType.intValue()) {
            //如果是评分选型是"优选"那么新增的分数必须小于返回的规则分数
            return result < 0;
        } else if (SelectionAttributeEnums.OTHER.getId() == selectionType.intValue()) {
            //如果是评分选型是"其它"那么新增的分数就必须大于返回的规则分数
            return result > 0;
        }
        return false;
    }

    private void addBrandTag(String brandId, String tagName) {
        //组装productBrandTagPo对象
        List<String> tagNames = StrSplitter.split(tagName, ',', 0, true, true);
        for (int i = 0; i < tagNames.size(); i++) {
            String item = tagNames.get(i);
            ProductBrandTagPo tagPo = new ProductBrandTagPo();
            tagPo.setBrandId(brandId);
            String id = UUID.randomUUID().toString();
            tagPo.setId(id);
            tagPo.setTagName(item);
            tagPo.setTagOrder(i); // 根据循环索引设置标签顺序
            tagMapper.insertProductBrandTag(tagPo);
        }

    }


    @Override
    public int updateBrandAndTag(ProductBrandAddDto brandDto) {
        // 检查更新的品牌名称+标签组合是否重复
        updateConditionBrandName(brandDto);
        //1：删除该品牌下的所有标签信息
        tagMapper.deleteProductBrandTagByBrandId(brandDto.getId());
        //2：新增编辑的标签信息
        addBrandTag(brandDto.getId(), brandDto.getTagName());
        //3:文档关联关系
        //删除品牌之前关联的文档信息
        deleteBrandAssociatedDocument(brandDto.getId());
        //判断是否传递文档id
        if (!StringUtils.isBlank(brandDto.getDocumentIds())){
            addBrandAssociatedDocument(brandDto.getId(), brandDto.getDocumentIds());
        }
        //5：更新品牌信息
        ProductBrandPo brandPo = new ProductBrandPo();
        BeanUtils.copyProperties(brandDto, brandPo);
        String userId = authService.getUserId();
        brandPo.setUpdateBy(userId);
        brandPo.setUpdateTime(DateTimeUtils.getCurrentTime());
        return brandMapper.updateProductBrand(brandPo);
    }
    //抽取方法在更新品牌时查看品牌名+标签是否重复
    private void updateConditionBrandName(ProductBrandAddDto brandDto) {
        List<String> paramTagNames = StrSplitter.split(brandDto.getTagName(), ',', 0, true, true);
        //查询当前编辑品牌修改前的信息与当前传入信息进行比较若相等就放行否则(查询修改后的品牌名+标签是否存在,若存在则提示已重复)
        ProductBrandVo productBrandVo = brandMapper.selectProductBrandById(brandDto.getId());
        //todo 根据品牌id获取标签列表
        ProductBrandTagPo brandTagPo=new ProductBrandTagPo();
        brandTagPo.setBrandId(productBrandVo.getId());
        //获取对应标签列表
        List<ProductBrandTagPo> productBrandTagPos = tagMapper.selectProductBrandTagList(brandTagPo);
        if (CollectionUtils.isEmpty(productBrandTagPos)){
            //放行
        }else {
            List<String> tagNames=productBrandTagPos.stream().map(ProductBrandTagPo::getTagName).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tagNames)){
                String oralNameAndTags=extractBrandNameAndTagName(productBrandVo.getBrandName(),tagNames);
                String newNameAndTags=extractBrandNameAndTagName(brandDto.getBrandName(),paramTagNames);
                if (oralNameAndTags.equals(newNameAndTags)){
                    //放行
                }else {
                    if (!queryService.judgingConditionBrandName(brandDto.getProductCategoryId(), brandDto.getBrandName(),brandDto.getTagName())) {
                        //重复
                        throw new BusinessException(ProductBrandStatusCode.BRAND_NAME_ALREADY_EXISTS);
                    }
                    //放行
                }
            }
        }

    }
    //抽取的方法，提取品牌名+标签名
    private String extractBrandNameAndTagName(String bandName,List<String> tagNames){
        StringBuilder sb = new StringBuilder();
        sb.append(bandName);
        for (String tagName : tagNames) {
            sb.append(tagName);
        }
        return sb.toString();
    }

    /* Started by AICoder, pid:cb35dua36f39bd01402e0a9070b46b21c0e2254d */
    @Override
    public int deleteBrandAndTag(String id) {
        ProductBrandVo productBrandVo = brandMapper.selectProductBrandById(id);
        permissionUtil.checkPermission(productBrandVo.getProductCategoryId(), PermissionEnum.PRODUCT_BRAND_DELETE);

        // 1: 判断品牌是否被其他业务引用, 若被引用则不允许被删除
        List<DocumentInfoVo> documentInfoVos = documentService.queryDocumentByBrandId(id);
        if (CollectionUtils.isNotEmpty(documentInfoVos)) {
            log.info("deleteBrandAndTag : The brand is quoted and is not allowed to be deleted");
            throw new BusinessException(ProductBrandStatusCode.BRAND_QUOTED_NOT_DELETED);
        }

        // 2: 删除品牌对应的关联文档关系
        deleteBrandAssociatedDocument(id);

        // 3: 删除品牌对应标签
        tagMapper.deleteProductBrandTagByBrandId(id);

        // 4: 删除品牌
        int result = brandMapper.deleteProductBrandById(id);
        return result;
    }

    /* Ended by AICoder, pid:cb35dua36f39bd01402e0a9070b46b21c0e2254d */

    @Override
    public void addBrandAssociatedDocument(String brandId, String documentIds) {
        //String转List<String>,收集文档id
        List<String> documentIdList = StrSplitter.split(documentIds, ',', 0, true, true);
        documentService.relateDocuments(brandId, documentIdList, DocumentRelateResourceTypeEnum.BRAND.getCode());
        //若文档的品牌类型为专属品牌，需将当前品牌更新到文档的专属品牌列表中
        documentService.updateDocumentBrandInfo(brandId, documentIdList);
    }

    @Override
    public void deleteBrandAssociatedDocument(String brandId) {
        documentService.deleteByResourceId(brandId);
    }
}
