package com.zte.uedm.dcdigital.interfaces.web.material.vo;

/* Started by AICoder, pid:qf78bea289d68f7145f30aec8024892af2074527 */
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * ParseVerifyMaterialOthVo 类用于封装物料解析和校验的结果，提供详细的导入反馈信息。
 * 该类包含解析的物料数据和总数等信息。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class ParseVerifyMaterialOthVo {

    /**
     * 解析的物料数据，包含所有导入的物料信息。
     */
    private List<TemplateImportOthVo> material;

    /**
     * 总数，表示导入的物料总数。
     */
    private Integer total;
}
/* Ended by AICoder, pid:qf78bea289d68f7145f30aec8024892af2074527 */