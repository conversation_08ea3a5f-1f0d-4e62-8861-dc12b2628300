/* Started by AICoder, pid:00570j3eddz0959145f108e0817dc50986316ca6 */
package com.zte.uedm.dcdigital.interfaces.web.material.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 采购成本视图对象 (VO)。
 * 用于封装采购成本查询结果的详细信息。
 */
@Getter
@Setter
@ToString
public class ProcurementCostVo {

    /**
     * id，唯一标识。
     */
    private String id;

    /**
     * 选型单所属项目，
     */
    private String projectName;

    /**
     * 归属选型单id，
     * 关联的选型单的唯一标识。
     */
    private String lectotypeId;

    /**
     * 选型单名称。
     */
    private String lectotypeName;
    /**
     * 选型单状态。
     */
    private Integer lectotypeStatus;

    /**
     * 选型单状态描述名称。
     */
    private String lectotypeStatusName;

    /**
     * 归属选型类型，例如招标或指定。
     */
    private String lectotypeType;

    /**
     * 洽谈价。
     * 记录采购过程中的洽谈价格。
     */
    private BigDecimal negotiatedPrice;

    /**
     * 基准目标价格。
     * 记录采购过程中的基准目标价格。
     */
    private BigDecimal datumTargetPrice;

    /**
     * 挑战目标价。
     * 记录采购过程中的挑战目标价格。
     */
    private BigDecimal challengeTargetPrice;

    /**
     * 开标价。
     * 记录采购过程中的开标价格。
     */
    private BigDecimal openTenderPrice;

    /**
     * 定标价。
     * 记录采购过程中的定标价格。
     */
    private BigDecimal setBidPrice;

    /**
     * 发标时间。
     * 记录采购过程中发标的时间。
     */
    private String bidIssuingTime;

    /**
     * 开标时间。
     * 记录采购过程中开标的时间。
     */
    private String bidOpeningTime;

    /**
     * 创建时间，
     * 记录采购记录的创建时间。
     */
    private String createTime;

    /**
     * 更新时间，
     * 记录采购记录的最后更新时间。
     */
    private String updateTime;

    /**
     * 创建用户，
     * 记录创建该采购记录的用户。
     */
    private String createBy;

    /**
     * 更新用户，
     * 记录最后更新该采购记录的用户。
     */
    private String updateBy;
}
/* Ended by AICoder, pid:00570j3eddz0959145f108e0817dc50986316ca6 */