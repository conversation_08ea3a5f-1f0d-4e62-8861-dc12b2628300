package com.zte.uedm.dcdigital.interfaces.web.product.vo;
/* Started by AICoder, pid:9b469u55a1r428e14cfe08818035e9583ec64f78 */
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * StrongEchoCommentPo 类用于表示强回声评论的数据持久化对象。
 * 该类包含多个字段，用于描述评论的各种属性。
 */
@Getter
@Setter
public class StrongEchoCommentVo {

    /**
     * id 属性表示记录的唯一标识符。
     */
    private String id;

    /**
     * echoId 属性表示关联的回声 ID。
     */
    private String echoId;

    /**
     * pId 属性表示父评论的 ID。
     */
    private String pid;

    /**
     * comment 属性表示评论内容。
     */
    private String comment;

    /**
     * contact 属性表示联系信息。
     */
    private String contact;

    /**
     * createBy 属性表示创建者。
     */
    private String createBy;

    /**
     * createTime 属性表示创建时间。
     */
    private String createTime;

    /**
     * updateBy 属性表示更新者。
     */
    private String updateBy;

    /**
     * updateTime 属性表示更新时间。
     */
    private String updateTime;

    private Boolean isOwn;

    List<StrongEchoCommentVo> commentList= new ArrayList<>();
}
/* Ended by AICoder, pid:9b469u55a1r428e14cfe08818035e9583ec64f78 */