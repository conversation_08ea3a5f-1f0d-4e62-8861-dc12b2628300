/* Started by AICoder, pid:1cabcp3e17r61cb14ac00b1ae0bf283d32274ca0 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.dcdigital.domain.model.product.convert.ProductGroupCustomSortConverter;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupCustomSortEntity;
import com.zte.uedm.dcdigital.domain.repository.ProductGroupCustomSortRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductGroupCustomSortMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupCustomSortPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Slf4j
@Repository
public class ProductGroupCustomSortRepositoryImpl extends ServiceImpl<ProductGroupCustomSortMapper,ProductGroupCustomSortPo> implements ProductGroupCustomSortRepository {

    @Autowired
    private ProductGroupCustomSortMapper customSortMapper;

    @Override
    public List<ProductGroupCustomSortEntity> findByParentId(String parentId) {
        List<ProductGroupCustomSortPo> poList = customSortMapper.findByParentId(parentId);
        return ProductGroupCustomSortConverter.INSTANCE.listPoToProductGroupCustomEntityList(poList);
    }

    @Override
    public void deleteByParentId(String parentId) {
        customSortMapper.deleteByParentId(parentId);
    }

    @Override
    public void batchInsertCustomSort(List<ProductGroupCustomSortEntity> newSorts) {
        List<ProductGroupCustomSortPo> poList = ProductGroupCustomSortConverter.INSTANCE.listEntityToProductGroupCustomPoList(newSorts);
        customSortMapper.batchInsertProductGroupCustomSort(poList);
    }

    @Override
    public List<ProductGroupCustomSortEntity> findCustomSortByGroupIds(List<String> groupIds) {
        List<ProductGroupCustomSortPo> poList = customSortMapper.findCustomSortByGroupIds(groupIds);
        return ProductGroupCustomSortConverter.INSTANCE.listPoToProductGroupCustomEntityList(poList);
    }

    @Override
    public void deleteByParentIds(List<String> parentIds) {
        if (CollectionUtils.isNotEmpty(parentIds)) {
            LambdaQueryWrapper<ProductGroupCustomSortPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(ProductGroupCustomSortPo::getParentId, parentIds);
            customSortMapper.delete(queryWrapper);
        }
    }
}

/* Ended by AICoder, pid:1cabcp3e17r61cb14ac00b1ae0bf283d32274ca0 */