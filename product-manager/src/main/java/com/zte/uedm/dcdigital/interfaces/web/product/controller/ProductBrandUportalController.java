package com.zte.uedm.dcdigital.interfaces.web.product.controller;

import com.zte.uedm.dcdigital.application.brand.ProductBrandCommandService;
import com.zte.uedm.dcdigital.application.brand.ProductBrandQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductBrandStatusCode;
import com.zte.uedm.dcdigital.domain.common.vobj.SpecialPageVo;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductBrandMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductBrandPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductBrandAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductBrandQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.BrandPurchaseModeDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductBrandVo;
import com.zte.uedm.dcdigital.security.annotation.DcPermission;
import com.zte.uedm.dcdigital.log.annotation.DcOperationLog;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.math.BigDecimal;
import java.util.List;

@Path("/uportal/product-brand")
@Api(value = "产品小类的品牌管理")
@Controller
@Slf4j
public class ProductBrandUportalController {

    @Autowired
    private ProductBrandQueryService queryService;

    @Autowired
    private ProductBrandCommandService commandService;

    @POST
    @Path("/new-grade-standard")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "查询选型评分最新的上限或下限", notes = "查询选型评分最新的上限或下限", httpMethod = "POST")
    public BaseResult<Object> getNewSelectScore(ProductBrandQueryDto productBrandQueryDto) {
        if (StringUtils.isBlank(productBrandQueryDto.getProductCategoryId()) || productBrandQueryDto.getSelectionAttribute() == null) {
            throw new BusinessException(ProductBrandStatusCode.BRAND_PARAMETER_MISSING);
        }
        String productCategoryId = productBrandQueryDto.getProductCategoryId();
        Integer selectionAttribute = productBrandQueryDto.getSelectionAttribute();
        BigDecimal score = queryService.getNewSelectScore(productCategoryId, selectionAttribute);
        return BaseResult.success(score);
    }

    @POST
    @Path("/reduced-fuzzy-search")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "根品牌名称或者标签名称模糊查询品牌列表", notes = "根品牌名称或者标签名称模糊查询品牌列表", httpMethod = "POST")
    public BaseResult<Object> reducedFuzzySearchBrandList(ProductBrandQueryDto productBrandQueryDto) {
        log.info("reducedFuzzySearchBrandList:{}", productBrandQueryDto);
        SpecialPageVo<ProductBrandVo> pageVO = queryService.fuzzySearchBrandList(productBrandQueryDto);
        return BaseResult.success(pageVO);
    }

    @POST
    @Path("/duplicate-name-query")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "查询品牌名称是否存在", notes = "查询品牌名称是否存在", httpMethod = "POST")
    public BaseResult<Object> duplicateBrandNameQuery(ProductBrandQueryDto productBrandQueryDto) {
        if (StringUtils.isBlank(productBrandQueryDto.getProductCategoryId()) || StringUtils.isBlank(productBrandQueryDto.getBrandName())) {
            throw new BusinessException(ProductBrandStatusCode.BRAND_PARAMETER_MISSING);
        }
        String productCategoryId = productBrandQueryDto.getProductCategoryId();
        String brandName = productBrandQueryDto.getBrandName();
        boolean result = queryService.judgingConditionBrandName(productCategoryId, brandName,productBrandQueryDto.getTagName());
        return BaseResult.success(result);
    }

    @POST
    @Path("/add")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "新增品牌", notes = "新增品牌", httpMethod = "POST")
    @DcOperationLog(method = OperationMethodEnum.ADD, module = "module-product-manager",
            targetClass = ProductBrandAddDto.class, operation = "ProductBrandAdd",mapperName= ProductBrandMapper.class)
    @DcPermission(value = {"product.brand.add"}, checkResource = true)
    public BaseResult<Object> add(ProductBrandAddDto productBrandAddDto) {
        //已在dto类中注解验证参数合法性,直接调用service业务接口
        productBrandAddDto.validate();
        int result = commandService.addBrandAndTag(productBrandAddDto);
        return result > 0 ? BaseResult.success() : BaseResult.failed();
    }

    @POST
    @Path("/single-query")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询产品小类的品牌列表", notes = "查询产品小类的品牌列表", httpMethod = "POST")
    public BaseResult<Object> fuzzyQuery(ProductBrandQueryDto productBrandQueryDto) {
        if (StringUtils.isBlank(productBrandQueryDto.getProductCategoryId())) {
            log.error("the productCategoryId is empty");
            throw new BusinessException(ProductBrandStatusCode.BRAND_PARAMETER_MISSING);
        }
        PageVO<ProductBrandVo> pageVO = queryService.detailedSearchBrandAndTagList(productBrandQueryDto);
        return BaseResult.success(pageVO);
    }

    @GET
    @Path("/get-detail/{id}")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询品牌详情", notes = "查询品牌详情", httpMethod = "GET")
    public BaseResult<Object> getDetail(@PathParam("id") String id) {
        if (StringUtils.isBlank(id)) {
            log.error("the id is empty when  query the brand");
            throw new BusinessException(ProductBrandStatusCode.BRAND_PARAMETER_MISSING);
        }
        ProductBrandVo productBrandVo = queryService.getBrandAndTagDetail(id);
        return BaseResult.success(productBrandVo);
    }

    @POST
    @Path("/update")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @DcOperationLog(method = OperationMethodEnum.UPDATE, module = "module-product-manager",
            targetClass = ProductBrandAddDto.class, operation = "ProductBrandEditDescription",mapperName= ProductBrandMapper.class)
    @ApiOperation(value = "更新/编辑品牌", notes = "更新/编辑品牌", httpMethod = "POST")
    @DcPermission(value = {"product.brand.edit"}, checkResource = true)
    public BaseResult<Object> update(ProductBrandAddDto productBrandAddDto) {
        if (StringUtils.isBlank(productBrandAddDto.getId())) {
            log.error("the id is empty when  update or edit the brand");
            throw new BusinessException(ProductBrandStatusCode.BRAND_PARAMETER_MISSING);
        }
        productBrandAddDto.validate();
        int result = commandService.updateBrandAndTag(productBrandAddDto);
        return result > 0 ? BaseResult.success() : BaseResult.failed();
    }

    @POST
    @Path("/delete/{id}")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @DcOperationLog(method = OperationMethodEnum.DELETE, module = "module-product-manager",
            targetClass = ProductBrandPo.class, operation = "ProductBrandDelete",mapperName= ProductBrandMapper.class)
    @ApiOperation(value = "删除品牌", notes = "删除品牌", httpMethod = "POST")
    @DcPermission(value = {"product.brand.delete"})
    public BaseResult<Object> delete(@PathParam("id") String id) {
        if (StringUtils.isBlank(id)) {
            log.error("the id is empty when  delete the brand");
            throw new BusinessException(ProductBrandStatusCode.BRAND_PARAMETER_MISSING);
        }
        int result = commandService.deleteBrandAndTag(id);
        return result > 0 ? BaseResult.success() : BaseResult.failed();
    }

    @POST
    @Path("/advanced-query")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询产品小类的品牌列表", notes = "查询产品小类的品牌列表", httpMethod = "POST")
    public BaseResult<Object> detailQuery(ProductBrandQueryDto productBrandQueryDto) {
        if (StringUtils.isBlank(productBrandQueryDto.getProductCategoryId())){
            log.error("the productCategoryId is empty when  query the brand");
            throw new BusinessException(ProductBrandStatusCode.BRAND_PARAMETER_MISSING);
        }
        PageVO<ProductBrandVo> productBrandVos = queryService.advancedQueryProductBrands(productBrandQueryDto);
        return BaseResult.success(productBrandVos);
    }

    @POST
    @Path("/get-purchas")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取采购模式", notes = "获取采购模式", httpMethod = "POST")
    public BaseResult<Object> getPurchas(ProductBrandQueryDto productBrandQueryDto) {
        List<BrandPurchaseModeDto> purchasList = queryService.getPurchas();
        return BaseResult.success(purchasList);
    }

    @GET
    @Path("/query-by-category")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询产品品牌列表", notes = "查询产品品牌列表", httpMethod = "GET")
    public BaseResult<List<IdNameBean>> queryAllBrandByCategory(@QueryParam("id") String id) {
        if (StringUtils.isBlank(id)) {
            log.error("The category id is empty when  query the brand.");
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
        List<IdNameBean> nameBeanList = queryService.queryAllBrandByCategory(id);
        return BaseResult.success(nameBeanList);
    }
}
