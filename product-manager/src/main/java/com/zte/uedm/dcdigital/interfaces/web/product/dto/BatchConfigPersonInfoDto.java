package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import java.util.List;


/* Started by AICoder, pid:c53ffhbb84j23b0141070aff704c026e15c0e479 */
@Getter
@Setter
@ToString
@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
public class BatchConfigPersonInfoDto {

    /**
     * 勾选的产品小类ID集合，必填
     */
    @JsonProperty("productCategoryIds")
    @NotEmpty(message = "产品小类ID集合不能为空")
    private List<ProductCategoryEditDto> productCategoryIds;

    /**
     * 产品经理、品牌管理员、采购商务等人员信息，可选
     * 如果为空表示清空所有人员配置
     */
    @JsonProperty("users")
    private List<UserDto> users;

    /**
     * 参数校验方法，确保传入的参数符合业务规则
     *
     * @throws BusinessException 如果参数不符合要求，抛出异常
     */
    public void validate() throws BusinessException {
        // 校验bean字段NotEmpty
        ValidResult validResult = ValidateUtils.validateObj(this);
        if (validResult.isError()) {
            log.error("[BatchConfigPersonInfoDto check param] Invalid parameter: {}", validResult.getErrorMessage());
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }

        // 校验产品小类ID集合不能为空
        if (CollectionUtils.isEmpty(productCategoryIds)) {
            log.error("[BatchConfigPersonInfoDto check param] productCategoryIds cannot be empty");
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }

        // 校验每个产品小类ID不能为空
        for (ProductCategoryEditDto productCategoryId : productCategoryIds) {
            if (productCategoryId.getId().trim().isEmpty()) {
                log.error("[BatchConfigPersonInfoDto check param] productCategoryId cannot be null or empty");
                throw new BusinessException(StatusCode.INVALID_PARAMETER);
            }
        }

        // 如果users不为空，校验每个UserDto
        if (!CollectionUtils.isEmpty(users)) {
            for (UserDto user : users) {
                if (user == null) {
                    log.error("[BatchConfigPersonInfoDto check param] user in users list cannot be null");
                    throw new BusinessException(StatusCode.INVALID_PARAMETER);
                }
                // 这里可以添加对UserDto的进一步校验，如果UserDto有validate方法的话
            }
        }
    }
}
/* Ended by AICoder, pid:c53ffhbb84j23b0141070aff704c026e15c0e479 */
