/* Started by AICoder, pid:kac34d3231mc890147e608d56159ad25b7973497 */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 物料历史表属性，用于记录物料的历史变更信息。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("material_historical")
public class MaterialHistoryPo {
    /**
     * 历史ID，唯一标识每条历史记录。
     */
    private String id;

    /**
     * 物料ID，关联主表的物料ID。
     */
    @TableField("material_id")
    private String materialId;

    /**
     * 审批表ID，关联特定的审批流程。
     */
    @TableField("approval_id")
    private String approvalId;

    /**
     * 物料名称，描述物料的基本名称。
     */
    private String name;

    /**
     * 品牌，表示物料的品牌信息。
     */
    private String brand;

    /**
     * 供应商，表示提供物料的供应商信息。
     */
    private String supplier;

    /**
     * 采购模式，表示物料的采购方式。
     */
    @TableField("purchase_mode")
    private String purchaseMode;

    /**
     * 失效日期，表示物料的有效期截止日期。
     */
    @TableField("expiration_date")
    private String expirationDate;

    /**
     * 保质期，表示物料的保质期限。
     */
    @TableField("warranty_period")
    private String warrantyPeriod;

    /**
     * 成本费用，表示物料的成本价格。
     */
    private String cost;

    /**
     * 交期，表示物料的交付时间。
     */
    @TableField("delivery_days")
    private String deliveryDays;

    /**
     * 分组ID，用于将物料分组管理。
     */
    @TableField("group_id")
    private String groupId;

    /**
     * PDM的ID，关联PDM系统中的物料信息。
     */
    @TableField("pdm_info_id")
    private String pdmInfoId;

    /**
     * 版本，表示物料的版本信息。
     */
    private String version;

    /**
     * 推荐等级 从A、B、C三个等级中选择其一
     */
    @TableField("recommended_level")
    private String recommendedLevel;


    /**
     * 描述：
     */
    private String description;

    /**
     * 单位：
     */
    private String unit;

    /**
     * 发起人，记录发起变更的用户。
     */
    private String submitter;

    /**
     * 审批通过时间，记录物料变更审批通过的时间。
     */
    @TableField("approval_time")
    private String approvalTime;

    /**
     * 版本变更原因，记录物料版本变更的原因（例如：PDM同步或审批通过）。
     */
    @TableField("change_reason")
    private String changeReason;

    /* Started by AICoder, pid:y7d201962a09e6514ccf090480eb800096f99f22 */
    /**
     * 规格型号：表示产品的具体规格或型号，例如产品版本或技术规格。
     */
    @TableField("specification_model")
    private String specificationModel;

    /**
     * 服务：表示与产品相关的服务信息，例如售后服务、保修服务等。
     */
    private String service;

    /**
     * 生产代码，表示物料的生产代码。
     */
    private String productionCode;

    /**
     * 销售状态，表示物料的销售状态。
     */
    private String salesStatus;
    /* Ended by AICoder, pid:y7d201962a09e6514ccf090480eb800096f99f22 */
    /**
     * 创建者，记录创建该历史记录的用户。
     */
    private String createBy;

    /**
     * 更新者，记录更新该历史记录的用户。
     */
    private String updateBy;

    /**
     * 创建日期，记录历史记录的创建时间。
     */
    private String createTime;

    /**
     * 更新日期，记录历史记录的最后更新时间。
     */
    private String updateTime;
}
/* Ended by AICoder, pid:kac34d3231mc890147e608d56159ad25b7973497 */