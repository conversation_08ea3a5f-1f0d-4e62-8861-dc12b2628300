/* Started by AICoder, pid:7c80c2001bj51b3140a20b1100f94f1bee272fda */
package com.zte.uedm.dcdigital.application.material;

import com.zte.uedm.dcdigital.interfaces.web.material.dto.ProcurementCostEditDto;

/**
 * 采购命令服务接口。
 * 提供对采购成本编辑操作的业务逻辑处理。
 */
public interface ProcurementCommandService {

    /**
     * 编辑采购成本信息。
     *
     * @param editDto 包含编辑信息的数据传输对象
     */
    void edit(ProcurementCostEditDto editDto);
}
/* Ended by AICoder, pid:7c80c2001bj51b3140a20b1100f94f1bee272fda */