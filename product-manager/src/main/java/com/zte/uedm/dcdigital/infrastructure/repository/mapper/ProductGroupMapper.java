package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductGroupMapper extends BaseMapper<ProductGroupPo> {

    List<ProductGroupPo> selectByNameAndProductCategoryId(@Param("name") String name,@Param("productCategoryId") String productCategoryId);

    List<ProductGroupPo> selectByParentId(@Param("id") String id);

    List<ProductGroupPo> queryProductGroups(@Param("queryDto") ProductGroupQueryDto queryDto);

    List<ProductGroupPo> selectProductGroupAndMaterialByGroupId(@Param("id") String id);

    ProductGroupPo selectMaterialApproveByGroupId(String parentId);

    List<ProductGroupPo> selectNodeAllChildNode(@Param("id") String id);

    List<ProductGroupPo> selectChildNodeByParentId(@Param("parentId") String parentId);

    List<ProductGroupPo> selectChildNodeByParentIdList(@Param("parentIds") List<String> parentIds);
    ProductGroupEntity selectProductNodeById(@Param("id") String id);

    List<String> selectGroupIdByProductCategoryId(@Param("categoryId") String categoryId);

    List<String> selectAllChildNodeId(@Param("groupIds") List<String> groupIds);

    List<String> queryingLeafNodeGroups(@Param("categoryId") String categoryId);
    List<String> queryingLeafAllNodeGroups();

    ProductGroupPo queryProductGroup(@Param("productCategoryId") String productCategoryId,
                                     @Param("groupName") String groupName,
                                     @Param("parentId") String parentId);
}
