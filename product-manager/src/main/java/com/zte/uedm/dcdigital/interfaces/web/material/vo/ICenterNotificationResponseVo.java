package com.zte.uedm.dcdigital.interfaces.web.material.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * iCenter通知响应VO
 * 用于封装iCenter通知API的响应结果
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
/* Started by AICoder, pid:i4601bea612ad2b142a90b5220e9df8a30816339 */
public class ICenterNotificationResponseVo {

    /**
     * 响应代码对象
     */
    @ApiModelProperty(value = "响应代码对象")
    private Code code;

    /**
     * 业务对象
     */
    @ApiModelProperty(value = "业务对象")
    private Bo bo;

    /**
     * 其他信息
     */
    @ApiModelProperty(value = "其他信息")
    private Object other;

    /**
     * 响应规则
     */
    @ApiModelProperty(value = "响应规则")
    private String responseRule;

    /**
     * 响应代码类
     */
    @Getter
    @Setter
    @ToString
    public static class Code {
        /**
         * 消息编码
         * 0000表示成功，其他编码表示异常
         */
        @ApiModelProperty(value = "消息编码")
        private String code;

        /**
         * 消息值
         */
        @ApiModelProperty(value = "消息值")
        private String msgId;

        /**
         * 定义消息内容
         */
        @ApiModelProperty(value = "定义消息内容")
        private String msg;
    }

    /**
     * 业务对象类
     */
    @Getter
    @Setter
    @ToString
    public static class Bo {
        /**
         * 发送结果状态
         * 1 发送成功， 2 告警（部分发送成功）
         */
        @ApiModelProperty(value = "发送结果状态")
        private Integer status;

        /**
         * 发送结果详情
         */
        @ApiModelProperty(value = "发送结果详情")
        private Object result;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return code != null && "0000".equals(code.getCode());
    }
}
/* Ended by AICoder, pid:i4601bea612ad2b142a90b5220e9df8a30816339 */
