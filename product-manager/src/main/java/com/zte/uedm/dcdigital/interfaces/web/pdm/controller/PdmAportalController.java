package com.zte.uedm.dcdigital.interfaces.web.pdm.controller;

import com.zte.uedm.dcdigital.application.pdm.PdmApiQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.ProductLineDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.ProductLineVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

@Path("/aportal/pdm")
@Controller
public class PdmAportalController {


    @Autowired
    private PdmApiQueryService pdmApiQueryService;

    @Path("/product-line")
    @POST
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "查询PDM产品线", notes = "查询PDM产品线",httpMethod = "POST")
    public BaseResult<PageVO<ProductLineVo>> selectProductLineList(ProductLineDto dto){
        PageVO<ProductLineVo> productLineList = pdmApiQueryService.getProductLineList(dto);
        return BaseResult.success(productLineList);
    }

}
