/* Started by AICoder, pid:755b8f38bah2216147140816d0e4355033b3bae9 */
package com.zte.uedm.dcdigital.interfaces.web.material.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 材料模糊查询数据传输对象，用于前端传递材料模糊查询的参数。
 */
@Getter
@Setter
public class MaterialFuzzyQueryDto {

    /**
     * 产品类别ID，用于指定要查询的产品类别。
     */
    private String productCategoryId;

    /**
     * 分组ID列表，用于指定要查询的分组。
     */
    private List<String> groupId;

    /**
     * 选型单id，用于指定要查询的选型单。
     */
    private String lectotypeId;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     *  排序顺序
     */
    private String sortOrder;

    /**
     * 指定的物料id集
     */
    private List<String> materialIdList;
    /**
     * 搜索条件，用户输入的关键词。
     */
    private String condition;

    /**
     * 当前页码，默认为第一页。
     */
    private Integer pageNum = 1;

    /**
     * 每页显示的记录数，默认为10条。
     */
    private Integer pageSize = 10;

    /**
     * 获取关键词列表的方法。该方法将搜索条件拆分为关键词列表。
     *
     * @return 关键词列表，如果搜索条件为空或仅包含空白字符，则返回空列表。
     */
    public List<String> getKeywords() {
        if (condition == null || condition.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.asList(condition.trim().split("\\s+"));
    }
}

/* Ended by AICoder, pid:755b8f38bah2216147140816d0e4355033b3bae9 */