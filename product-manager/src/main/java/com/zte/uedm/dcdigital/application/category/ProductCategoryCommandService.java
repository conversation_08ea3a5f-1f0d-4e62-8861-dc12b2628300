/* Started by AICoder, pid:ucd7dd6cd8f0cea14bdd0a3c30b1a96a1763969a */
package com.zte.uedm.dcdigital.application.category;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryDragDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryEditDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.UserDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryTreeVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 产品分类服务接口，定义了对产品分类进行增删查改的操作。
 *
 * <AUTHOR>
 */
public interface ProductCategoryCommandService {

    /**
     * 添加新的产品分类。
     *
     * @param productCategoryAddDto  包含新增产品分类信息的DTO对象
     * @throws BusinessException         如果添加过程中发生异常
     */
    void addProductCategory(ProductCategoryAddDto productCategoryAddDto) throws BusinessException;

    /**
     * 编辑现有产品分类。
     *
     * @param productCategoryEditDto 包含编辑后产品分类信息的DTO对象
     * @throws BusinessException         如果编辑过程中发生异常
     */
    void editProductCategory(ProductCategoryEditDto productCategoryEditDto) throws BusinessException;

    /**
     * 根据ID删除产品分类。
     *
     * @param id               产品分类的ID
     * @throws BusinessException   如果删除过程中发生异常
     */
    void deleteProductCategoryById(String id) throws BusinessException;

    /* Started by AICoder, pid:23f05ua967s0c77145f90a1460d96105c6e888e0 */
    /**
     * 更新与指定产品小类相关的用户信息。
     *
     * @param id 产品小类id。
     * @param users 包含需要更新的用户信息的列表。每个UserDto对象代表一个用户及其相关信息，如姓名、角色等。
     * @param nonStandardItems 一个字符串，包含非标准项目的信息。
     * @param extendedWarrantyFactor 延保系数取值范围从1到10，保留两位小数。
     * @param materialType 物料类型.
     */
    void updateRelatedUser(String id, List<UserDto> users, String nonStandardItems, BigDecimal extendedWarrantyFactor, String materialType);
    /* Ended by AICoder, pid:ucd7dd6cd8f0cea14bdd0a3c30b1a96a1763969a */


    /**
     * 拖拽产品分类。
     *
     * @param dragDto 包含拖拽操作信息的DTO对象
     * @throws BusinessException         如果拖拽过程中发生异常
     */
    void drag(ProductCategoryDragDto dragDto);
    /* Ended by AICoder, pid:23f05ua967s0c77145f90a1460d96105c6e888e0 */

    /**
     * 查询产品分类树。
     *
     * @return 包含产品分类树信息的列表
     */
    List<ProductCategoryTreeVo> queryProductCategoryTree();

    /**
     * 批量配置产品分类的人员信息
     *
     * @param productCategoryIds 产品小类ID集合
     * @param users 用户信息列表，如果为空表示清空所有人员配置
     */
    void batchConfigPersonInfo(List<ProductCategoryEditDto> productCategoryIds, List<UserDto> users);

}
/* Ended by AICoder, pid:ucd7dd6cd8f0cea14bdd0a3c30b1a96a1763969a */