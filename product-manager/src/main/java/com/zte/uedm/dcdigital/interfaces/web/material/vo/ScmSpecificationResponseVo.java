package com.zte.uedm.dcdigital.interfaces.web.material.vo;
/* Started by AICoder, pid:gf77co1d217760c14e340b0461832824d409569a */
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * 物料拆分信息，包含代码、业务对象（BO）和其他信息。
 */
@Getter
@Setter
@ToString
@Slf4j
public class ScmSpecificationResponseVo {

    /**
     * 响应代码，包含状态码、消息ID和消息内容。
     */
    private Code code;

    /**
     * 业务对象（BO），可以是单个对象或对象列表。
     * 使用自定义反序列化器处理 JSON 数组或单个对象。
     */
    @JsonDeserialize(using = BoDeserializer.class)
    private Object bo;

    /**
     * 其他信息，如分页数据。
     */
    private Other other;

    /**
     * 获取业务对象列表的方法。
     * 如果 bo 是列表，则返回该列表；否则返回空列表。
     *
     * @return 业务对象列表
     */
    public List<BO> getBoList() {
        if (bo instanceof List) {
            return (List<BO>) bo;
        }
        return Collections.emptyList();
    }

    /**
     * 响应代码类，包含状态码、消息ID和消息内容。
     */
    @Getter
    @Setter
    @ToString
    public static class Code {
        private String code;       // 状态码
        private String msgId;      // 消息ID
        private String msg;        // 消息内容
    }

    /**
     * 业务对象类，包含产品销售的详细信息。
     */
    @Getter
    @Setter
    @ToString
    public static class BO {
        private String itemId;                     // 项目ID
        private String itemNo;                     // 项目编号
        private String itemName;                   // 项目名称
        private String itemLicenseexception;       // 许可例外信息
        private String itemUuid;                   // 项目UUID
        private String supplierNo;                 // 供应商编号
        private String supplierName;               // 供应商名称
        private String brandStyle;                 // 品牌样式
        private String brandEnstyle;               // 品牌英文样式
        private String brandNo;                    // 品牌编号
        private String brandName;                  // 品牌名称
        private String brandEnname;                // 品牌英文名称
        private String specwordFile;               // 规格Word文件
        private String specpdfFile;                // 规格PDF文件
        private String classNo;                    // 分类编号
        private String param;                      // 参数
    }

    /**
     * 包装类，用于封装BO列表。
     */
    @Getter
    @Setter
    @ToString
    public static class ScmBO {
        private List<BO> list;                     // 业务对象列表
    }

    /**
     * 自定义反序列化器，用于将 JSON 数组或单个对象反序列化为 BO 列表。
     */
    public static class BoDeserializer extends JsonDeserializer<List<BO>> {

        @Override
        public List<BO> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            JsonNode node = p.getCodec().readTree(p);
            ObjectMapper objectMapper = (ObjectMapper) p.getCodec();
            ScmBO  scmbo = objectMapper.treeToValue(node, ScmBO.class);
            if(null!=scmbo.getList()&&scmbo.getList().size()>0){
                return scmbo.getList();
            }else {
                log.debug(JSON.toJSONString(node));
            }
            return Collections.emptyList();
        }
    }

    /**
     * 其他信息类，包含分页数据。
     */
    @Getter
    @Setter
    @ToString
    public static class Other {
        private int current;       // 当前页
        private int pageCount;     // 总页数
        private int total;         // 总记录数
        private int pageSize;      // 每页记录数
    }
}
/* Ended by AICoder, pid:gf77co1d217760c14e340b0461832824d409569a */