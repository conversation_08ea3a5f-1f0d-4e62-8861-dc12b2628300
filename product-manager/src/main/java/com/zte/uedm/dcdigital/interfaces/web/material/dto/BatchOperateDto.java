/* Started by AICoder, pid:oc497m8412dcb91146d90a21d0b803482633f0ac */
package com.zte.uedm.dcdigital.interfaces.web.material.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量上下架 批量提交变更 DTO 类
 *
 * 该类用于批量操作，如批量上架、下架或提交变更。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BatchOperateDto {

    /**
     * 操作类型，例如：批量上架、批量下架、批量提交变更。
     *
     * @ApiModelProperty 注解用于 Swagger 文档生成，描述字段的含义。
     * @NotBlank 确保该字段不能为空，否则会抛出验证异常。
     */
    @ApiModelProperty(value = "批量操作，批量上架，批量下架，批量提交变更")
    @NotBlank(message = "operate必填, operate is required")
    private String operate;

    @ApiModelProperty(value = "是否申请oth，0 否，1是,上架时必填")
    private Integer applyOth;

    /**
     * 物料 ID 列表。
     *
     * @ApiModelProperty 注解用于 Swagger 文档生成，描述字段的含义。
     * @NotEmpty 确保该列表不为空且不包含空元素，否则会抛出验证异常。
     */
    @ApiModelProperty(value = "物料ids，物料id")
    @NotEmpty(message = "物料ID列表不能为空且不能为空列表")
    private List<String> ids;
}
/* Ended by AICoder, pid:oc497m8412dcb91146d90a21d0b803482633f0ac */