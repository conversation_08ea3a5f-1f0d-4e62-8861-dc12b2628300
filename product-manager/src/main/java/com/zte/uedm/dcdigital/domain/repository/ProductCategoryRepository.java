/* Started by AICoder, pid:a5680bbc1eub0a6143300a5291d87f1522e06f54 */
package com.zte.uedm.dcdigital.domain.repository;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.MaterialStatisticsQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductSubcategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.MaterialStatisticsVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryTreeVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductSubcategoryWithCategoryVo;

import java.util.List;

/**
 * 产品分类的仓储接口，定义了对产品分类进行增删查改的操作。
 *
 * <AUTHOR>
 */
public interface ProductCategoryRepository {

    /**
     * 根据ID查询产品分类实体。
     *
     * @param id 产品分类的ID
     * @return 包含产品分类信息的实体对象
     * @throws BusinessException 如果查询过程中发生异常
     */
    ProductCategoryEntity queryById(String id) throws BusinessException;



    /**
     * 根据ID删除产品分类。
     *
     * @param id 产品分类的ID
     * @throws BusinessException 如果删除过程中发生异常
     */
    void deleteById(String id) throws BusinessException;



    /**
     * 添加新的产品分类。
     *
     * @param categoryEntity 包含产品分类信息的实体对象
     * @throws BusinessException 如果添加过程中发生异常
     */
    void addProductCategory(ProductCategoryEntity categoryEntity) throws BusinessException;


    /**
     * 更新现有产品分类。
     *
     * @param categoryEntity 包含更新后产品分类信息的实体对象
     * @throws BusinessException 如果更新过程中发生异常
     */
    void updateProductCategory(ProductCategoryEntity categoryEntity) throws BusinessException;

    /**
     * 根据当前id查询当前节点节点子子节点数据
     * @param currentNodeId 当前节点id
     * @return
     */
    List<ProductCategoryEntity> selectCurentNodeAllChildNode(String currentNodeId);

    /**
     * 批量修改
     * @param productCategoryEntities 批量修改pathname
     */
    void updateBatch(List<ProductCategoryEntity> productCategoryEntities);

    /**
     * 根据条件查询产品分类列表。
     *
     * @param queryDto 查询参数
     * @return 包含符合条件的产品分类实体对象列表
     * @throws BusinessException 如果查询过程中发生异常
     */
    PageInfo<ProductCategoryEntity> queryByCondition(ProductCategoryQueryDto queryDto) throws BusinessException;


    /**
     * 根据名称和排除的ID统计产品分类数量。
     *
     * @param name 产品分类名称
     * @param id 排除的ID
     * @return 符合条件的产品分类数量
     * @throws BusinessException 如果查询过程中发生异常
     */
    boolean countByNameAndIdNot(String name, String id) throws BusinessException;


    ////检查当前产品分类名下是否有关联产品分类
    Long checkCorrelationRelationship(String id) throws BusinessException;


    List<ProductCategoryVo> batchQueryProductSubclasses(List<String> ids);

    List<ProductSubcategoryVo> queryUserProductSubcategory(List<String> ids);

    /* Started by AICoder, pid:19292m274bme0e014d880be91050240825e900be */
    /**
     * 验证给定名称在指定节点类型和父节点下的唯一性。
     *
     * @param name      要验证的名称，不能为空。
     * @param nodeType  节点类型，用于区分不同层级的分类，如产品线、产品大类、产品小类等。
     * @param parentId  父节点的ID，用于确定名称在特定层级下的唯一性。可以为空，表示顶级节点。
     * @return          如果名称在指定条件下是唯一的，返回true；否则返回false。
     */
    boolean nameValidation(String name, Integer nodeType, String parentId);

    /* Started by AICoder, pid:c3fead5d1d10f291462d09a7e088a50d6396e9c6 */
    /**
     * 查询所有产品子类别。
     *
     * @return List<ProductSubcategoryVo> 包含所有产品子类别的列表。
     */
    List<ProductSubcategoryVo> queryAllProductSubcategory();

    List<ProductCategoryEntity> queryByCategoryIds(List<String> parentIdList);
    /* Ended by AICoder, pid:c3fead5d1d10f291462d09a7e088a50d6396e9c6 */
    /* Ended by AICoder, pid:19292m274bme0e014d880be91050240825e900be */
    /* Started by AICoder, pid:l128d20f617c859145ea093ce02cce0bd2f79fc2 */
    /**
     * 根据产品类别ID查询品牌信息。
     *
     * @param productCategoryId 产品类别ID
     * @return 品牌信息列表
     */
    List<String> selectByCategoryId(String productCategoryId);
    /* Ended by AICoder, pid:l128d20f617c859145ea093ce02cce0bd2f79fc2 */

    /* Started by AICoder, pid:0317d2f4494eb75143fe0890b0a6210fd3770b0f */
    /**
     * 根据类别名称查询子类别信息。
     *
     * @param categoryName 类别名称
     * @return 包含子类别信息的 ProductSubcategoryVo 对象列表
     */
    List<ProductSubcategoryVo> selectByName(String categoryName);
    /* Ended by AICoder, pid:0317d2f4494eb75143fe0890b0a6210fd3770b0f */

    /**
     * 查询所有产品小类的数据列表
     * */
    List<ProductCategoryTreeVo> queryAllProductCategoryList();

    /* Started by AICoder, pid:25c1eebb093c65014cf50bea70e47c05c8e78db9 */
    /**
     * 根据节点类型查询产品子类别信息。
     *
     * @param nodeType 节点类型
     * @return 包含产品子类别信息的 ProductSubcategoryVo 对象列表
     */
    List<ProductSubcategoryVo> selectByNodeType(String nodeType);

    /**
     * 根据产品小类id、产品小类编号、产品小类父id判断当前产品编号在同产品线下是否已存在
     * @param productCategoryId 产品小类id
     * @param productNo     产品小类编号
     * @return true:产品编号已存在,false:产品编号不存在
     * */
    boolean countByProductNoAndProductParentId(String productCategoryId,String productNo,Integer nodeType,String productLineNo,String parentId);
    /* Ended by AICoder, pid:25c1eebb093c65014cf50bea70e47c05c8e78db9 */

    List<ProductCategoryInfoVo> getIdsByParentId(String parentId);

    List<String> getProductSubcategoryId();

    /**
     * 根据父级ID查询所有子孙产品小类及其所属大类信息
     *
     * @param parentId 父级ID（产品线ID或产品大类ID）
     * @return 产品小类及其所属大类信息列表
     */
    List<ProductSubcategoryWithCategoryVo> querySubcategoriesWithCategoryByParentId(String parentId);

    /**
     * 根据产品小类ID列表和时间节点范围，统计每个产品小类在指定时间范围内的物料数据指标
     *
     * @param queryDto 查询参数，包含产品小类ID列表、时间范围和时间类型
     * @param timePoints 完整的时间点列表
     * @return 包含物料统计指标的结果列表
     */
    List<MaterialStatisticsVo> queryMaterialStatisticsByTimeRange(MaterialStatisticsQueryDto queryDto, List<String> timePoints);
}
/* Ended by AICoder, pid:a5680bbc1eub0a6143300a5291d87f1522e06f54 */