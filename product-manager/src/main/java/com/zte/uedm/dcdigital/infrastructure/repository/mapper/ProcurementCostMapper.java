
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProcurementCostPo;

import org.apache.ibatis.annotations.Mapper;


/**
 * 产品分类数据访问对象（Mapper）接口。
 * 该接口继承自MyBatis Plus的BaseMapper，用于对ProductCategoryPo实体进行数据库操作。
 * 它提供了基本的CRUD（创建、读取、更新、删除）功能，并且可以通过MyBatis Plus的扩展方法实现更复杂的查询和操作。
 *
 * <AUTHOR>
 */
@Mapper
public interface ProcurementCostMapper extends BaseMapper<ProcurementCostPo> {


}
