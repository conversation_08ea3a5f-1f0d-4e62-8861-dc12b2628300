package com.zte.uedm.dcdigital.interfaces.web.material.controller;
/* Started by AICoder, pid:d0b9c8b3b1z235614b800955500af6410b31bcd7 */

import com.zte.uedm.dcdigital.application.material.DemandService;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementLectotypeUpdDto;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.DemandManagementDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.DemandManagementLectotypeDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.LectotypeMaterialAddDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.PageDemandDto;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementLectotypeVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.SelectionFormDetailsVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * DemandUportalController 类用于处理与需求管理相关的 RESTful API 请求。
 *
 * <AUTHOR>
 */
@Controller
@Path("/uportal/demand")
@Slf4j
public class DemandUportalController {

    @Autowired
    private DemandService demandService; // 注入需求服务

    /**
     * 添加新的需求管理记录。
     *
     * @param demandManagementDto 包含需求管理信息的数据传输对象
     * @return 操作结果
     */
    @POST
    @Path("/add")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "需求新增", notes = "需求新增", httpMethod = "POST")
    public BaseResult<Object> addDemand(DemandManagementDto demandManagementDto) {
        demandService.addDemand(demandManagementDto); // 调用服务层方法添加需求
        return BaseResult.success(); // 返回成功结果
    }

    /* Started by AICoder, pid:qf9e9ofb4b9ebae14dc90b9e61feb19b7ac00a0b */


    /**
     * 分页查询需求列表。
     *
     * @param pageDemandDto 包含分页和过滤条件的数据传输对象
     * @return 查询结果
     */
    @POST
    @Path("/pageDemand")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "分页查询需求列表", notes = "分页查询需求列表", httpMethod = "POST")
    public BaseResult<Object> pageDemand(@Validated PageDemandDto pageDemandDto) {
        return BaseResult.success(demandService.pageDemand(pageDemandDto));
    }

    /**
     * 分页查询需求工程量清单。
     *
     * @param demandId 需求ID
     * @param page     当前页码
     * @param size     每页大小
     * @return 查询结果
     */
    @GET
    @Path("/pageQuantity")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "分页查询需求工程量清单", notes = "分页查询需求工程量清单", httpMethod = "GET")
    public BaseResult<Object> pageQuantity(@QueryParam("demandId") String demandId,
                                           @QueryParam("page") Integer page,
                                           @QueryParam("size") Integer size) {
        return BaseResult.success(demandService.pageQuantity(demandId, page, size));
    }

    /**
     * 查询需求选型清单。
     *
     * @param demandId 需求ID
     * @param page     当前页码
     * @param size     每页大小
     * @return 查询结果
     */
    @GET
    @Path("/pageLectotype")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询需求选型清单", notes = "查询需求选型清单", httpMethod = "GET")
    public BaseResult<Object> pageLectotype(@QueryParam("demandId") String demandId,
                                            @QueryParam("page") Integer page,
                                            @QueryParam("size") Integer size) {
        return BaseResult.success(demandService.pageLectotype(demandId, page, size));
    }

    /**
     * 查询需求详情。
     *
     * @param demandId 需求ID
     * @return 查询结果
     */
    @GET
    @Path("/detailDemand")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "需求详情", notes = "需求详情", httpMethod = "GET")
    public BaseResult<Object> detailDemand(@QueryParam("demandId") String demandId) {
        return BaseResult.success(demandService.detailDemand(demandId));
    }

    /**
     * 查询需求选型清单物料。
     *
     * @param lectotypeId 定标ID
     * @param page        当前页码
     * @param size        每页大小
     * @return 查询结果
     */
    @GET
    @Path("/detailDemandLectotypeMaterial")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "查询需求选型清单物料", notes = "查询需求选型清单物料", httpMethod = "GET")
    public BaseResult<Object> detailDemandLectotypeMaterial(@QueryParam("lectotypeId") String lectotypeId,
                                                            @QueryParam("page") Integer page,
                                                            @QueryParam("size") Integer size) {
        return BaseResult.success(demandService.detailDemandLectotypeMaterial(lectotypeId, page, size));
    }

    /**
     * 新增选型记录。
     *
     * @param dto 包含选型信息的数据传输对象
     * @return 操作结果
     */
    @POST
    @Path("/addLectotype")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "新增选型", notes = "新增选型", httpMethod = "POST")
    public BaseResult<Object> addLectotype(DemandManagementLectotypeDto dto) {
        demandService.addLectotype(dto);
        return BaseResult.success();
    }

    /**
     * 编辑选型记录。
     *
     * @param dto 包含更新信息的数据传输对象
     * @return 操作结果
     */
    @POST
    @Path("/updLectotype")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "编辑选型", notes = "编辑选型", httpMethod = "POST")
    public BaseResult<Object> updLectotype(DemandManagementLectotypeDto dto) {
        if (dto == null || StringUtils.isBlank(dto.getLectotypeId())) {
            log.error("lectotypeId is null");
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
        demandService.editLectotype(dto);
        return BaseResult.success();
    }

    /**
     * 删除选型记录。
     *
     * @param lectotypeId 选型ID
     * @return 操作结果
     */
    @POST
    @Path("/delLectotype")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "删除选型", notes = "删除选型", httpMethod = "POST")
    public BaseResult<Object> delLectotype(@QueryParam("lectotypeId") String lectotypeId) {
        demandService.delLectotype(lectotypeId);
        return BaseResult.success();
    }

    /**
     * 批量删除选型记录。
     *
     * @param lectotypeIds 以逗号分隔的选型ID列表
     * @return 操作结果
     */
    @POST
    @Path("/delLectotypes")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "批量删除选型", notes = "批量删除选型", httpMethod = "POST")
    public BaseResult<Object> delLectotypes(@QueryParam("lectotypeIds") String lectotypeIds) {
        demandService.delLectotypes(lectotypeIds);
        return BaseResult.success();
    }

    /**
     * 新增选型物料关联。
     *
     * @param lectotypeMaterialAddDto 包含选型物料信息的数据传输对象
     * @return 操作结果
     */
    @POST
    @Path("/addLectotypeMaterial")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "新增选型物料关联", notes = "新增选型物料关联", httpMethod = "POST")
    public BaseResult<Object> addLectotypeMaterial(LectotypeMaterialAddDto lectotypeMaterialAddDto) {
        demandService.addLectotypeMaterial(lectotypeMaterialAddDto);
        return BaseResult.success();
    }

    /**
     * 批量删除选型物料关联。
     *
     * @param lectotypeId 选型ID
     * @param materialIds 以逗号分隔的物料ID列表
     * @return 操作结果
     */
    @POST
    @Path("/delLectotypeMaterial")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "批量删除选型物料关联", notes = "批量删除选型物料关联", httpMethod = "POST")
    public BaseResult<Object> delLectotypeMaterial(@QueryParam("lectotypeId") String lectotypeId,
                                                   @QueryParam("materialIds") String materialIds) {
        demandService.delLectotypeMaterial(lectotypeId, materialIds);
        return BaseResult.success();
    }
    /* Ended by AICoder, pid:qf9e9ofb4b9ebae14dc90b9e61feb19b7ac00a0b */

    /**
     * 选型单状态枚举查询
     * @return 状态集
     */
    @GET
    @Path("/get-all-status")
    @Produces({MediaType.APPLICATION_JSON})
    @ApiOperation(value = "查询选型单所有状态", notes = "查询选型单所有状态", httpMethod = "GET")
    public BaseResult<List<IdNameBean>> queryAllStatus() {
        List<IdNameBean> idNameBeanList = demandService.queryAllStatus();
        return BaseResult.success(idNameBeanList);
    }

    /**
     * 选型单详情查询
     * @return 选型单详情
     */
    @GET
    @Path("/get-by-id")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "选型单详情查询", notes = "选型单详情查询", httpMethod = "GET")
    public BaseResult<SelectionFormDetailsVo> getSelectionFormById(@QueryParam("id") String id) {
        return BaseResult.success(demandService.querySelectionFormById(id));
    }

    @POST
    @Path("/onceSubmit")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "一键上架", notes = "一键上架", httpMethod = "POST")
    public BaseResult<Object> onceSubmit(@QueryParam("lectotypeId") String lectotypeId) {
        demandService.onceSubmit(lectotypeId);
        return BaseResult.success();
    }

    @GET
    @Path("/getDemandManagementLectotypeById")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "根据选型单获取需求单", notes = "根据选型单获取需求单", httpMethod = "GET")
    public BaseResult<Object> getDemandManagementLectotypeById(@QueryParam("lectotypeId") String lectotypeId) {
        return BaseResult.success(demandService.getDemandManagementLectotypeById(lectotypeId));
    }
}
/* Ended by AICoder, pid:d0b9c8b3b1z235614b800955500af6410b31bcd7 */
