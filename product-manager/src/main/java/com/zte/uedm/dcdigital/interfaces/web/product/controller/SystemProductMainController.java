/* Started by AICoder, pid:2574ci177fhb60b14e5009fbb011c740a9e58d7a */
package com.zte.uedm.dcdigital.interfaces.web.product.controller;

import com.zte.uedm.dcdigital.application.material.impl.SystemProductMainServiceImpl;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductDataVo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.StaticsRequestBean;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 */
@Path("uportal/page-product")
@Controller
@Slf4j
public class SystemProductMainController {

    @Autowired
    private SystemProductMainServiceImpl systemProductMainServiceImpl;

    /**
     * 添加强回声。
     *
     * @param staticsRequestBean 包含强回声信息的 DTO 对象
     * @return 操作结果的 BaseResult 对象
     */
    @POST
    @Path("/product-all")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "获取统计数据", notes = "获取统计数据", httpMethod = "POST")
    public BaseResult<Object> getProductStaticsData(StaticsRequestBean staticsRequestBean){
        ProductDataVo productDataVo = systemProductMainServiceImpl.getProductDataVoByIDTime(staticsRequestBean);
        return BaseResult.success(productDataVo);
    }

}
/* Ended by AICoder, pid:2574ci177fhb60b14e5009fbb011c740a9e58d7a */