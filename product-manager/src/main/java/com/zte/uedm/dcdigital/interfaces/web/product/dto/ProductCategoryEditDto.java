/* Started by AICoder, pid:957f60ee9av46ee1421409898045aa9d1d002147 */
package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import com.zte.uedm.basis.exception.UedmException;


import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.ValidResult;
import com.zte.uedm.dcdigital.common.util.ValidateUtils;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 产品分类编辑数据传输对象，用于接收前端传入的产品分类信息。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Slf4j
public class ProductCategoryEditDto {

    /**
     * 产品编码，必填。
     */
    private String productNo;
    /**
     * 产品分类ID，必填。
     */
    @NotEmpty(message = "Product category ID cannot be empty")
    @ApiModelProperty(value = "产品分类id")
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    private String id;

    /**
     * 名称，必填。
     */
    @NotEmpty(message = "Name cannot be empty")
    @ApiModelProperty(value = "产品分类名称")
    @LogMark(range = {OperationMethodEnum.ADD,OperationMethodEnum.UPDATE})
    private String productName;

    /**
     * 描述，非必填。
     */
    @ApiModelProperty(value = "产品线描述")
    private String description;

    /* Started by AICoder, pid:642d03cc65bb920140f50ab2802a5a097d194f47 */
    /**
     * PDM产品线编号，用于标识产品线的唯一编号。
     */
    private String productLineNo;

    /**
     * PDM产品线名称，用于描述产品线的名称。
     */
    private String productLineName;
    /* Ended by AICoder, pid:642d03cc65bb920140f50ab2802a5a097d194f47 */

    /**
     * 产品梯队，枚举类型，非必填。更新产品小类的时候需要传入，其他情况为空。
     */
    @ApiModelProperty(value = "产品小类的产品梯队")
    private String productLevel;

    /**
     * 产品组成，枚举类型，非必填。更新产品小类的时候需要传入，其他情况为空。
     */
    @ApiModelProperty(value = "产品小类的产品组成")
    private String productComponent;

    /**
     * 产品经理只能选一个，非必填。更新产品小类的时候需要传入，其他情况为空。
     */
    @ApiModelProperty(value = "产品小类的产品经理")
    private ManagerDto productManager;

    /**
     * 品牌经理可多选，可不选。
     */
    @ApiModelProperty(value = "产品小类的品牌经理")
    private List<ManagerDto> brandManagers;

    /**
     * 可非标项，表示一些可以自定义或非标准化的项目。
     */
    @ApiModelProperty(value = "可非标项")
    private String nonStandardItems;

    /**
     * 材料类别
     */
    @ApiModelProperty(value = "材料类别")
    private String materialType;

    private List<UserDto> users;

    /**
     * 参数校验方法，确保传入的参数符合业务规则。
     *
     * @throws UedmException 如果参数不符合要求，抛出异常。
     */
    public void parameterVerification() throws BusinessException {
        // 校验bean字段NotEmpty

        ValidResult validResult = ValidateUtils.validateObj(this);
        if (validResult.isError()) {
            log.error("[ProductCategoryEditDto check param] Invalid parameter: {}", validResult.getErrorMessage());
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }

        // 校验名称长度是否超过限制
        if (productName.length() > GlobalConstants.NAME_LENGTH) {
            log.error("Name length exceeds the limit.");
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }

        // 校验产品编码长度是否超过限制
//        if (productNo.length() > GlobalConstants.TEN) {
//            log.error("Name length exceeds the limit.");
//            throw new BusinessException(StatusCode.INVALID_PARAMETER);
//        }

        // 校验描述长度是否超过限制
        if (StringUtils.isNotBlank(description) && description.length() > GlobalConstants.DESC_LENGTH) {
            log.error("Description length exceeds the limit.");
            throw new BusinessException(StatusCode.INVALID_PARAMETER);
        }
    }
}
/* Ended by AICoder, pid:957f60ee9av46ee1421409898045aa9d1d002147 */