package com.zte.uedm.dcdigital.interfaces.web.pdm.vo;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * 产品销售响应对象，包含代码、业务对象（BO）和其他信息。
 */
@Getter
@Setter
@ToString
public class ProductSaleResponseVo {

    /**
     * 响应代码，包含状态码、消息ID和消息内容。
     */
    private Code code;

    /**
     * 业务对象（BO），可以是单个对象或对象列表。
     * 使用自定义反序列化器处理 JSON 数组或单个对象。
     */
    @JsonDeserialize(using = BoDeserializer.class)
    private Object bo;

    /**
     * 其他信息，如分页数据。
     */
    private Other other;

    /**
     * 获取业务对象列表的方法。
     * 如果 bo 是列表，则返回该列表；否则返回空列表。
     *
     * @return 业务对象列表
     */
    public List<BO> getBoList() {
        if (bo instanceof List) {
            return (List<BO>) bo;
        }
        return Collections.emptyList();
    }

    /**
     * 响应代码类，包含状态码、消息ID和消息内容。
     */
    @Getter
    @Setter
    @ToString
    public static class Code {
        private String code;       // 状态码
        private String msgId;      // 消息ID
        private String msg;        // 消息内容
    }

    /**
     * 业务对象类，包含产品销售的详细信息。
     */
    @Getter
    @Setter
    @ToString
    public static class BO {
        private String basicNo;            // 基本编号
        private String basicNameCn;        // 基本名称（中文）
        private String basicNameCnIn;      // 基本名称（中文内部）
        private String basicNameEn;        // 基本名称（英文）
        private String basicNameEnIn;      // 基本名称（英文内部）
        private String middleNo;           // 中间编号
        private String middleNameCn;       // 中间名称（中文）
        private String middleNameCnIn;     // 中间名称（中文内部）
        private String modelNo;            // 机型编号
        private String modelNameCn;        // 机型名称（中文）
        private String modelNameEn;        // 机型名称（英文）
        private String saleStatusName;     // 销售状态名称
        private String ptoNo;              // PTO 编号
        private String ptoName;            // PTO 名称
        private String productModelNo;     // 产品型号编号
        private String customerName;       // 客户名称
    }

    /**
     * 自定义反序列化器，用于将 JSON 数组或单个对象反序列化为 BO 列表。
     */
    public static class BoDeserializer extends JsonDeserializer<List<BO>> {

        @Override
        public List<ProductSaleResponseVo.BO> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            JsonNode node = p.getCodec().readTree(p);
            ObjectMapper objectMapper = (ObjectMapper) p.getCodec();

            if (node.isArray()) {
                // 如果是数组，使用 readValue 方法并传递 TypeReference
                return objectMapper.readValue(node.traverse(), new TypeReference<List<ProductSaleResponseVo.BO>>() {});
            } else if (node.isObject()) {
                // 如果是单个对象，创建一个包含该对象的列表
                ProductSaleResponseVo.BO bo = objectMapper.treeToValue(node, ProductSaleResponseVo.BO.class);
                return Collections.singletonList(bo);
            }
            return Collections.emptyList();
        }
    }

    /**
     * 其他信息类，包含分页数据。
     */
    @Getter
    @Setter
    @ToString
    public static class Other {
        private int current;       // 当前页
        private int pageCount;     // 总页数
        private int total;         // 总记录数
        private int pageSize;      // 每页记录数
    }
}
