/* Started by AICoder, pid:g4f29a03c4862f514f3e0bac3014444a65d49ff9 */
package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 产品中品牌管理员信息的封装类。
 * 该类用于表示品牌管理员的基本信息，包括ID、姓名、电子邮件、员工ID和电话号码。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class ManagerDto {

    /**
     * 管理员的唯一标识符。
     */
    @ApiModelProperty(value = "管理员id")
    private String id;

    /**
     * 管理员的姓名。
     */
    @ApiModelProperty(value = "管理员名称")
    private String name;

    /**
     * 管理员的电子邮件地址。
     */
    @ApiModelProperty(value = "管理员邮箱")
    private String email;

    /**
     * 管理员的员工ID。
     */
    @ApiModelProperty(value = "管理员工号")
    private String employeeId;

    /**
     * 管理员的电话号码。
     */
    @ApiModelProperty(value = "管理员电话")
    private String phoneNumber;

    /**
     * 管理员的权限角色code
     */
    @ApiModelProperty(value = "角色code")
    private String roleCode;

}
/* Ended by AICoder, pid:g4f29a03c4862f514f3e0bac3014444a65d49ff9 */