package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zte.uedm.dcdigital.application.configuration.converter.RequirementDashboardConvert;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.model.product.entity.RequirementDashboardEntity;
import com.zte.uedm.dcdigital.domain.repository.RequirementDashboardRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.RequirementDashboardMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.RequirementDashboardPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.DashboardConditionQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.DashboardQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.RequirementDashboardVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class RequirementDashboardRepositoryImpl extends ServiceImpl<RequirementDashboardMapper, RequirementDashboardPo> implements RequirementDashboardRepository {


    @Override
    public RequirementDashboardEntity queryByProductAndProject(DashboardQueryDto queryDto) {
        RequirementDashboardPo requirementDashboardPo = baseMapper.selectByProjectIdAndCategoryId(queryDto.getProjectId(),queryDto.getProductCategoryId());
        return RequirementDashboardConvert.INSTANCE.poToEntity(requirementDashboardPo);
    }

    @Override
    public RequirementDashboardEntity queryBoardById(String id) {
        RequirementDashboardPo requirementDashboardPo = baseMapper.queryRequirementDashboardDetailById(id);
        return RequirementDashboardConvert.INSTANCE.poToEntity(requirementDashboardPo);
    }

    @Override
    public void updateOrInsert(boolean isUpdate, RequirementDashboardEntity dashboardEntity) {
        RequirementDashboardPo requirementDashboardPo = RequirementDashboardConvert.INSTANCE.entityToPo(dashboardEntity);
        log.info("updateOrInsert isUpdate:{}, requirementDashboardPo:{}", isUpdate,requirementDashboardPo);
        if (isUpdate) {
            baseMapper.updateById(requirementDashboardPo);
        } else {
            baseMapper.insert(requirementDashboardPo);
        }
    }

    @Override
    public PageVO<RequirementDashboardVo> queryByProjectAndCondition(DashboardConditionQueryDto queryDto, List<String> productCategoryIdList) {
        if (CollectionUtils.isEmpty(productCategoryIdList)) {
            return new PageVO<>(0, Collections.emptyList());
        }
        Page<RequirementDashboardVo> page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());

        List<RequirementDashboardVo> productCategoryVos = baseMapper.queryByProjectAndCondition(queryDto.getProjectId(),queryDto.getStatus(),productCategoryIdList);
        return new PageVO<>(page.getTotal(),productCategoryVos);
    }

    @Override
    public PageVO<RequirementDashboardVo> queryByCancelCondition(DashboardConditionQueryDto queryDto, List<String> productCategoryIdList) {
        if (CollectionUtils.isEmpty(productCategoryIdList)) {
            return new PageVO<>(0, Collections.emptyList());
        }
        Page<RequirementDashboardVo> page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        //需求看板没有记录时，查询为空
        List<RequirementDashboardVo> productCategoryVos = baseMapper.queryByCancelCondition(queryDto.getProjectId(),queryDto.getStatus(),productCategoryIdList);
        return new PageVO<>(page.getTotal(),productCategoryVos);
    }
}

