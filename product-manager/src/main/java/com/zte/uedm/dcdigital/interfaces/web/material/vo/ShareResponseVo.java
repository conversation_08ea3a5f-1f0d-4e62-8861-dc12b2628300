package com.zte.uedm.dcdigital.interfaces.web.material.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 分享响应VO
 * 用于封装分享操作的响应结果
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
/* Started by AICoder, pid:uce3dfaa88v7ceb14fde0b1c4093c16621e0812e */
public class ShareResponseVo {

    /**
     * 分享是否成功
     */
    @ApiModelProperty(value = "分享是否成功")
    private boolean success;

    /**
     * 响应消息
     */
    @ApiModelProperty(value = "响应消息")
    private String message;

    /**
     * 发送状态
     * 1 发送成功， 2 告警（部分发送成功）
     */
    @ApiModelProperty(value = "发送状态", notes = "1:发送成功, 2:告警(部分发送成功)")
    private Integer status;

    /**
     * 发送结果详情
     */
    @ApiModelProperty(value = "发送结果详情")
    private Object result;

    /**
     * 创建成功响应
     */
    public static ShareResponseVo success(String message) {
        ShareResponseVo vo = new ShareResponseVo();
        vo.setSuccess(true);
        vo.setMessage(message);
        vo.setStatus(1);
        return vo;
    }

    /**
     * 创建成功响应（带详情）
     */
    public static ShareResponseVo success(String message, Integer status, Object result) {
        ShareResponseVo vo = new ShareResponseVo();
        vo.setSuccess(true);
        vo.setMessage(message);
        vo.setStatus(status);
        vo.setResult(result);
        return vo;
    }

    /**
     * 创建失败响应
     */
    public static ShareResponseVo failed(String message) {
        ShareResponseVo vo = new ShareResponseVo();
        vo.setSuccess(false);
        vo.setMessage(message);
        return vo;
    }
}
/* Ended by AICoder, pid:uce3dfaa88v7ceb14fde0b1c4093c16621e0812e */
