/* Started by AICoder, pid:z85372c69cl73e6147c109ecd1c0c614aec0f3f9 */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zte.uedm.dcdigital.log.annotation.LogMark;
import com.zte.uedm.dcdigital.log.domain.logenum.OperationMethodEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 物料属性，用于存储物料的基本信息和状态。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("material")
public class MaterialPo {
    /**
     * 物料ID，唯一标识每个物料。
     */
    private String id;

    /**
     * 审批表ID，关联特定的审批流程。
     */
    @TableField("approval_id")
    private String approvalId;

    /**
     * 物料名称，描述物料的名称。
     */
    @LogMark(range = {OperationMethodEnum.DELETE,OperationMethodEnum.UPDATE})
    private String name;
    private String nameEn;

    /**
     * 品牌，表示物料的品牌信息。
     */
    private String brand;

    /**
     * 供应商，表示提供物料的供应商信息。
     */
    private String supplier;

    /**
     * 采购模式，表示物料的采购方式。
     */
    @TableField("purchase_mode")
    private String purchaseMode;

    /**
     * 失效日期，表示物料的有效期截止日期。
     */
    @TableField("expiration_date")
    private String expirationDate;

    /**
     * 保质期，表示物料的保质期限。
     */
    @TableField("warranty_period")
    private String warrantyPeriod;

    /**
     * 成本费用，表示物料的成本价格。
     */
    private String cost;

    /**
     * 交期，表示物料的交付时间。
     */
    @TableField("delivery_days")
    private String deliveryDays;

    /**
     * 分组ID，用于将物料分组管理。
     */
    @TableField("group_id")
    private String groupId;

    /**
     * PDM的ID，关联PDM系统中的物料信息。
     */
    @TableField("pdm_info_id")
    private String pdmInfoId;

    /**
     * 非PDM的ID，关联非PDM系统中的信息。
     */
    @TableField("oth_info_id")
    private String othInfoId;
    /**
     * 物料状态，表示物料的当前状态（例如：在库、已报废等）。
     */
    @TableField("material_status")
    @LogMark(range = {OperationMethodEnum.DELETE,OperationMethodEnum.UPDATE})
    private String materialStatus;

    /**
     * 版本，表示物料的版本信息。
     */
    private String version;
    /* Started by AICoder, pid:y7d201962a09e6514ccf090480eb800096f99f22 */
    /**
     * 规格型号：表示产品的具体规格或型号，例如产品版本或技术规格。
     */
    @TableField("specification_model")
    private String specificationModel;

    /**
     * 服务：表示与产品相关的服务信息，例如售后服务、保修服务等。
     */
    private String service;
    /* Ended by AICoder, pid:y7d201962a09e6514ccf090480eb800096f99f22 */

    /**
     * 推荐等级 从A、B、C三个等级中选择其一
     */
    @TableField("recommended_level")
    private String recommendedLevel;


    /**
     * 描述：
     */
    private String description;

    /**
     * 单位：
     */
    private String unit;
    private String unitEn;
    /**
     * 创建者，记录创建该物料记录的用户。
     */
    private String createBy;

    /**
     * 更新者，记录更新该物料记录的用户。
     */
    private String updateBy;

    /**
     * 创建日期，记录物料记录的创建时间。
     */
    private String createTime;

    /**
     * 更新日期，记录物料记录的最后更新时间。
     */
    private String updateTime;

    /**
     * 物料规格书备注。
     */
    private String specificationRemark;
}
/* Ended by AICoder, pid:z85372c69cl73e6147c109ecd1c0c614aec0f3f9 */