/* Started by AICoder, pid:s686cubd18ob0a514e05089fe013c793b35323e1 */
package com.zte.uedm.dcdigital.interfaces.web.material.dto;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/24
 * @Description:  物料精确查询请求体
 */

@ApiModel(description = "物料精确查询请求体")
@Data
public class MaterialAccurateDto{

    @ApiModelProperty(value = "产品小类id，下拉选择具体的产品小类；当前用户下有权限的小类")
    private String categoryId;

    @ApiModelProperty(value = "产品分组ids，分组id，下拉寻找；产品小类id未选择，则分组不能选择")
    private List<String> groupId;

    @ApiModelProperty(value = "物料的名称")
    private String materialName;

    @ApiModelProperty(value = "当前物料流程，枚举：草稿/上架审批中/已上架/变更中，变更审批中，下架审批中，已下架")
    private String materialStatus;

    @ApiModelProperty(value = "销售代码")
    private String salesCode;

    @ApiModelProperty(value = "生产代码")
    private String productionCode;

    @ApiModelProperty(value = "销售状态")
    private String salesStatus;

    @ApiModelProperty(value = "采购模式")
    private String purchaseMode;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "失效开始日期")
    private String startDate;

    @ApiModelProperty(value = "失效结束日期")
    private String endDate;

    @ApiModelProperty(value = "第几页")
    private Integer pageNum;

    @ApiModelProperty(value = "每页数量")
    private Integer pageSize;
}
/* Ended by AICoder, pid:s686cubd18ob0a514e05089fe013c793b35323e1 */
