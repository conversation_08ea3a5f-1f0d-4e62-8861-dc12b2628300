package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class SubProductTreeVo extends ProductTreeVo{

    private String productLevel;
    private String productNo;
    private Integer sortOrder;

    /**
     * 组件信息（仅适用于子类别），子类别产品的组件信息。
     */
    private String productComponent;

    /**
     * 可非标项，子类别产品的非标项信息。
     */
    private String nonStandardItems;
    /**
     * 所属产品线的PDM产品线编号。
     */
    private String productLineNo;

    /**
     * 所属产品线的PDM产品线名称。
     */
    private String productLineName;
    /**
     * 名称，产品小类的名称。
     */
    private String productName;

    /**
     * 描述，产品小类的描述信息。
     */
    private String description;
}
