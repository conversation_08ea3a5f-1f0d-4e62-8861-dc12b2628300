/* Started by AICoder, pid:r502886100oc78114e7e088ae0561956ebc98b56 */
package com.zte.uedm.dcdigital.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 非PDM销售代码信息表，用于存储非PDM销售代码的基本信息。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("oth_info")
public class OthInfoPo {

    /**
     * 唯一标识符，用于唯一识别每条记录。
     */
    private String id;

    /**
     * 产品的名称。
     */
    private String name;

    /**
     * 产品的销售代码，用于在销售系统中标识产品。
     */
    @TableField("sales_code")
    private String salesCode;

    /**
     * 产品的生产代码，用于在生产系统中标识产品。
     */
    @TableField("production_code")
    private String productionCode;

    /**
     * 产品的销售状态，固定值“仅限报价”。
     */
    @TableField("sales_status")
    private String salesStatus;

    /**
     * 产品的单位，例如：个、件、箱等。
     */
    private String unit;

    /**
     * 产品的描述信息，用于详细说明产品的特性或用途。
     */
    private String description;
}
/* Ended by AICoder, pid:r502886100oc78114e7e088ae0561956ebc98b56 */