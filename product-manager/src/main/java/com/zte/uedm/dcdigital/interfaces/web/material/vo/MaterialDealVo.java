package com.zte.uedm.dcdigital.interfaces.web.material.vo;

/* Started by AICoder, pid:a408d4b681f13c2149d70b8b7132c034ce25e34b */
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * MaterialDealVo 类用于表示物料处理的数据视图对象。
 * 该类包含多个字段，用于描述物料的各种属性。
 */
@Getter
@Setter
@ToString
public class MaterialDealVo {

    /**
     * 物料ID，唯一标识每个物料。
     */
    @ExcelProperty(index = 0)
    private String id;

    /**
     * 产品小类路径，表示产品小类的完整路径。
     */
    @ExcelProperty(index = 1)
    private String productPathName;

    /**
     * 分组1 ID，用于将物料分组管理。
     */
    @ExcelProperty(index = 2)
    private String groupName1;

    /**
     * 分组2 ID，用于将物料分组管理。
     */
    @ExcelProperty(index = 3)
    private String groupName2;

    /**
     * 物料名称/PDM名称，描述物料的名称。
     */
    @ExcelProperty(index = 4)
    private String name;

    /**
     * 物料名称（英文），描述物料的英文名称。
     */
    @ExcelProperty(index = 5)
    private String nameEn;

    /**
     * 品牌，表示物料的品牌信息。
     */
    @ExcelProperty(index = 6)
    private String brand;

    /**
     * 规格型号，表示产品的具体规格或型号，例如产品版本或技术规格。
     */
    @ExcelProperty(index = 7)
    private String specificationModel;

    /**
     * 采购模式，表示物料的采购方式。
     */
    @ExcelProperty(index = 8)
    private String purchaseMode;

    /**
     * 供应商，表示提供物料的供应商信息。
     */
    @ExcelProperty(index = 9)
    private String supplier;

    /**
     * 单位，表示物料的计量单位。
     */
    @ExcelProperty(index = 10)
    private String unit;

    /**
     * 描述，表示物料的详细描述。
     */
    @ExcelProperty(index = 11)
    private String description;

    /**
     * 销售代码，表示物料的销售代码。
     */
    @ExcelProperty(index = 12)
    private String salesCode;

    /**
     * 生产代码，表示物料的生产代码。
     */
    @ExcelProperty(index = 13)
    private String productionCode;

    /**
     * 销售状态，表示物料的销售状态。
     */
    @ExcelProperty(index = 14)
    private String salesStatus;

    /**
     * 失效日期，表示物料的有效期截止日期。
     */
    @ExcelProperty(index = 15)
    private String expirationDate;

    /**
     * 服务，表示物料的服务信息。
     */
    @ExcelProperty(index = 16)
    private String service;

    /**
     * 推荐等级，从 A、B、C 三个等级中选择其一。
     */
    @ExcelProperty(index = 17)
    private String recommendedLevel;

    /**
     * 检查结果，表示物料的检查结果。该字段不会导出到 Excel。
     */
    @ExcelIgnore
    private String checkResult;

    /**
     * 错误原因，表示物料检查失败的原因。该字段不会导出到 Excel。
     */
    @ExcelIgnore
    private String errorReason;

    @ExcelIgnore
    private String groupId;
}
/* Ended by AICoder, pid:a408d4b681f13c2149d70b8b7132c034ce25e34b */