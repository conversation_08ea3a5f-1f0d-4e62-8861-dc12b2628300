/* Started by AICoder, pid:d114afd09b43c59147b40a6be158e90e26b20e55 */
/* Started by AICoder, pid:g387827e34037281495209d570dc1088ff653126 */
package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 产品品牌数据传输对象 (DTO)。
 * 
 * 该类用于在系统中传递产品品牌的相关信息。
 */
@Getter
@Setter
@ToString
public class ProductBrandDto {

    /**
     * 主键，唯一标识一个品牌。
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 所属产品小类ID，关联到具体的产品。
     */
    @ApiModelProperty(value = "所属产品ID")
    private String productCategoryId;

    /**
     * 品牌名称，描述品牌的名称。
     */
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    /**
     * 选型属性，0表示优选，1表示其它。
     */
    @ApiModelProperty(value = "选型属性，0表示优选，1表示其它")
    private Integer selectionAttribute;

    /**
     * 品牌标签。
     */
    @ApiModelProperty(value = "品牌标签")
    private String tagName;

    /**
     * 选型评分，范围在1-10之间。
     */
    @ApiModelProperty(value = "选型评分，范围在1-10之间")
    private BigDecimal selectionScore;

    /**
     * 采购模式，1:自研、2:战略采购、3:项目采购、4:框架采购。
     */
    @ApiModelProperty(value = "采购模式，1:自研、2:战略采购、3:项目采购、4:框架采购")
    private String procurementMode;

    /**
     * 失效日期，格式为YYYY-MM-DD。
     */
    @ApiModelProperty(value = "失效日期，格式为YYYY-MM-DD")
    private String expiryDate;

    /**
     * 被引用的业务ID，关联到具体的业务。
     */
    @ApiModelProperty(value = "被引用的业务ID")
    private String quoteId;

    /**
     * 创建时间，记录品牌创建的时间。
     */
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    /**
     * 更新时间，记录品牌最后更新的时间。
     */
    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    /**
     * 创建用户ID，记录创建品牌的用户。
     */
    @ApiModelProperty(value = "创建用户ID")
    private String createBy;

    /**
     * 更新用户ID，记录最后更新品牌的用户。
     */
    @ApiModelProperty(value = "更新用户ID")
    private String updateBy;

}

/* Ended by AICoder, pid:g387827e34037281495209d570dc1088ff653126 */

/* Ended by AICoder, pid:d114afd09b43c59147b40a6be158e90e26b20e55 */