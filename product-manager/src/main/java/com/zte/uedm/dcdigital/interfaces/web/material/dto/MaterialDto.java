package com.zte.uedm.dcdigital.interfaces.web.material.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * 物料类型参数DTO
 * 用于封装物料分享的参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class MaterialDto {

    /**
     * 物料名
     */
    @ApiModelProperty(value = "物料名", required = true)
    @NotBlank(message = "物料名不能为空")
    private String name;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌", required = true)
    @NotBlank(message = "品牌不能为空")
    private String brand;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", required = true)
    @NotBlank(message = "描述不能为空")
    private String description;
}
