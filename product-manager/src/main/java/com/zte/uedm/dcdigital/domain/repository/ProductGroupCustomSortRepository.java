/* Started by AICoder, pid:w08907ee74x40181484d0b03407b0a28b0b23a7d */
package com.zte.uedm.dcdigital.domain.repository;

import com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupCustomSortEntity;

import java.util.List;

public interface ProductGroupCustomSortRepository {

    /**
     * 根据父节点查询排序数据
     */
    List<ProductGroupCustomSortEntity> findByParentId(String groupParentId);

    /**
     * 根据父节点删除排序数据
     */
    void deleteByParentId(String groupParentId);

    /**
     * 批量插入排序数据
     */
    void batchInsertCustomSort(List<ProductGroupCustomSortEntity> newSorts);

    /**
     *  根据分组id查询排序数据
     * */
    List<ProductGroupCustomSortEntity> findCustomSortByGroupIds(List<String> groupIds);

    void deleteByParentIds(List<String> parentIds);
}

/* Ended by AICoder, pid:w08907ee74x40181484d0b03407b0a28b0b23a7d */