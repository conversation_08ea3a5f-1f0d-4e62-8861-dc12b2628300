/* Started by AICoder, pid:bbba0033b3l8e55147e30b4250065499aea4f2aa */
package com.zte.uedm.dcdigital.interfaces.web.material.vo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/12/24
 * @Description:  物料模糊查询返回体
 */


@ApiModel(description = "物料模糊查询返回体")
@Data
public class MaterialFuzzyVo {

    @ApiModelProperty(value = "物料ID")
    private String id;

    @ApiModelProperty(value = "物料名称")
    private String name;

    @ApiModelProperty(value = "物料状态")
    private String materialStatus;

    @ApiModelProperty(value = "销售代码")
    private String salesCode;

    @ApiModelProperty(value = "销售状态")
    private String salesStatus;

    @ApiModelProperty(value = "生产代码")
    private String productionCode;

    @ApiModelProperty(value = "采购模式")
    private String purchaseMode;

    @ApiModelProperty(value = "失效日期")
    private String expirationDate;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "产品分组路径")
    private String groupPathName;

    @ApiModelProperty(value = "产品小类路径")
    private String categoryPathName;

    @ApiModelProperty(value = "创建时间")
    private String createTime;
}
/* Ended by AICoder, pid:bbba0033b3l8e55147e30b4250065499aea4f2aa */
