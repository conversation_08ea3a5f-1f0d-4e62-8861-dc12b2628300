package com.zte.uedm.dcdigital.infrastructure.gateway.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.common.service.JsonService;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.infrastructure.gateway.SystemManagerRpc;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.UserDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.UserRolePermissionMenuVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;


@Service
@Slf4j
/* Started by AICoder, pid:343cccd047t250114807083ea03b0b289ff69e69 */
public class SystemRpcImpl {

    @Autowired
    private SystemManagerRpc systemManagerRpc;

    @Autowired
    private JsonService jsonService;

    public UserRolePermissionMenuVo userVerify(String employeeId) {
        try {
            UserDto userDto = new UserDto();
            userDto.setEmployeeId(employeeId);
            Response<BaseResult<UserRolePermissionMenuVo>> response = systemManagerRpc.userVerify(userDto).execute();
            if (response.isSuccessful()) {
                if (response.body() != null && response.body().getCode() == 0) {
                    log.info("userVerify response: {}", JSON.toJSONString(response.body().getData()));
                    UserRolePermissionMenuVo data = jsonService.jsonToObject(jsonService.objectToJson(response.body().getData()), UserRolePermissionMenuVo.class);
                    return data;
                }
            }
        } catch (Exception e) {
            log.error("userVerify error: {}", e.getMessage(), e);
        }
        return new UserRolePermissionMenuVo();
    }
}
/* Ended by AICoder, pid:343cccd047t250114807083ea03b0b289ff69e69 */
