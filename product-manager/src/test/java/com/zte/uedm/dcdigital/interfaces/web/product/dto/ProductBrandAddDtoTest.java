package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import org.junit.Before;
import org.junit.Test;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Set;

import static org.junit.Assert.*;

public class ProductBrandAddDtoTest {

    private Validator validator;
    private ProductBrandAddDto productBrandAddDto;

    @Before
    public void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
        productBrandAddDto = new ProductBrandAddDto();
    }

    @Test
    public void testGettersAndSetters() {
        // Arrange
        String id = "123e4567";
        String productCategoryId = "123e4567-e89b-12d3-a456-426655440000";
        String brandName = "Apple";
        String tagName = "High Quality";
        Integer selectionAttribute = 0;
        BigDecimal selectionScore = new BigDecimal("10.0");
        String procurementMode = "1";
        String expiryDate = "2024-12-31";
        String quoteId = "quote123";
        String createTime = "2024-01-01";
        String updateTime = "2024-01-02";
        String createBy = "user1";
        String updateBy = "user2";

        // Act
        productBrandAddDto.setId(id);
        productBrandAddDto.setProductCategoryId(productCategoryId);
        productBrandAddDto.setBrandName(brandName);
        productBrandAddDto.setTagName(tagName);
        productBrandAddDto.setSelectionAttribute(selectionAttribute);
        productBrandAddDto.setSelectionScore(selectionScore);
        productBrandAddDto.setProcurementMode(procurementMode);
        productBrandAddDto.setExpiryDate(expiryDate);
        productBrandAddDto.setQuoteId(quoteId);
        productBrandAddDto.setCreateTime(createTime);
        productBrandAddDto.setUpdateTime(updateTime);
        productBrandAddDto.setCreateBy(createBy);
        productBrandAddDto.setUpdateBy(updateBy);

        // Assert
        assertEquals(id, productBrandAddDto.getId());
        assertEquals(productCategoryId, productBrandAddDto.getProductCategoryId());
        assertEquals(brandName, productBrandAddDto.getBrandName());
        assertEquals(tagName, productBrandAddDto.getTagName());
        assertEquals(selectionAttribute, productBrandAddDto.getSelectionAttribute());
        assertEquals(selectionScore, productBrandAddDto.getSelectionScore());
        assertEquals(procurementMode, productBrandAddDto.getProcurementMode());
        assertEquals(expiryDate, productBrandAddDto.getExpiryDate());
        assertEquals(quoteId, productBrandAddDto.getQuoteId());
        assertEquals(createTime, productBrandAddDto.getCreateTime());
        assertEquals(updateTime, productBrandAddDto.getUpdateTime());
        assertEquals(createBy, productBrandAddDto.getCreateBy());
        assertEquals(updateBy, productBrandAddDto.getUpdateBy());
    }

    @Test
    public void testValidation() {
        // Arrange
        productBrandAddDto.setProductCategoryId("123e4567-e89b-12d3-a456-426655440000");
        productBrandAddDto.setBrandName("Apple");
        productBrandAddDto.setTagName("High Quality");
        productBrandAddDto.setSelectionAttribute(0);
        productBrandAddDto.setSelectionScore(new BigDecimal("10.0"));
        productBrandAddDto.setExpiryDate("2024-12-31");

        // Act
        Set<ConstraintViolation<ProductBrandAddDto>> violations = validator.validate(productBrandAddDto);


    }

    @Test
    public void testInvalidSelectionScore() {
        // Arrange
        productBrandAddDto.setSelectionScore(new BigDecimal("11.0")); // Invalid score

        // Act
        Set<ConstraintViolation<ProductBrandAddDto>> violations = validator.validate(productBrandAddDto);

    }

    @Test
    public void testToString() {
        // Arrange
        productBrandAddDto.setId("123e4567");
        productBrandAddDto.setProductCategoryId("123e4567-e89b-12d3-a456-426655440000");
        productBrandAddDto.setBrandName("Apple");
        productBrandAddDto.setTagName("High Quality");
        productBrandAddDto.setSelectionAttribute(0);
        productBrandAddDto.setSelectionScore(new BigDecimal("10.0"));
        productBrandAddDto.setProcurementMode("1");
        productBrandAddDto.setExpiryDate("2024-12-31");
        productBrandAddDto.setQuoteId("quote123");
        productBrandAddDto.setCreateTime("2024-01-01");
        productBrandAddDto.setUpdateTime("2024-01-02");
        productBrandAddDto.setCreateBy("user1");
        productBrandAddDto.setUpdateBy("user2");

        // Act
        String result = productBrandAddDto.toString();

        // Assert
        assertTrue(result.contains("id=123e4567"));
        assertTrue(result.contains("productCategoryId=123e4567-e89b-12d3-a456-426655440000"));
        assertTrue(result.contains("brandName=Apple"));
        assertTrue(result.contains("tagName=High Quality"));
        assertTrue(result.contains("selectionAttribute=0"));
        assertTrue(result.contains("selectionScore=10.0"));
        assertTrue(result.contains("procurementMode=1"));
        assertTrue(result.contains("expiryDate=2024-12-31"));
        assertTrue(result.contains("quoteId=quote123"));
        assertTrue(result.contains("createTime=2024-01-01"));
        assertTrue(result.contains("updateTime=2024-01-02"));
        assertTrue(result.contains("createBy=user1"));
        assertTrue(result.contains("updateBy=user2"));
    }
}