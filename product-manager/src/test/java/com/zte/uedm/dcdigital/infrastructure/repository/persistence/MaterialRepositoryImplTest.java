package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

/* Started by AICoder, pid:c67ffu67cajecaa147610ba3e0a2367394658f31 */
/* Started by AICoder, pid:y7b70rd749rd4f0149ba08f9d164e33812188bbb */
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.dcdigital.common.bean.product.MaterialWithExtendInfoVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.model.material.entity.MaterialEntity;
import com.zte.uedm.dcdigital.domain.model.material.entity.MaterialWithExtendInfoEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.MaterialConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.MaterialHistoryMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.MaterialMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.MaterialPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.MaterialQueryBean;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialAccurateDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialConditionQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialFuzzyDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.MaterialFuzzyQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.MaterialVo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MaterialRepositoryImplTest {

    @InjectMocks
    private MaterialRepositoryImpl materialRepository;

    @Mock
    private MaterialMapper materialMapper;
    @Mock
    private MaterialHistoryMapper materialHistoryMapper;

    private static final String MATERIAL_ID = "material123";

    @Before
    public void setUp() {
        // Setup code if needed
    }

    @Test
    public void queryMaterialDetailsByApprovalIdTest() {
        when(materialMapper.unionQueryByApprovalId(Mockito.anyString())).thenReturn(Collections.emptyList());
        List<MaterialQueryBean> materialQueryBeans = materialRepository.queryMaterialDetailsByApprovalId(MATERIAL_ID);
        assertTrue(materialQueryBeans.isEmpty());
        List<MaterialQueryBean> beans = new ArrayList<>();
        MaterialQueryBean bean = new MaterialQueryBean();
        beans.add(bean);
        when(materialMapper.unionQueryByApprovalId(Mockito.anyString())).thenReturn(beans);
        List<MaterialQueryBean> result = materialRepository.queryMaterialDetailsByApprovalId(MATERIAL_ID);
        assertFalse(result.isEmpty());
        try {
            doThrow(new RuntimeException("Database error")).when(materialMapper).unionQueryByApprovalId(anyString());
            materialRepository.queryMaterialDetailsByApprovalId(MATERIAL_ID);
        } catch (BusinessException e) {
            assertEquals("1005", String.valueOf(e.getCode()));
        }
    }
    @Test
    public void queryByApprovalIdTest() {
        when(materialMapper.selectList(Mockito.any())).thenReturn(Collections.emptyList());
        List<MaterialEntity> materialQueryBeans = materialRepository.queryByApprovalId(MATERIAL_ID);
        assertTrue(materialQueryBeans.isEmpty());
        List<MaterialPo> beans = new ArrayList<>();
        MaterialPo bean = new MaterialPo();
        beans.add(bean);
        when(materialMapper.selectList(Mockito.any())).thenReturn(beans);
        List<MaterialEntity> result = materialRepository.queryByApprovalId(MATERIAL_ID);
        assertFalse(result.isEmpty());
        try {
            doThrow(new RuntimeException("Database error")).when(materialMapper).selectList(any());
            materialRepository.queryByApprovalId(MATERIAL_ID);
        } catch (BusinessException e) {
            assertEquals("1005", String.valueOf(e.getCode()));
        }
    }



    @Test
    public void testQueryMaterialByIds_Exception() {
        try {
            materialRepository.queryMaterialByIds(Collections.singletonList(MATERIAL_ID));
        } catch (BusinessException e) {
            assertEquals("1005", String.valueOf(e.getCode()));
        }
    }
    @Test
    public void testQueryMaterialByIds_EmptyList() {
        List<MaterialEntity> result = materialRepository.queryMaterialByIds(Collections.emptyList());
        assertTrue(result.isEmpty());
    }

    @Test
    public void testQueryMaterialByIds_NoMaterialsFound() {
        when(materialMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        List<MaterialEntity> result = materialRepository.queryMaterialByIds(Arrays.asList("1", "2"));
        assertTrue(result.isEmpty());
        verify(materialMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testQueryMaterialByIds_MaterialsFound() {
        MaterialPo mockMaterialPo = new MaterialPo();
        mockMaterialPo.setId("1");
        mockMaterialPo.setName("Material1");

        when(materialMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.singletonList(mockMaterialPo));

        MaterialEntity expectedEntity = MaterialConvert.INSTANCE.materialPoToMaterialEntity(mockMaterialPo);
        List<MaterialEntity> result = materialRepository.queryMaterialByIds(Arrays.asList("1"));

        assertEquals(1, result.size());
        assertEquals(expectedEntity.getId(), result.get(0).getId());
        assertEquals(expectedEntity.getName(), result.get(0).getName());
        verify(materialMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test(expected = BusinessException.class)
    public void testQueryMaterialByIds_ExceptionThrown() {
        when(materialMapper.selectList(any(LambdaQueryWrapper.class))).thenThrow(new RuntimeException("Database error"));

        try {
            materialRepository.queryMaterialByIds(Arrays.asList("1"));
        } catch (BusinessException e) {
            assertEquals(StatusCode.DATABASE_OPERATION_EXCEPTION.getCode(), e.getCode());
            throw e;
        }
    }

    @Test
    public void testBatchUpdateMaterials_EmptyList() {
        materialRepository.batchUpdateMaterials(Collections.emptyList());
        verify(materialMapper, never()).batchUpdateMaterialStatus(anyList());
    }

    @Test(expected = BusinessException.class)
    public void testBatchUpdateMaterials_ValidList() {
        MaterialEntity mockEntity = new MaterialEntity();
        mockEntity.setId("1");

        materialRepository.batchUpdateMaterials(Collections.singletonList(mockEntity));
    }

    @Test(expected = BusinessException.class)
    public void testBatchUpdateMaterials_ExceptionThrown() {

        try {
            materialRepository.batchUpdateMaterials(Collections.singletonList(new MaterialEntity()));
        } catch (BusinessException e) {
            assertEquals(StatusCode.DATABASE_OPERATION_EXCEPTION.getCode(), e.getCode());
            throw e;
        }
    }

    /* Ended by AICoder, pid:y7b70rd749rd4f0149ba08f9d164e33812188bbb */

    /* Started by AICoder, pid:u3c98o2c99i95e814c640acce0bbab8f717742ff */
    /**
     * 测试添加物料。
     */
    @Test
    public void given_materialEntity_when_addMaterial_then_mapperIsCalled() {
        // Given
        MaterialEntity entity = new MaterialEntity();

        // When
        materialRepository.addMaterial(entity);

        // Then
        verify(materialMapper, times(1)).insert(any(MaterialPo.class));
    }

    /**
     * 测试编辑物料。
     */
    @Test
    public void given_materialEntity_when_editMaterial_then_mapperIsCalled() {
        // Given
        MaterialEntity entity = new MaterialEntity();

        // When
        materialRepository.editMaterial(entity);

        // Then
        verify(materialMapper, times(1)).updateById(any(MaterialPo.class));
    }

    /**
     * 测试删除物料。
     */
    @Test
    public void given_materialId_when_deleteMaterial_then_mapperIsCalled() {
        // Given
        String id = "testId";

        // When
        materialRepository.deleteMaterial(id);

        // Then
        verify(materialMapper, times(1)).deleteById(id);
    }

    /**
     * 测试根据ID查询物料。
     */
    @Test
    public void given_materialId_when_queryMaterialById_then_returnsCorrectEntity() {
        // Given
        String id = "testId";
        MaterialPo materialPo = new MaterialPo();
        MaterialEntity expectedEntity = new MaterialEntity();

        when(materialMapper.selectById(id)).thenReturn(materialPo);

        // When
        MaterialEntity result = materialRepository.queryMaterialById(id);
        // Then
        assertEquals(materialPo.getId(), result.getId());
    }

    /**
     * 测试根据ID查询物料，但未找到的情况。
     */
    @Test
    public void given_nonexistentMaterialId_when_queryMaterialById_then_returnsNull() {
        // Given
        String id = "nonexistentId";

        when(materialMapper.selectById(id)).thenReturn(null);

        // When
        MaterialEntity result = materialRepository.queryMaterialById(id);

        // Then
        assertEquals(null, result);
    }
    /* Ended by AICoder, pid:u3c98o2c99i95e814c640acce0bbab8f717742ff */
    /* Started by AICoder, pid:0790f319260d79114a4e089b8087049c2620ceb0 */
    @Test
    public void testExistPdmInfoId_Exists() {
        when(materialMapper.exists(any())).thenReturn(true);
        assertTrue(materialRepository.existPdmInfoId("validId"));
    }

    /* Ended by AICoder, pid:0790f319260d79114a4e089b8087049c2620ceb0 */
    @Test
    public void testMultiTableQueryById_Found() {
        String id = "123";
        MaterialVo expectedDetail = new MaterialVo();
        expectedDetail.setId(id);
        expectedDetail.setName("Sample Material");

        when(materialMapper.queryLatestMaterialById(id)).thenReturn(expectedDetail);

        MaterialVo actualDetail = materialRepository.multiTableQueryById(id);

        assertNotNull(actualDetail);
        assertEquals(expectedDetail.getId(), actualDetail.getId());
        assertEquals(expectedDetail.getName(), actualDetail.getName());

        verify(materialMapper, times(1)).queryLatestMaterialById(id);
    }

    /* Started by AICoder, pid:j8489i024eh57a414c4e0a0810b4a8506103cec7 */
    @Test
    public void given_emptyList_when_batchAdd_then_noOperation() {
        // Given
        List<MaterialEntity> materialEntityList = Collections.emptyList();

        // When
        materialRepository.batchAdd(materialEntityList);

        // Then
        verifyZeroInteractions(materialMapper);
    }

    /**
     * 测试当输入列表为null时的情况。
     */
    @Test
    public void given_nullList_when_batchAdd_then_noOperation() {
        // Given
        List<MaterialEntity> materialEntityList = null;

        // When
        materialRepository.batchAdd(materialEntityList);

        // Then
        verifyZeroInteractions(materialMapper);
    }

    /**
     * 测试正常情况下的批量添加操作。
     */
    @Test(expected = MybatisPlusException.class)
    public void given_validList_when_batchAdd_then_success() {
        // Given
        MaterialEntity entity1 = new MaterialEntity();
        MaterialEntity entity2 = new MaterialEntity();
        List<MaterialEntity> materialEntityList = Arrays.asList(entity1, entity2);

        // When
        materialRepository.batchAdd(materialEntityList);
    }
    /* Ended by AICoder, pid:j8489i024eh57a414c4e0a0810b4a8506103cec7 */
    /* Started by AICoder, pid:xdf52z858cn169114a7c0bf670ca7e92f5816603 */
    @Test
    public void queryMaterialByConditionTest() {

        MaterialConditionQueryDto dto = new MaterialConditionQueryDto();
        dto.setMaterialStatus(new ArrayList<>());
        dto.setGroupId(new ArrayList<>());
        dto.setPurchaseMode(new ArrayList<>());
        dto.setSalesStatus(new ArrayList<>());
        materialRepository.queryMaterialByCondition(dto);

        verify(materialMapper, times(1)).queryMaterialByCondition(Mockito.any(),Mockito.anyList(),
                Mockito.anyList(),Mockito.anyList(),Mockito.anyList());
    }
    @Test
    public void selectByIdsTest() {
        materialRepository.selectByIds(Collections.singletonList("dto"));
        verify(materialMapper, times(1)).selectByIds(Mockito.anyList());
    }

    @Test
    public void selectMaterialFuzzyTest() {
        materialRepository.selectMaterialFuzzy(new MaterialFuzzyDto());
        verify(materialMapper, times(1)).selectFuzzyMaterial(Mockito.any());
    }
    @Test
    public void selectMaterialAccurateTest() {
        materialRepository.selectMaterialAccurate(new MaterialAccurateDto());
        verify(materialMapper, times(1)).selectAccurateMaterial(Mockito.any());
    }

    @Test
    public void selectByIdAndNameTest() {
        materialRepository.selectByIdAndName("ufo","nmame");
        verify(materialMapper, times(1)).selectByIdAndName(Mockito.anyString(),Mockito.anyString());
    }
    @Test
    public void queryByGroupIdTest() {
        materialRepository.queryByGroupId("new MaterialFuzzyDto()");
        verify(materialMapper, times(1)).selectList(Mockito.any());
    }
    @Test
    public void updateBatchMaterialsTest() {
        List<MaterialEntity> entity = new ArrayList<>();
        materialRepository.updateBatchMaterials(entity);
        verify(materialMapper, times(1)).updateBatchGroupIdById(Mockito.anyList());
    }

    @Test
    public void batchQueryByPdmInfoIdsTest() {
        // 使用空列表调用批量查询方法
        materialRepository.batchQueryByPdmInfoIds(new ArrayList<>());

        // 验证是否正确调用了selectList方法，并且传入了任何参数（模拟列表）
        verify(materialMapper).selectList(any());
    }
    /* Ended by AICoder, pid:xdf52z858cn169114a7c0bf670ca7e92f5816603 */
    /* Started by AICoder, pid:q8ee7o2fe9v9e3e14d09092f90960b47fb194409 */
    @Test
    public void testBatchQueryByOthIds_EmptyList() {
        List<String> othIdList = Collections.emptyList();
        List<MaterialEntity> result = materialRepository.batchQueryByOthIds(othIdList, "DRAFT");
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBatchQueryByOthIds_ValidList() {
        List<String> othIdList = Arrays.asList("1", "2", "3");
        List<MaterialPo> materialPos = Arrays.asList(new MaterialPo(), new MaterialPo());
        when(materialMapper.selectList(any())).thenReturn(materialPos);

        // 假设 MaterialConvert 是一个工具类，我们不模拟它
        List<MaterialEntity> expectedEntities = Arrays.asList(new MaterialEntity(), new MaterialEntity());

        List<MaterialEntity> result = materialRepository.batchQueryByOthIds(othIdList, "DRAFT");
        assertEquals(2, result.size());
    }

    @Test
    public void testQueryMaterialByOthName_EmptyList() {
        List<String> nameList = Collections.emptyList();
        List<MaterialEntity> result = materialRepository.queryMaterialByOthName(nameList, "DRAFT");
        assertTrue(result.isEmpty());
    }

    @Test
    public void testQueryMaterialByOthName_ValidList() {
        List<String> nameList = Arrays.asList("name1", "name2");
        List<MaterialPo> materialPos = Arrays.asList(new MaterialPo(), new MaterialPo());
        when(materialMapper.selectList(any())).thenReturn(materialPos);

        // 假设 MaterialConvert 是一个工具类，我们不模拟它
        List<MaterialEntity> expectedEntities = Arrays.asList(new MaterialEntity(), new MaterialEntity());

        List<MaterialEntity> result = materialRepository.queryMaterialByOthName(nameList, "DRAFT");
        assertEquals(2, result.size());
    }
    /* Ended by AICoder, pid:q8ee7o2fe9v9e3e14d09092f90960b47fb194409 */
    /* Started by AICoder, pid:v73ebga978a88d0142e70b0b80213a14e3c63abd */
    @Test
    public void testQueryByPdmInfoIdAndBrand_AllFields() {
        String id = "1";
        String brand = "";
        String materialId = "Material1";

        MaterialPo materialPo = new MaterialPo();
        when(materialMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.singletonList(materialPo));

        List<MaterialEntity> result = materialRepository.queryByPdmInfoIdAndBrand(id, brand, materialId);

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(materialMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testCheckNonPdmMaterialName_NameExists() {
        String name = "MaterialName";
        String id = "1";

        when(materialMapper.exists(any(LambdaQueryWrapper.class))).thenReturn(true);

        boolean result = materialRepository.checkNonPdmMaterialName(name, id);

        assertTrue(result);
        verify(materialMapper).exists(any(LambdaQueryWrapper.class));
    }
    @Test
    public void testUpdateDraftMaterialRelation() {
        String id = "1";
        String pdmInfoId = "PDM1";
        String othInfoId = "OTH1";

        doAnswer((Answer<Void>) invocation -> {
            // Verify the parameters passed to the method
            assertEquals("1", invocation.getArgument(0));
            assertEquals("PDM1", invocation.getArgument(1));
            assertEquals("OTH1", invocation.getArgument(2));
            return null;
        }).when(materialMapper).updateDraftMaterialRelation(eq(id), eq(pdmInfoId), eq(othInfoId));

        materialRepository.updateDraftMaterialRelation(id, pdmInfoId, othInfoId);

        verify(materialMapper).updateDraftMaterialRelation(eq(id), eq(pdmInfoId), eq(othInfoId));
    }
    /* Ended by AICoder, pid:y73ebsa978c88d0142e70b0b80213a14e3c83abd */
    /* Started by AICoder, pid:h034c783b5q11f414f5608e8e000b3163104f400 */
    @Test
    public void testQueryByPdmInfoIdAndStatus_WithData() {
        String pdmInfoId = "1";
        String status = "active";

        MaterialPo materialPo = new MaterialPo();
        when(materialMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.singletonList(materialPo));

        List<MaterialEntity> result = materialRepository.queryByPdmInfoIdAndStatus(pdmInfoId, status);

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(materialMapper).selectList(any(LambdaQueryWrapper.class));
    }
    /* Ended by AICoder, pid:h034c783b5q11f414f5608e8e000b3163104f400 */

    @Test
    public void queryMaterialWithExtendInfoByIds() {
        Mockito.when(materialMapper.queryMaterialWithExtendInfoByIds(Mockito.any())).thenReturn(new ArrayList<>());
        List<MaterialWithExtendInfoEntity> list = materialRepository.queryMaterialWithExtendInfoByIds(Mockito.any());
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void fuzzyQuery() {
        Mockito.when(materialMapper.fuzzyQuery(Mockito.any())).thenReturn(new ArrayList<>());
        PageInfo<MaterialVo> list = materialRepository.fuzzyQuery(new MaterialFuzzyQueryDto());
        Assert.assertTrue(list.getList().isEmpty());
    }
}
/* Ended by AICoder, pid:c67ffu67cajecaa147610ba3e0a2367394658f31 */
