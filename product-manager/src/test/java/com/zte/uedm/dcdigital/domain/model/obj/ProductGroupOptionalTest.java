package com.zte.uedm.dcdigital.domain.model.obj;

import com.zte.uedm.dcdigital.domain.model.product.vobj.ProductGroupOptional;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;


public class ProductGroupOptionalTest {

    @Test
    public void testStaticConstants() {
        Assertions.assertEquals(ProductGroupOptional.INTERFACE_ERROR.getCode(), -400);
        Assertions.assertNotNull(ProductGroupOptional.INTERFACE_ERROR.getI18nDesc());
    }


    @Test
    public void testConstructor() {
        ProductGroupOptional customError = new ProductGroupOptional(-999, "{\"zh_CN\":\"自定义错误\",\"en_US\":\"Custom error.\"}");
        Assertions.assertNotNull(customError.toString());
        Assertions.assertEquals(-999, customError.getCode());
    }

}
