/* Started by AICoder, pid:z36b2wf39b49df014216089cf0c2fa34b40287cc */
package com.zte.uedm.dcdigital.domain.common.enums;

import com.zte.uedm.dcdigital.common.web.IStatusCode;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductCategoryStatusCode;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 测试枚举类的测试用例。
 */
public class EnumsTest {

    /**
     * 测试ProductCategoryEnums枚举类的方法。
     */
    @Test
    public void testProductCategoryEnums() {
        // 断言PRODUCT_LINE的id不为空
        assertNotNull("PRODUCT_LINE的ID不应为空", ProductCategoryEnums.PRODUCT_LINE.getId());

        // 断言各个枚举值的name不为空
        assertNotNull("PRODUCT_LINE的名称不应为空", ProductCategoryEnums.PRODUCT_LINE.getName());
        assertNotNull("PRODUCT_CATEGORIES的名称不应为空", ProductCategoryEnums.PRODUCT_CATEGORIES.getName());
        assertNotNull("PRODUCT_SUBCATEGORY的名称不应为空", ProductCategoryEnums.PRODUCT_SUBCATEGORY.getName());

        // 测试通过ID获取枚举值
        ProductCategoryEnums byId = ProductCategoryEnums.getById(1);
        assertNotNull("通过ID '1' 应能找到对应的枚举值", byId);

        // 测试不存在的ID
        ProductCategoryEnums byId2 = ProductCategoryEnums.getById(5);
        assertNull("通过无效ID '5' 不应找到任何枚举值", byId2);
    }

    /* Started by AICoder, pid:r01fd38685n2f6c14bd80aa5b0027000b963f6b5 */
    @Test
    public void testProductStatusCodeEnums() {

        assertEquals(Integer.valueOf(1201), ProductCategoryStatusCode.DUPLICATE_NAME.getCode());
        assertEquals(Integer.valueOf(1202), ProductCategoryStatusCode.PARENT_PRODUCT_ERROR.getCode());
        assertEquals(Integer.valueOf(1203), ProductCategoryStatusCode.ASSOCIATION_CHECK_ERROR.getCode());
        assertEquals("StatusCode{code=1203, msg='the product also has associated subclasses'}", ProductCategoryStatusCode.ASSOCIATION_CHECK_ERROR.toString());
        IStatusCode statusCode = ProductCategoryStatusCode.DUPLICATE_NAME;
        assertEquals(Integer.valueOf(1201), statusCode.getCode());
        assertEquals("The product category name already exists", statusCode.getMessage());
    }
    /* Ended by AICoder, pid:u01fd58685d2f6c14bd80aa5b0027010b961f6b5 */
    /* Started by AICoder, pid:34457r2743k8b3b1489b0b39f0c18f436dd340f6 */
    /* Started by AICoder, pid:l2d8fj224e10af01438e0af2c0e53d5a22506e41 */
    /* Started by AICoder, pid:x8c526106bef56814a3508b4d0026f5c01a6e297 */
    /* Started by AICoder, pid:n7418x9627qa8dc1476209def00ba038cbe73f34 */
    @Test
    public void testGetId() {
        assertEquals("1", MaterialStatusEnums.DRAFT.getId());
        assertEquals("1", OperateEnums.SUBMIT.getId());
        assertEquals("1", PurchaseModeEnums.SELF_DEV.getId());
        assertEquals("0", TemplateCheckResultEnum.PASS.getCode());
        assertEquals("1", TemplateCheckResultEnum.FAIL.getCode());
        assertEquals("2", PriceCategoryEnums.DATUM_TARGET_PRICE.getCode());
        // ... 其他枚举值
    }

    @Test
    public void testGetName() {
        assertEquals("{\"zh-CN\":\"草稿\",\"en-US\":\"Draft\"}", MaterialStatusEnums.DRAFT.getName());
        assertEquals("{\"zh-CN\":\"提交\",\"en-US\":\"Submit\"}", OperateEnums.SUBMIT.getName());
        assertEquals("{\"zh-CN\":\"自研\",\"en-US\":\"Self developed\"}", PurchaseModeEnums.SELF_DEV.getName());
        // ... 其他枚举值
    }

    @Test
    public void testGetById() {
        assertEquals(MaterialStatusEnums.DRAFT, MaterialStatusEnums.getById("1"));
        assertEquals(OperateEnums.SUBMIT, OperateEnums.getById("1"));
        assertEquals(PurchaseModeEnums.SELF_DEV, PurchaseModeEnums.getById("1"));
        assertEquals(PriceCategoryEnums.NEGOTIATED_PRICE, PriceCategoryEnums.getByCode("1"));
    }

    @Test
    public void testIsInRange() {
        assertTrue(MaterialStatusEnums.isInRange("1"));
        assertTrue(OperateEnums.isInRange("1"));
        assertTrue(PurchaseModeEnums.isInRange("1"));
        assertTrue(PriceCategoryEnums.isInRange("1"));
    }
    /* Ended by AICoder, pid:n7418x9627qa8dc1476209def00ba038cbe73f34 */
    /* Ended by AICoder, pid:x8c526106bef56814a3508b4d0026f5c01a6e297 */
    /* Ended by AICoder, pid:34457r2743k8b3b1489b0b39f0c18f436dd340f6 */
    /* Ended by AICoder, pid:l2d8fj224e10af01438e0af2c0e53d5a22506e41 */
}
/* Ended by AICoder, pid:z36b2wf39b49df014216089cf0c2fa34b40287cc */