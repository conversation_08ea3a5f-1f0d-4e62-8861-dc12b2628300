package com.zte.uedm.dcdigital.application.brand.impl;/* Started by AICoder, pid:d57a9c7b45r656314799090271c6f7534924b313 */
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.common.enums.BoundaryValueEnums;
import com.zte.uedm.dcdigital.domain.common.enums.SelectionAttributeEnums;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProductBrandMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductBrandPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductBrandQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductBrandVo;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductBrandQueryServiceImplTest {
    @Mock
    private ProductBrandMapper brandMapper;

    @Mock
    private DocumentService documentService;

    @InjectMocks
    private ProductBrandQueryServiceImpl service;

    private ProductBrandQueryDto dto;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 初始化查询 DTO
        dto = new ProductBrandQueryDto();
        dto.setPageNum(1);
        dto.setPageSize(10);

//        brandPo.setId("1");
    }

    // getNewSelectScore 测试用例
    @Test
    public void testGetNewSelectScore_Preference() throws Exception {
        // 创建ProductBrandPo对象并设置其属性
        ProductBrandPo productBrandPo = new ProductBrandPo();
        productBrandPo.setSelectionScore(BigDecimal.valueOf(10));

        when(brandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(productBrandPo);

        BigDecimal result = service.getNewSelectScore("category-id", SelectionAttributeEnums.PREFERENCE.getId());
        assertEquals(BigDecimal.valueOf(9.9), result);
    }

    @Test(expected = BusinessException.class)
    public void testGetNewSelectScore_InvalidSelectionAttribute() throws Exception {
        int invalidSelectionAttribute = 999;
        try {
            service.getNewSelectScore("category-id", invalidSelectionAttribute);
        } catch (BusinessException e) {
            assertEquals("parameter error", e.getMessage());
            throw e;
        }
    }

    @Test
    public void testGetNewSelectScore_Other() throws Exception {
        // 创建ProductBrandPo对象并设置其属性
        ProductBrandPo productBrandPo = new ProductBrandPo();
        productBrandPo.setSelectionScore(BigDecimal.valueOf(10));

        // 模拟 brandMapper.selectOne 返回指定的对象
        when(brandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(productBrandPo);

        // 调用被测方法并传递 SelectionAttributeEnums.OTHER
        BigDecimal result = service.getNewSelectScore("category-id", SelectionAttributeEnums.OTHER.getId());

        // 验证结果是否符合预期
        assertEquals(BigDecimal.valueOf(10), result);
    }

    @Test
    public void testGetNewSelectScore_NoRecords() throws Exception {
        when(brandMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        BigDecimal result = service.getNewSelectScore("category-id", SelectionAttributeEnums.PREFERENCE.getId());
        assertEquals(BigDecimal.TEN, result);
    }

    @Test
    public void given_emptyResult_when_fuzzySearchBrandList_then_returnEmptyPageVO() {
        // Given: 模拟 fuzzySearchBrandIds 返回空列表
        List<String> ids = Arrays.asList("id1", "id2");
        when(brandMapper.fuzzySearchBrandIds(any())).thenReturn(ids);

        // Mock PageHelper.startPage to return a mock page object (if necessary)
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        // When: 调用模糊搜索方法
        PageVO<ProductBrandVo> result = service.fuzzySearchBrandList(dto);

        // Then: 验证返回的是一个包含数据的 PageVO
        assertNotNull(result);
        assertNull(result.getList());
        assertEquals(0, result.getTotal());
    }

    @Test
    public void given_nonEmptyResult_when_fuzzySearchBrandList_then_returnPopulatedPageVO() {
        // Given: 模拟 fuzzySearchBrandIds 返回非空列表
        List<String> ids = Arrays.asList("id1", "id2");
        ProductBrandVo brandVo=new ProductBrandVo();
        brandVo.setBrandName("testName");
        List<ProductBrandVo> productBrandVoList=new ArrayList<>();
        productBrandVoList.add(brandVo);
        when(brandMapper.fuzzySearchBrandIds(any())).thenReturn(ids);
        when(brandMapper.fuzzySearchBrandAndTagList(ids)).thenReturn(productBrandVoList);
        // When: 调用模糊搜索方法
        PageVO<ProductBrandVo> result = service.fuzzySearchBrandList(dto);

        // Then: 验证返回的是一个空的 PageVO
        assertNotNull(result);
        assertFalse(result.getList().isEmpty());
        assertEquals(0, result.getTotal());
    }

    @Test
    public void testDetailedSearchBrandAndTagList_WithResults() throws Exception {
        List<String> ids = Arrays.asList("id1");
        when(brandMapper.advancedDetailedSearchBrandIds(any(ProductBrandQueryDto.class))).thenReturn(ids);
        when(brandMapper.detailedSearchProductBrandAndTagList(ids)).thenReturn(Arrays.asList(mock(ProductBrandVo.class)));
        PageVO<?> pageVO = service.detailedSearchBrandAndTagList(new ProductBrandQueryDto());
        assertFalse(pageVO.getList().isEmpty());
    }
    @Test
    public void testFuzzySearchBrandList_WithResults() throws Exception {
        List<String> ids = Arrays.asList("id1");
        when(brandMapper.fuzzySearchBrandIds(any(ProductBrandQueryDto.class))).thenReturn(ids);
        when(brandMapper.fuzzySearchBrandAndTagList(ids)).thenReturn(Arrays.asList(mock(ProductBrandVo.class)));
        PageVO<?> pageVO = service.fuzzySearchBrandList(new ProductBrandQueryDto());
        assertFalse(pageVO.getList().isEmpty());
    }

    // getBrandAndTagDetail 测试用例
    @Test(expected = BusinessException.class)
    public void testGetBrandAndTagDetail_NotFound() throws Exception {
        when(brandMapper.selectProductBrandById("brand-id")).thenReturn(null);
        try {
            service.getBrandAndTagDetail("brand-id");
        } catch (BusinessException e) {
            assertEquals("data not found", e.getMessage());
            throw e;
        }
    }

    @Test
    public void testGetBrandAndTagDetail_Found() throws Exception {
        ProductBrandVo mockBrandVo = mock(ProductBrandVo.class);
        when(brandMapper.selectProductBrandById("brand-id")).thenReturn(mockBrandVo);
        when(brandMapper.selectTagsByBrandId("brand-id")).thenReturn(Collections.emptyList());

        ProductBrandVo result = service.getBrandAndTagDetail("brand-id");
        assertNotNull(result);
    }

    // queryBrandAssociatedDocumentList 测试用例
    @Test
    public void testQueryBrandAssociatedDocumentList() throws Exception {
        when(documentService.queryByResourceId("brand-id")).thenReturn(Collections.emptyList());
        List<?> documents = service.queryBrandAssociatedDocumentList("brand-id");
        assertNotNull(documents);
    }

    // judgingConditionBrandName 测试用例
    @Test
    public void testJudgingConditionBrandName_NotExists() throws Exception {
        when(brandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        boolean result = service.judgingConditionBrandName("category-id", "brand-name","tag");
        assertTrue(result);
    }

    @Test
    public void testJudgingConditionBrandName_Exists() throws Exception {
        when(brandMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(new ProductBrandPo()));
        boolean result = service.judgingConditionBrandName("category-id", "brand-name","tag");
        assertFalse(result);
    }

    // selectByIds 测试用例
    @Test
    public void testSelectByIds() throws Exception {
        when(brandMapper.selectByIds(anyList())).thenReturn(Collections.emptyList());
        List<?> result = service.selectByIds(Collections.singletonList("id"));
        assertNotNull(result);
    }


    /* Started by AICoder, pid:ye936m5cede913d14fd40988c08dbf74d034ef12 */
    /**
     * 测试 addScore 方法：当分数小于最大值时增加分数。
     */
    @Test
    public void given_ScoreBelowMax_when_AddScore_then_IncreaseScore() {
        ProductBrandPo brandPo=new ProductBrandPo();
        brandPo.setSelectionScore(new BigDecimal("5"));
        ProductBrandPo result = service.addScore(brandPo);
        assertEquals(new BigDecimal("5.1"), result.getSelectionScore());
    }

    /**
     * 测试 addScore 方法：当分数等于最大值时不增加分数。
     */
    @Test
    public void given_ScoreEqualsMax_when_AddScore_then_NoIncrease() {
        ProductBrandPo brandPo=new ProductBrandPo();
        brandPo.setSelectionScore(BoundaryValueEnums.MAX_VALUE.getValue());
        ProductBrandPo result = service.addScore(brandPo);
        assertEquals(BoundaryValueEnums.MAX_VALUE.getValue(), result.getSelectionScore());
    }

    /**
     * 测试 addScore 方法：当品牌对象为空时返回空。
     */
    @Test
    public void given_NullBrand_when_AddScore_then_ReturnNull() {
        ProductBrandPo result = service.addScore(null);
        assertEquals(null, result);
    }

    /**
     * 测试 subtractScore 方法：当分数大于最小值时减少分数。
     */
    @Test
    public void given_ScoreAboveMin_when_SubtractScore_then_DecreaseScore() {
        ProductBrandPo brandPo=new ProductBrandPo();
        brandPo.setSelectionScore(new BigDecimal("5"));
        ProductBrandPo result = service.subtractScore(brandPo);
        assertEquals(new BigDecimal("4.9"), result.getSelectionScore());
    }

    /**
     * 测试 subtractScore 方法：当分数等于最小值时不减少分数。
     */
    @Test
    public void given_ScoreEqualsMin_when_SubtractScore_then_NoDecrease() {
        ProductBrandPo brandPo=new ProductBrandPo();
        brandPo.setSelectionScore(BoundaryValueEnums.MIN_VALUE.getValue());
        ProductBrandPo result = service.subtractScore(brandPo);
        assertEquals(BoundaryValueEnums.MIN_VALUE.getValue(), result.getSelectionScore());
    }

    /**
     * 测试 subtractScore 方法：当品牌对象为空时返回空。
     */
    @Test
    public void given_NullBrand_when_SubtractScore_then_ReturnNull() {
        ProductBrandPo result = service.subtractScore(null);
        assertEquals(null, result);
    }
    /* Ended by AICoder, pid:ye936m5cede913d14fd40988c08dbf74d034ef12 */
}

/* Ended by AICoder, pid:d57a9c7b45r656314799090271c6f7534924b313 */