package com.zte.uedm.dcdigital.interfaces.web.material.controller;

import com.zte.uedm.dcdigital.common.web.BaseResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MaterialAportalControllerTest {
    @InjectMocks
    private MaterialAportalController materialAportalController;

    @Test
    public void getById() {
        BaseResult<Object> baseResult = materialAportalController.getById("1");
        Assert.assertTrue(baseResult.isSuccess());
    }

}