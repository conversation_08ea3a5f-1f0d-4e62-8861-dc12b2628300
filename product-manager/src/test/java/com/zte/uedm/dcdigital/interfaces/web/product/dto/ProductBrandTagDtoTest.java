package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class ProductBrandTagDtoTest {

    private ProductBrandTagDto productBrandTagDto;

    @Before
    public void setUp() {
        productBrandTagDto = new ProductBrandTagDto();
    }

    @Test
    public void testGettersAndSetters() {
        // Arrange
        String id = "123";
        String tagName = "Electronics";
        String brandId = "456";

        // Act
        productBrandTagDto.setId(id);
        productBrandTagDto.setTagName(tagName);
        productBrandTagDto.setBrandId(brandId);

        // Assert
        assertEquals("The ID should match", id, productBrandTagDto.getId());
        assertEquals("The Tag Name should match", tagName, productBrandTagDto.getTagName());
        assertEquals("The Brand ID should match", brandId, productBrandTagDto.getBrandId());
    }

    @Test
    public void testToString() {
        // Arrange
        productBrandTagDto.setId("123");
        productBrandTagDto.setTagName("Electronics");
        productBrandTagDto.setBrandId("456");

        // Act
        String result = productBrandTagDto.toString();

        // Assert
        assertTrue("toString should contain the ID", result.contains("id=123"));
        assertTrue("toString should contain the Tag Name", result.contains("tagName=Electronics"));
        assertTrue("toString should contain the Brand ID", result.contains("brandId=456"));
    }
}