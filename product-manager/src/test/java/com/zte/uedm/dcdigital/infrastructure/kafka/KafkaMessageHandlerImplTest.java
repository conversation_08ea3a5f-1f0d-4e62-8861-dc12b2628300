package com.zte.uedm.dcdigital.infrastructure.kafka;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class KafkaMessageHandlerImplTest {
    @InjectMocks
    KafkaMessageHandlerImpl kafkaMessageHandler;

    @Test
    void onMsg() {
        Assertions.assertDoesNotThrow(() -> kafkaMessageHandler.onMsg("msg"));
    }
}