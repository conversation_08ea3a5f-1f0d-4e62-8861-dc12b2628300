/* Started by AICoder, pid:vb03bbcdfdu071c1471d0891b1f7fb0abb4455e6 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.model.material.entity.MaterialHistoryEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.MaterialHistoryMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.MaterialHistoryPo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MaterialHistoryRepositoryImplTest {

    @InjectMocks
    private MaterialHistoryRepositoryImpl materialHistoryRepository;

    @Mock
    private MaterialHistoryMapper materialHistoryMapper;

    @Before
    public void setUp() {
        // Setup code if needed
    }


    @Test
    public void testBatchAddHistoryMaterial_EmptyList() {
        materialHistoryRepository.batchAddHistoryMaterial(Collections.emptyList());
        verify(materialHistoryMapper, never()).batchInsert(anyList());
    }

    @Test(expected = BusinessException.class)
    public void testBatchAddHistoryMaterial_ValidList() {
        MaterialHistoryEntity mockEntity = new MaterialHistoryEntity();
        mockEntity.setId("1");


        materialHistoryRepository.batchAddHistoryMaterial(Collections.singletonList(mockEntity));

    }

    @Test(expected = BusinessException.class)
    public void testBatchAddHistoryMaterial_ExceptionThrown() {
        doThrow(new RuntimeException("Database error")).when(materialHistoryMapper).batchInsert(anyList());

        try {
            materialHistoryRepository.batchAddHistoryMaterial(Collections.singletonList(new MaterialHistoryEntity()));
        } catch (BusinessException e) {
            assertEquals(StatusCode.DATABASE_OPERATION_EXCEPTION.getCode(), e.getCode());
            throw e;
        }
    }
    @Test
    public void testUpdateHistoryMaterial_Success() {
        MaterialHistoryEntity historyEntity = new MaterialHistoryEntity();
        historyEntity.setId("1");

        MaterialHistoryPo expectedPo = new MaterialHistoryPo();
        expectedPo.setId("1");
        doReturn(1).when(materialHistoryMapper).updateById(any(MaterialHistoryPo.class));
        materialHistoryRepository.updateHistoryMaterial(historyEntity);
        ArgumentCaptor<MaterialHistoryPo> captor = ArgumentCaptor.forClass(MaterialHistoryPo.class);
        verify(materialHistoryMapper).updateById(captor.capture());
        assertEquals(expectedPo.getId(), captor.getValue().getId());
    }

    @Test(expected = BusinessException.class)
    public void testUpdateHistoryMaterial_Exception() {
        MaterialHistoryEntity historyEntity = new MaterialHistoryEntity();
        historyEntity.setId("1");
        when(materialHistoryMapper.updateById(any())).thenThrow(new RuntimeException("Database error"));
        materialHistoryRepository.updateHistoryMaterial(historyEntity);
    }
    @Test
    public void testQueryMaterialVersionById() {
        List<MaterialHistoryPo> list = new ArrayList<>();
        MaterialHistoryPo historyEntity = new MaterialHistoryPo();
        historyEntity.setId("123");
        historyEntity.setMaterialId("1");
        list.add(historyEntity);
        when(materialHistoryMapper.selectList(any())).thenReturn(list);
        List<MaterialHistoryEntity> entities = materialHistoryRepository.queryMaterialVersionById("1");
        assertEquals(1, entities.size());
    }
    /* Started by AICoder, pid:fc5cc0e11ahfb9614bff0b0a307ce90d0d66884f */
    @Test
    public void queryByGroupIdTest() {
        materialHistoryRepository.queryByGroupId("id");
        verify(materialHistoryMapper).selectList(any());
    }
    @Test
    public void updateBatchMaterialsTest() {
        MaterialHistoryEntity mockEntity = new MaterialHistoryEntity();
        mockEntity.setId("1");

        materialHistoryRepository.updateBatchMaterials(Collections.singletonList(mockEntity));
        verify(materialHistoryMapper).updateBatchById(anyList());
    }
    /* Ended by AICoder, pid:fc5cc0e11ahfb9614bff0b0a307ce90d0d66884f */
    /* Started by AICoder, pid:j0a01oe9b53fc8f141770aee70405c162ad86b6d */
    @Test
    public void testAddHistoryMaterial() {
        MaterialHistoryEntity historyEntity = new MaterialHistoryEntity();
        historyEntity.setId("123"); // 设置一些属性以便验证

        // 调用方法
        materialHistoryRepository.addHistoryMaterial(historyEntity);

        // 验证插入方法是否被调用
        verify(materialHistoryMapper, times(1)).insert(Mockito.any());
    }
    /* Ended by AICoder, pid:j0a01oe9b53fc8f141770aee70405c162ad86b6d */
}
/* Ended by AICoder, pid:vb03bbcdfdu071c1471d0891b1f7fb0abb4455e6 */
