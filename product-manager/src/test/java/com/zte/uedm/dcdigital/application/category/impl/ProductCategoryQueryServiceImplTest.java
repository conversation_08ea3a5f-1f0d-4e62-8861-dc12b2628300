package com.zte.uedm.dcdigital.application.category.impl;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.domain.service.ProductCategoryDomainService;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductCategoryQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductSubcategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductTreeVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryManagerVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductCategoryQueryServiceImplTest {

    @InjectMocks
    private ProductCategoryQueryServiceImpl productCategoryService;

    @Mock
    private ProductCategoryDomainService productCategoryDomainService;
    @Test
    public void testQueryProductCategoryById() {
        ProductCategoryEntity vo = new ProductCategoryEntity();
        vo.setId("1");
        vo.setProductName("Test Category");;
        when(productCategoryDomainService.queryProductCategoryById(anyString())).thenReturn(vo);

        ProductCategoryVo result = productCategoryService.queryProductCategoryById("1");

        assertEquals("1", result.getId());
        assertEquals("Test Category", result.getProductName());
    }

    @Test
    public void testQueryProductCategory() {
        ProductCategoryQueryDto queryDto = new ProductCategoryQueryDto();
        ProductCategoryEntity entity = new ProductCategoryEntity();
        entity.setId("1");
        entity.setProductName("Test Category");
        PageInfo<ProductCategoryEntity> pageInfo = new PageInfo<>(Arrays.asList(entity));
        when(productCategoryDomainService.queryProductCategory(any(ProductCategoryQueryDto.class))).thenReturn(pageInfo);

        PageInfo<ProductCategoryVo> result = productCategoryService.queryProductCategory(queryDto);

        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals("1", result.getList().get(0).getId());
        assertEquals("Test Category", result.getList().get(0).getProductName());
    }

    @Test
    public void testQueryProductCategory_EmptyList() {
        ProductCategoryQueryDto queryDto = new ProductCategoryQueryDto();
        PageInfo<ProductCategoryEntity> pageInfo = new PageInfo<>(Collections.emptyList());
        when(productCategoryDomainService.queryProductCategory(any(ProductCategoryQueryDto.class))).thenReturn(pageInfo);

        PageInfo<ProductCategoryVo> result = productCategoryService.queryProductCategory(queryDto);

        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
    }
    /* Started by AICoder, pid:ofc3aucd65d46dd1418e09a7200ff8277fa25fb8 */
    @Test
    public void testGetUserProductSubcategory() {
        List<ProductSubcategoryVo> expectedList = Arrays.asList(new ProductSubcategoryVo(), new ProductSubcategoryVo());
        when(productCategoryDomainService.getUserProductSubcategory()).thenReturn(expectedList);

        List<ProductSubcategoryVo> result = productCategoryService.getUserProductSubcategory();

        assertEquals(expectedList, result);
        verify(productCategoryDomainService, times(1)).getUserProductSubcategory();

    }

    @Test
    public void testBatchQueryCategory() {
        List<String> ids = Arrays.asList("1", "2");
        List<ProductCategoryVo> expectedList = Arrays.asList(new ProductCategoryVo(), new ProductCategoryVo());
        when(productCategoryDomainService.batchQueryByIds(ids)).thenReturn(expectedList);

        List<ProductCategoryVo> result = productCategoryService.batchQueryCategory(ids);

        assertEquals(expectedList, result);
        verify(productCategoryDomainService, times(1)).batchQueryByIds(ids);
    }
    /* Ended by AICoder, pid:ofc3aucd65d46dd1418e09a7200ff8277fa25fb8 */
    /* Started by AICoder, pid:y236bgcbfe430031481209314077a026b929755b */
    @Test
    public void testQueryByIds() {
        List<String> ids = Arrays.asList("1", "2", "3");
        List<ProductCategoryInfoVo> expected = new ArrayList<>();

        when(productCategoryDomainService.queryByIds(ids)).thenReturn(expected);

        List<ProductCategoryInfoVo> result = productCategoryService.queryByIds(ids);

        assertEquals(expected, result);
        verify(productCategoryDomainService, times(1)).queryByIds(ids);
    }

    @Test
    public void testQueryProductCategoryManagerById() {
        String id = "1";
        ProductCategoryManagerVo expected = new ProductCategoryManagerVo();

        when(productCategoryDomainService.queryProductCategoryManagerById(id)).thenReturn(expected);

        ProductCategoryManagerVo result = productCategoryService.queryProductCategoryManagerById(id);

        assertEquals(expected, result);
        verify(productCategoryDomainService, times(1)).queryProductCategoryManagerById(id);
    }
    /* Ended by AICoder, pid:y236bgcbfe430031481209314077a026b929755b */
    @Test
    public void testGetAllProductSubcategory() {
        productCategoryService.getAllProductSubcategory();
        verify(productCategoryDomainService, times(1)).getAllProductSubcategory();
    }

    @Test
    public void testGetAllProductSubcategoryTree() {
        List<ProductSubcategoryVo> list = new ArrayList<>();
        ProductSubcategoryVo v1 = new ProductSubcategoryVo();
        v1.setPathName("IDC新产品/IDC配套/台式电脑/amd");
        v1.setPathId("36e583c0-169f-4043-aeb7-da3798a26157/471ac14d-c140-4336-b253-aaf66996c672/3ff88f8e-e94e-4ace-9436-f6b23705716a");
        v1.setProductLevel("1");
        v1.setNodeType(3);
        list.add(v1);
        ProductSubcategoryVo v2 = new ProductSubcategoryVo();
        v2.setPathName("IDC新产品/IDC配套/游戏本");
        v2.setPathId("36e583c0-169f-4043-aeb7-da3798a26157/471ac14d-c140-4336-b253-aaf66996c672/266a7be6-961b-4eca-b04b-72e5628b7c37");
        v2.setProductLevel("1");
        v2.setProductNo("21");
        v2.setNodeType(3);
        list.add(v2);
        ProductSubcategoryVo v3 = new ProductSubcategoryVo();
        v3.setPathName("IDC新产品1/IDC配套1/台式电脑1");
        v3.setPathId("36e583c0-169f-4043-aeb7-da3798a261571/471ac14d-c140-4336-b253-aaf66996c6721/3ff88f8e-e94e-4ace-9436-f6b23705716a1");
        v3.setProductLevel("1");
        v3.setProductNo("11");
        v3.setNodeType(3);
        list.add(v3);
        ProductSubcategoryVo v4 = new ProductSubcategoryVo();
        v4.setPathName("IDC新产品1/IDC配套1/游戏本1");
        v4.setPathId("36e583c0-169f-4043-aeb7-da3798a261571/471ac14d-c140-4336-b253-aaf66996c6721/266a7be6-961b-4eca-b04b-72e5628b7c371");
        v4.setProductLevel("1");
        v4.setProductNo("21");
        v4.setNodeType(3);
        list.add(v4);
        when(productCategoryDomainService.getAllProductSubcategory()).thenReturn(list);
        List<ProductTreeVo> ret = productCategoryService.getAllProductSubcategoryTree();
        assertEquals(2, ret.size());
    }

    @Test
    public void testQueryByPath() {
        Set<String> pathList = new HashSet<>();
        productCategoryService.queryByPath(pathList);
        verify(productCategoryDomainService, times(1)).queryByPathName(Mockito.anySet());
    }
}
