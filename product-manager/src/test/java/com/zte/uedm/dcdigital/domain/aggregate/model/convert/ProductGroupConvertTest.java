package com.zte.uedm.dcdigital.domain.aggregate.model.convert;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupEntity;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ProductGroupConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProductGroupPo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductGroupVo;
import org.junit.Test;


import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class ProductGroupConvertTest {


    /* Started by AICoder, pid:x8aeev488fd0c4814ba809e0a0497d051c59a254 */
    @Test
    public void testProductGroupDtoToEntity() {
        ProductGroupAddDto dto = new ProductGroupAddDto();
        dto.setName("Test Group");
        dto.setParentId("123");
        ProductGroupEntity entity = ProductGroupConvert.INSTANCE.productGroupDtoToEntity(dto);
        assertNotNull(entity);
        assertEquals("Test Group", entity.getName());
    }
    /* Ended by AICoder, pid:x8aeev488fd0c4814ba809e0a0497d051c59a254 */

    /* Started by AICoder, pid:m476cwf98fb1126145af09d680db421cabe07153 */
    @Test
    public void testProductGroupPoToProductGroupEntity() {
        ProductGroupPo po = new ProductGroupPo();
        po.setId("1");
        po.setName("Test Group");
        po.setParentId("123");
        ProductGroupEntity entity = ProductGroupConvert.INSTANCE.productGroupPoToProductGroupEntity(po);
        assertNotNull(entity);
        assertEquals("1", entity.getId());
    }
    /* Ended by AICoder, pid:m476cwf98fb1126145af09d680db421cabe07153 */

    /* Started by AICoder, pid:91197d23e4tce2c144e40abc902c081eb9572efa */
    @Test
    public void testListProductGroupPoToEntity() {
        List<ProductGroupPo> poList = new ArrayList<>();
        ProductGroupPo po1 = new ProductGroupPo();
        po1.setId("1");
        po1.setName("Group 1");
        po1.setParentId("123");
        poList.add(po1);
        ProductGroupPo po2 = new ProductGroupPo();
        po2.setId("2");
        po2.setName("Group 2");
        po2.setParentId("456");
        poList.add(po2);
        List<ProductGroupEntity> entityList = ProductGroupConvert.INSTANCE.listProductGroupPoToEntity(poList);
        assertNotNull(entityList);
        assertEquals(2, entityList.size());
    }
    /* Ended by AICoder, pid:91197d23e4tce2c144e40abc902c081eb9572efa */

    /* Started by AICoder, pid:3e2d0pc32bea5ad14aa008a84041ce159a227093 */
    @Test
    public void testProductGroupEntityToPo() {
        ProductGroupEntity entity = new ProductGroupEntity();
        entity.setId("1");
        entity.setName("Test Group");
        entity.setParentId("123");
        ProductGroupPo po = ProductGroupConvert.INSTANCE.productGroupEntityToPo(entity);
        assertNotNull(po);
        assertEquals("1", po.getId());
        assertEquals("Test Group", po.getName());
        assertEquals("123", po.getParentId());
    }
    /* Ended by AICoder, pid:3e2d0pc32bea5ad14aa008a84041ce159a227093 */

    /* Started by AICoder, pid:ubdfa36b68l9e42144470a21107e4a1a1db03f69 */
    @Test
    public void testProductGroupEntityToVo() {
        ProductGroupEntity entity = new ProductGroupEntity();
        entity.setId("1");
        entity.setName("Test Group");
        entity.setParentId("123");
        ProductGroupVo vo = ProductGroupConvert.INSTANCE.productGroupEntityToVo(entity);
        assertNotNull(vo);
        assertEquals("1", vo.getId());
    }
    /* Ended by AICoder, pid:ubdfa36b68l9e42144470a21107e4a1a1db03f69 */

    /* Started by AICoder, pid:yb1bbx4045oe2c0140b708a230b9e41e8627e3cd */
    @Test
    public void testProductGroupEntityListToVo() {
        List<ProductGroupEntity> entityList = new ArrayList<>();
        ProductGroupEntity entity1 = new ProductGroupEntity();
        entity1.setId("1");
        entity1.setName("Group 1");
        entity1.setParentId("123");
        entityList.add(entity1);
        ProductGroupEntity entity2 = new ProductGroupEntity();
        entity2.setId("2");
        entity2.setName("Group 2");
        entity2.setParentId("456");
        entityList.add(entity2);
        List<ProductGroupVo> voList = ProductGroupConvert.INSTANCE.productGroupEntityListToVo(entityList);
        assertNotNull(voList);
        assertEquals(2, voList.size());
    }
    /* Ended by AICoder, pid:yb1bbx4045oe2c0140b708a230b9e41e8627e3cd */
}
