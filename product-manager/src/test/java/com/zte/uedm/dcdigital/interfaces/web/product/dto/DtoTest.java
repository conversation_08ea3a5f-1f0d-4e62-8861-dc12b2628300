/* Started by AICoder, pid:11818m8f4chf1181429d08bb10e1c62515e68334 */
package com.zte.uedm.dcdigital.interfaces.web.product.dto;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.model.material.entity.*;
import com.zte.uedm.dcdigital.domain.model.material.vobj.TemplateObj;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryExtraInfoEntity;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.domain.common.utils.PojoTestUtil;
import com.zte.uedm.dcdigital.infrastructure.repository.po.*;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.*;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ManagerVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryManagerVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import org.junit.Assert;
import org.junit.Test;

public class DtoTest {
    /* Started by AICoder, pid:w692di1ab20c984149930b44e10f2629f7f5bade */
    @Test
    public void test() throws Exception {
        PojoTestUtil.TestForPojo(ProductCategoryVo.class);
        PojoTestUtil.TestForPojo(ProductCategoryExtraInfoPo.class);
        PojoTestUtil.TestForPojo(ProductCategoryPo.class);
        PojoTestUtil.TestForPojo(ProductCategoryEntity.class);
        PojoTestUtil.TestForPojo(ProductCategoryExtraInfoEntity.class);
        PojoTestUtil.TestForPojo(ManagerDto.class);
        PojoTestUtil.TestForPojo(ManagerVo.class);
        PojoTestUtil.TestForPojo(ProductCategoryManagerVo.class);
        PojoTestUtil.TestForPojo(ApprovalMaterialPo.class);
        PojoTestUtil.TestForPojo(PdmInfoPo.class);
        PojoTestUtil.TestForPojo(MaterialPo.class);
        PojoTestUtil.TestForPojo(MaterialHistoryPo.class);
        PojoTestUtil.TestForPojo(MaterialTemporaryPo.class);
        PojoTestUtil.TestForPojo(OthInfoPo.class);
        PojoTestUtil.TestForPojo(ProcurementCostEntity.class);
        PojoTestUtil.TestForPojo(ProcurementPriceHistoryEntity.class);
        PojoTestUtil.TestForPojo(ProcurementCostPo.class);
        PojoTestUtil.TestForPojo(ProcurementPriceHistoryPo.class);
        PojoTestUtil.TestForPojo(ProcurementCostEditDto.class);
        PojoTestUtil.TestForPojo(ProcurementQueryDto.class);
        PojoTestUtil.TestForPojo(PriceHistoryQueryDto.class);
        PojoTestUtil.TestForPojo(SelectionFormQueryDto.class);
        PojoTestUtil.TestForPojo(MaterialCostBatchUpdateDto.class);
        PojoTestUtil.TestForPojo(SelectionFormVo.class);
        PojoTestUtil.TestForPojo(PriceHistoryVo.class);
        PojoTestUtil.TestForPojo(ProcurementCostVo.class);
        PojoTestUtil.TestForPojo(MaterialQueryBean.class);
        PojoTestUtil.TestForPojo(ApprovalMaterialEntity.class);
        PojoTestUtil.TestForPojo(MaterialEntity.class);
        PojoTestUtil.TestForPojo(MaterialHistoryEntity.class);
        PojoTestUtil.TestForPojo(MaterialTemporaryEntity.class);
        PojoTestUtil.TestForPojo(PdmInfoEntity.class);
        PojoTestUtil.TestForPojo(OthInfoEntity.class);
        PojoTestUtil.TestForPojo(MaterialVo.class);
        PojoTestUtil.TestForPojo(MaterialDetailVo.class);
        PojoTestUtil.TestForPojo(MaterialVersionVo.class);
        PojoTestUtil.TestForPojo(MaterialAddDto.class);
        PojoTestUtil.TestForPojo(BatchOperateDto.class);
        PojoTestUtil.TestForPojo(MaterialEditDto.class);
        PojoTestUtil.TestForPojo(MaterialBatchAddDto.class);
        PojoTestUtil.TestForPojo(MaterialConditionQueryDto.class);
        PojoTestUtil.TestForPojo(TemplateObj.class);
        PojoTestUtil.TestForPojo(TemplateImportVo.class);
        PojoTestUtil.TestForPojo(ParseVerifyMaterialVo.class);
        PojoTestUtil.TestForPojo(UpdateUserDto.class);
        PojoTestUtil.TestForPojo(UserDto.class);
    }
    @Test
    public void testProductCategoryAddDto() throws Exception {
        PojoTestUtil.TestForPojo(ProductCategoryAddDto.class);
        ProductCategoryAddDto dto1 = new ProductCategoryAddDto();
        dto1.setNodeType(3);
        try {
            dto1.parameterVerification();
        } catch (BusinessException e) {
            Assert.assertEquals("1006",String.valueOf(e.getCode()));
        }
        ProductCategoryAddDto dto2 = new ProductCategoryAddDto();
        try {
            dto2.setProductName("111");
            dto2.setNodeType(6);
            dto2.parameterVerification();
        } catch (BusinessException e) {
            Assert.assertEquals("1006",String.valueOf(e.getCode()));
        }
        ProductCategoryAddDto dto3 = new ProductCategoryAddDto();
        try {
            dto3.setProductName("111");
            dto3.setNodeType(1);
            dto3.setParentId("11");
            dto3.parameterVerification();
        } catch (BusinessException e) {
            Assert.assertEquals("1006",String.valueOf(e.getCode()));
        }
        ProductCategoryAddDto dto4 = new ProductCategoryAddDto();
        try {
            dto4.setProductName("111");
            dto4.setNodeType(2);
            dto4.parameterVerification();
        } catch (BusinessException e) {
            Assert.assertEquals("1006",String.valueOf(e.getCode()));
        }
        ProductCategoryAddDto dto5 = new ProductCategoryAddDto();
        try {
            dto5.setProductName("111");
            dto5.setNodeType(3);
            dto5.parameterVerification();
        } catch (BusinessException e) {
            Assert.assertEquals("1006",String.valueOf(e.getCode()));
        }
        ProductCategoryAddDto dto6 = new ProductCategoryAddDto();
        try {
            dto6.setProductName("supercalifragilisticexpialidocious supercalifragilisticexpialidocious supercalifragilisticexpialidocious");
            dto6.setNodeType(3);
            dto6.parameterVerification();
        } catch (BusinessException e) {
            Assert.assertEquals("1006",String.valueOf(e.getCode()));
        }
    }
    @Test
    public void testProductCategoryEditDto() throws Exception {
        PojoTestUtil.TestForPojo(ProductCategoryEditDto.class);

        ProductCategoryEditDto dto1 = new ProductCategoryEditDto();
        try {
            dto1.setProductName("supercalifragilisticexpialidocious");
            dto1.parameterVerification();
        } catch (BusinessException e) {
            Assert.assertEquals("1006",String.valueOf(e.getCode()));
        }
        ProductCategoryEditDto dto2 = new ProductCategoryEditDto();
        try {
            dto2.setProductName("supercalifragilisticexpialidocious supercalifragilisticexpialidocious supercalifragilisticexpialidocious");
            dto2.setId("369");
            dto2.parameterVerification();
        } catch (BusinessException e) {
            Assert.assertEquals("1006",String.valueOf(e.getCode()));
        }
    }
    @Test
    public void testProductCategoryQueryDto() throws Exception {
        PojoTestUtil.TestForPojo(ProductCategoryQueryDto.class);
        ProductCategoryQueryDto dto = new ProductCategoryQueryDto();
        try {
            dto.parameterVerification();
        } catch (BusinessException e) {
            Assert.assertEquals("1006",String.valueOf(e.getCode()));
        }
    }
    /* Ended by AICoder, pid:w692di1ab20c984149930b44e10f2629f7f5bade */
}
/* Ended by AICoder, pid:11818m8f4chf1181429d08bb10e1c62515e68334 */
