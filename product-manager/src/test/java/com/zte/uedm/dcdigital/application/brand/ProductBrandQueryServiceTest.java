package com.zte.uedm.dcdigital.application.brand;/* Started by AICoder, pid:r763bv3cd7rb206141ed0ad7817bb10c878357c7 */
import com.zte.uedm.dcdigital.application.brand.ProductBrandQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.document.DocumentCitedVo;
import com.zte.uedm.dcdigital.common.bean.document.DocumentInfoVo;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductBrandQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductBrandVo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductBrandQueryServiceTest {

    @Mock
    private ProductBrandQueryService productBrandQueryService;

    @Before
    public void setUp() {
        // 初始化代码（如果需要）
    }

    /**
     * 测试获取新的选型评分。
     */
    @Test
    public void given_productCategoryId_and_selectionAttribute_when_getNewSelectScore_then_returnBigDecimal() {
        when(productBrandQueryService.getNewSelectScore("1", 1)).thenReturn(BigDecimal.ONE);
        assertEquals(BigDecimal.ONE, productBrandQueryService.getNewSelectScore("1", 1));
    }

    /**
     * 测试详细查询品牌和标签列表。
     */
    @Test
    public void given_productBrandQueryDto_when_detailedSearchBrandAndTagList_then_returnPageVO() {
        ProductBrandQueryDto dto = new ProductBrandQueryDto();
        PageVO<ProductBrandVo> pageVO = new PageVO<>(0, Collections.emptyList());
        when(productBrandQueryService.detailedSearchBrandAndTagList(dto)).thenReturn(pageVO);
        assertEquals(pageVO, productBrandQueryService.detailedSearchBrandAndTagList(dto));
    }

    /**
     * 测试模糊搜索品牌列表。
     */
    @Test
    public void given_productBrandQueryDto_when_fuzzySearchBrandList_then_returnPageVO() {
        ProductBrandQueryDto dto = new ProductBrandQueryDto();
        PageVO<ProductBrandVo> pageVO = new PageVO<>(0, Collections.emptyList());
        //when(productBrandQueryService.fuzzySearchBrandList(dto)).thenReturn(pageVO);
        assertEquals(pageVO, productBrandQueryService.fuzzySearchBrandList(dto));
    }

    /**
     * 测试根据品牌ID查询品牌详情。
     */
    @Test
    public void given_brandId_when_getBrandAndTagDetail_then_returnProductBrandVo() {
        ProductBrandVo vo = new ProductBrandVo();
        when(productBrandQueryService.getBrandAndTagDetail("1")).thenReturn(vo);
        assertEquals(vo, productBrandQueryService.getBrandAndTagDetail("1"));
    }

    /**
     * 测试根据品牌ID查询品牌关联的文档列表。
     */
    @Test
    public void given_brandId_when_queryBrandAssociatedDocumentList_then_returnListOfDocumentInfoVo() {
        List<DocumentInfoVo> docs = Arrays.asList(new DocumentInfoVo(), new DocumentInfoVo());
        when(productBrandQueryService.queryBrandAssociatedDocumentList("1")).thenReturn(docs);
        assertEquals(docs, productBrandQueryService.queryBrandAssociatedDocumentList("1"));
    }

    /**
     * 测试判断品牌名称是否重复。
     */
    @Test
    public void given_productCategoryId_and_brandName_when_judgingConditionBrandName_then_returnBoolean() {
        when(productBrandQueryService.judgingConditionBrandName("1", "Brand A","tag")).thenReturn(true);
        assertEquals(true, productBrandQueryService.judgingConditionBrandName("1", "Brand A","tag"));
    }

    /**
     * 测试通过ID列表选择文档。
     */
    @Test
    public void given_ids_when_selectByIds_then_returnListOfDocumentCitedVo() {
        List<String> ids = Arrays.asList("1", "2");
        List<DocumentCitedVo> docs = Arrays.asList(new DocumentCitedVo(), new DocumentCitedVo());
        when(productBrandQueryService.selectByIds(ids)).thenReturn(docs);
        assertEquals(docs, productBrandQueryService.selectByIds(ids));
    }
}

/* Ended by AICoder, pid:r763bv3cd7rb206141ed0ad7817bb10c878357c7 */