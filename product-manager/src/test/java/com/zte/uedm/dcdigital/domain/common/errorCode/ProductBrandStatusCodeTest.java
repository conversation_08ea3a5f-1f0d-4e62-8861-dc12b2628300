package com.zte.uedm.dcdigital.domain.common.errorCode;/* Started by AICoder, pid:7d440z62c5r3d3814d7509ae50a2973626b194bc */
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductBrandStatusCode;
import static org.junit.Assert.*;

import org.junit.Test;

public class ProductBrandStatusCodeTest {

    @Test
    public void testGetCodeAndMessage() {
        // 测试错误码和消息是否正确设置
        assertEquals(Integer.valueOf(1901), ProductBrandStatusCode.BRAND_NAME_ALREADY_EXISTS.getCode());
        assertEquals("The brand + label combination already exists", ProductBrandStatusCode.BRAND_NAME_ALREADY_EXISTS.getMessage());

        assertEquals(Integer.valueOf(1902), ProductBrandStatusCode.BRAND_SCORE_NOT_PRESCRIBED_RANGE.getCode());
        assertEquals("Brand score is not within the prescribed range", ProductBrandStatusCode.BRAND_SCORE_NOT_PRESCRIBED_RANGE.getMessage());

        assertEquals(Integer.valueOf(1903), ProductBrandStatusCode.BRAND_PARAMETER_MISSING.getCode());
        assertEquals("The required brand parameter is missing", ProductBrandStatusCode.BRAND_PARAMETER_MISSING.getMessage());
    }

    @Test
    public void testEnumValues() {
        // 确保枚举值的数量正确
        assertEquals(3, ProductBrandStatusCode.values().length);

        // 检查每个枚举值是否按预期定义
        assertTrue(ProductBrandStatusCode.valueOf("BRAND_NAME_ALREADY_EXISTS") == ProductBrandStatusCode.BRAND_NAME_ALREADY_EXISTS);
        assertTrue(ProductBrandStatusCode.valueOf("BRAND_SCORE_NOT_PRESCRIBED_RANGE") == ProductBrandStatusCode.BRAND_SCORE_NOT_PRESCRIBED_RANGE);
        assertTrue(ProductBrandStatusCode.valueOf("BRAND_PARAMETER_MISSING") == ProductBrandStatusCode.BRAND_PARAMETER_MISSING);
    }
}

/* Ended by AICoder, pid:7d440z62c5r3d3814d7509ae50a2973626b194bc */