package com.zte.uedm.dcdigital.domain.service.impl;


import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.constant.GlobalConstants;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductGroupStatusCode;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductCategoryEntity;
import com.zte.uedm.dcdigital.domain.model.product.entity.ProductGroupEntity;
import com.zte.uedm.dcdigital.domain.repository.ProductCategoryRepository;
import com.zte.uedm.dcdigital.domain.repository.ProductGroupRepository;
import com.zte.uedm.dcdigital.domain.service.MaterialDomainService;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupAddDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupEditDto;
import com.zte.uedm.dcdigital.interfaces.web.product.dto.ProductGroupQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductGroupDetailVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductGroupVo;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.security.util.PermissionUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.function.Executable;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;


@ExtendWith(MockitoExtension.class)
public class ProductGroupServiceImplTest {

    @InjectMocks
    private ProductGroupServiceImpl productGroupServiceImpl;

    @Mock
    private ProductGroupRepository productGroupRepository;

    @Mock
    private ProductCategoryRepository productCategoryRepository;

    @Mock
    private AuthService authService;

    @Mock
    private MaterialDomainService materialDomainService;

    @Mock
    private PermissionUtil permissionUtil;

    private ProductGroupAddDto addDto;
    private ProductGroupVo productGroupVo;
    private ProductGroupEntity productGroupEntity;
    private ProductGroupEntity parentGroupEntity;

    private ProductGroupEditDto editDto;

    private static final int GROUP_LEVEL_1 = 1;

    @Before
    public void setUp() throws Exception{
        MockitoAnnotations.initMocks(this);
        addDto = new ProductGroupAddDto();
        addDto.setName("Test Group");
        addDto.setParentId("123");
        addDto.setProductCategoryId("456");

        productGroupVo = new ProductGroupVo();
        productGroupVo.setId("1");
        productGroupVo.setName("Group");
        productGroupVo.setParentId("123");

        productGroupEntity = new ProductGroupEntity();
        productGroupEntity.setId("1");
        productGroupEntity.setName("Group");
        productGroupEntity.setParentId("123");
        productGroupEntity.setGroupLevel(GROUP_LEVEL_1);
        productGroupEntity.setPathName("Group");
        productGroupEntity.setPathId("1");

        parentGroupEntity = new ProductGroupEntity();
        parentGroupEntity.setId("123");
        parentGroupEntity.setName("Group");
        parentGroupEntity.setGroupLevel(GROUP_LEVEL_1);
        parentGroupEntity.setPathName("Group");
        parentGroupEntity.setPathId("123");

        editDto = new ProductGroupEditDto();
        editDto.setId("1");
        editDto.setName("Group");
        editDto.setPdmModelSpec("spec");


    }

    /* Started by AICoder, pid:f4893g55143a288146ef09426011ae180a494b84 */
    @Test
    public void testverifyLevelValidityTest() throws Exception {
        Method method = ProductGroupServiceImpl.class.getDeclaredMethod("verifyLevelValidity", String.class);
        method.setAccessible(true);
        Mockito.when(productGroupRepository.selectProductGroupById(Mockito.anyString())).thenReturn(null);
        Executable executable = () -> {
            try {
                method.invoke(productGroupServiceImpl, "123");
            } catch (InvocationTargetException e) {
                throw e.getCause(); // 重新抛出原始异常
            }
        };
        assertThrows(BusinessException.class, executable);
        ProductGroupEntity productGroupEntity = new ProductGroupEntity();
        productGroupEntity.setGroupLevel(3);
        Mockito.when(productGroupRepository.selectProductGroupById(Mockito.anyString())).thenReturn(productGroupEntity);
        Executable executable1 = () -> {
            try {
                method.invoke(productGroupServiceImpl, "123");
            } catch (InvocationTargetException e) {
                throw e.getCause(); // 重新抛出原始异常
            }
        };
        assertThrows(BusinessException.class, executable1);
    }
    /* Ended by AICoder, pid:f4893g55143a288146ef09426011ae180a494b84 */

    /* Started by AICoder, pid:t49dcyd97e2157014368091000fae34b6860b590 */
    @Test
    public void verifyGroupValidityTest() throws Exception {
        Method method = ProductGroupServiceImpl.class.getDeclaredMethod("verifyGroupValidity", List.class, String.class, String.class);
        method.setAccessible(true);
        List<ProductGroupEntity> list1 = new ArrayList<>();
        productGroupEntity.setParentId(null);
        list1.add(productGroupEntity);
        Executable executable1 = () -> {
            try {
                method.invoke(productGroupServiceImpl, list1, "Group", null);
            } catch (InvocationTargetException e) {
                throw e.getCause(); // 重新抛出原始异常
            }
        };
        assertThrows(BusinessException.class, executable1);
        productGroupEntity.setParentId("123");
        List<ProductGroupEntity> list2 = new ArrayList<>();
        list2.add(productGroupEntity);
        Executable executable2 = () -> {
            try {
                method.invoke(productGroupServiceImpl, list2, "Group", "123");
            } catch (InvocationTargetException e) {
                throw e.getCause(); // 重新抛出原始异常
            }
        };
        assertThrows(BusinessException.class, executable2);
//        productGroupEntity.setId("123");
//        productGroupEntity.setParentId("1");
//        productGroupEntity.setName("Group");
//        List<ProductGroupEntity> list3 = new ArrayList<>();
//        list3.add(productGroupEntity);
//        Executable executable3 = () -> {
//            try {
//                method.invoke(productGroupServiceImpl, list3, "Group", "123");
//            } catch (InvocationTargetException e) {
//                throw e.getCause(); // 重新抛出原始异常
//            }
//        };
//        assertThrows(BusinessException.class, executable3);
    }
    /* Ended by AICoder, pid:t49dcyd97e2157014368091000fae34b6860b590 */

    @Test
    public void generatePathTest() throws Exception {
        Method method = ProductGroupServiceImpl.class.getDeclaredMethod("generatePath", String.class,String.class);
        method.setAccessible(true);
        Object invoke = method.invoke(productGroupServiceImpl, "123", "123");
        Assert.assertEquals("123/123", invoke);
    }


    /* Started by AICoder, pid:f7ac5h8c11yf83414d9d0935600161207d742641 */
    @Test
    public void saveAndSetPathIdTest() throws Exception {
        Method method = ProductGroupServiceImpl.class.getDeclaredMethod("saveAndSetPathId", ProductGroupEntity.class, ProductGroupEntity.class, boolean.class);
        method.setAccessible(true);
        Mockito.when(productGroupRepository.saveOrUpdateProductGroup(productGroupEntity, false)).thenReturn(null);
        Executable executable3 = () -> {
            try {
                method.invoke(productGroupServiceImpl, productGroupEntity, parentGroupEntity, false);
            } catch (InvocationTargetException e) {
                throw e.getCause(); // 重新抛出原始异常
            }
        };
        assertThrows(BusinessException.class, executable3);
        Mockito.when(productGroupRepository.saveOrUpdateProductGroup(productGroupEntity, false)).thenReturn(productGroupEntity);
        Mockito.when(productGroupRepository.saveOrUpdateProductGroup(productGroupEntity, true)).thenReturn(productGroupEntity);
        Object invoke = method.invoke(productGroupServiceImpl, productGroupEntity, null, false);
        Assert.assertEquals(productGroupEntity, invoke);

        Mockito.when(productGroupRepository.saveOrUpdateProductGroup(productGroupEntity, false)).thenReturn(productGroupEntity);
        Mockito.when(productGroupRepository.saveOrUpdateProductGroup(productGroupEntity, true)).thenReturn(productGroupEntity);
        Mockito.when(materialDomainService.updateMaterialByGroupId(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Object invoke1 = method.invoke(productGroupServiceImpl, productGroupEntity, parentGroupEntity, false);
        Assert.assertEquals(productGroupEntity, invoke1);
    }
    /* Ended by AICoder, pid:f7ac5h8c11yf83414d9d0935600161207d742641 */

    /* Started by AICoder, pid:2e4d2a965fw00e714a900af0007112076749181d */
    @Test
    public void updateMaterialGroupTest() throws Exception {
        Method method = ProductGroupServiceImpl.class.getDeclaredMethod("updateMaterialGroup", ProductGroupEntity.class, ProductGroupEntity.class);
        method.setAccessible(true);
        String oldId = parentGroupEntity.getId();
        String newId = productGroupEntity.getId();
        method.invoke(productGroupServiceImpl, productGroupEntity, parentGroupEntity);
        Mockito.verify(materialDomainService, Mockito.times(1)).updateMaterialByGroupId(oldId, newId);
    }
    /* Ended by AICoder, pid:2e4d2a965fw00e714a900af0007112076749181d */

    /* Started by AICoder, pid:r7ec1lf7eee4e1814c15093b1039834d84d75b03 */
    @Test
    public void assemblyGroupEntityTest() throws Exception {
        Method method = ProductGroupServiceImpl.class.getDeclaredMethod("assemblyGroupEntity", ProductGroupAddDto.class, boolean.class);
        method.setAccessible(true);
        Mockito.when(productGroupRepository.selectProductGroupById(Mockito.anyString())).thenReturn(null);
        ProductCategoryEntity productCategoryEntity = new ProductCategoryEntity();
        productCategoryEntity.setPathId("1");
        productCategoryEntity.setPathName("1");
        Mockito.when(productCategoryRepository.queryById(Mockito.any())).thenReturn(productCategoryEntity);
        Executable executable3 = () -> {
            try {
                method.invoke(productGroupServiceImpl, addDto, true);
            } catch (InvocationTargetException e) {
                throw e.getCause(); // 重新抛出原始异常
            }
        };
        assertThrows(BusinessException.class, executable3);
        Mockito.when(productCategoryRepository.queryById(Mockito.any())).thenReturn(null);
        Executable executable1 = () -> {
            try {
                method.invoke(productGroupServiceImpl, addDto, true);
            } catch (InvocationTargetException e) {
                throw e.getCause(); // 重新抛出原始异常
            }
        };
        assertThrows(BusinessException.class, executable1);
        addDto.setParentId(null);
        productGroupEntity.setGroupLevel(GlobalConstants.GROUP_LEVEL_1);
        productGroupEntity.setPathName(productGroupEntity.getName());
        productGroupEntity.setPathId("1");
        productGroupEntity.setParentId(null);
        Mockito.when(productCategoryRepository.queryById(Mockito.any())).thenReturn(productCategoryEntity);
        Mockito.when(productGroupRepository.saveOrUpdateProductGroup(Mockito.any(), Mockito.anyBoolean())).thenReturn(productGroupEntity);
        Object invoke = method.invoke(productGroupServiceImpl, addDto, true);
        Assert.assertEquals(productGroupEntity, invoke);

        addDto.setParentId("1234");
        productGroupEntity.setGroupLevel(GlobalConstants.GROUP_LEVEL_2);
        productGroupEntity.setPathName(productGroupEntity.getName());
        productGroupEntity.setPathId("1");
        productGroupEntity.setParentId(null);
        Mockito.when(productCategoryRepository.queryById(Mockito.any())).thenReturn(productCategoryEntity);
        Mockito.when(productGroupRepository.selectProductGroupById(addDto.getParentId())).thenReturn(parentGroupEntity);
        Mockito.when(productGroupRepository.saveOrUpdateProductGroup(Mockito.any(), Mockito.anyBoolean())).thenReturn(productGroupEntity);
        Object invoke1 = method.invoke(productGroupServiceImpl, addDto, true);
        Assert.assertEquals(productGroupEntity, invoke1);
    }
    /* Ended by AICoder, pid:r7ec1lf7eee4e1814c15093b1039834d84d75b03 */

    @Test
    public void getGroupLevelTest() throws Exception{
        Method method = ProductGroupServiceImpl.class.getDeclaredMethod("getGroupLevel", Integer.class);
        method.setAccessible(true);
        Executable executable3 = () -> {
            try {
                method.invoke(productGroupServiceImpl,4);
            } catch (InvocationTargetException e) {
                throw e.getCause(); // 重新抛出原始异常
            }
        };
        assertThrows(BusinessException.class, executable3);
        Object invoke = method.invoke(productGroupServiceImpl, 1);
        Assert.assertEquals(2, invoke);
        Object invoke1 = method.invoke(productGroupServiceImpl, 2);
        Assert.assertEquals(3, invoke1);
    }

    /* Started by AICoder, pid:d9f02c3568o96ba14cbc08b9907d93177e696de3 */
    @Test
    public void checkOutOfPendingApprovalTest() throws Exception {
        Method method = ProductGroupServiceImpl.class.getDeclaredMethod("checkOutOfPendingApproval", ProductGroupAddDto.class);
        method.setAccessible(true);
        ProductGroupAddDto dto = new ProductGroupAddDto();
        dto.setParentId("123");
        Mockito.when(productGroupRepository.queryMaterialByGroupId(dto.getParentId())).thenReturn(productGroupEntity);
        Executable executable = () -> {
            try {
                method.invoke(productGroupServiceImpl, dto);
            } catch (InvocationTargetException e) {
                throw e.getCause(); // 重新抛出原始异常
            }
        };
        assertThrows(BusinessException.class, executable);
        Mockito.when(productGroupRepository.queryMaterialByGroupId(dto.getParentId())).thenReturn(null);
        Object invoke = method.invoke(productGroupServiceImpl, dto);
        Assert.assertEquals(false, invoke);
    }
    /* Ended by AICoder, pid:d9f02c3568o96ba14cbc08b9907d93177e696de3 */

    /* Started by AICoder, pid:la814gac746d5b7141a20a9760198f1e55b94ea6 */
    @Test
    public void addTest() throws Exception {
        List<ProductGroupEntity> list = new ArrayList<>();
        list.add(productGroupEntity);
        productGroupEntity.setParentId("1234");
        ProductCategoryEntity productCategoryEntity = new ProductCategoryEntity();
        productCategoryEntity.setPathId("1");
        productCategoryEntity.setPathName("1");

        Mockito.when(productGroupRepository.selectProductGroupById(Mockito.anyString())).thenReturn(productGroupEntity);
        Mockito.when(productGroupRepository.selectByNameAndProductCategoryId(Mockito.anyString(), Mockito.anyString())).thenReturn(list);
        Mockito.when(productGroupRepository.queryMaterialByGroupId(addDto.getParentId())).thenReturn(null);
        Mockito.when(productCategoryRepository.queryById(Mockito.any())).thenReturn(productCategoryEntity);
        Mockito.when(productGroupRepository.saveOrUpdateProductGroup(Mockito.any(), Mockito.anyBoolean())).thenReturn(productGroupEntity);
        Mockito.when(authService.getUserId()).thenReturn("123");

        addDto.setParentId(null);
        ProductGroupVo groupVo = productGroupServiceImpl.add(addDto);
        Assert.assertEquals("1", groupVo.getId());
    }
    /* Ended by AICoder, pid:la814gac746d5b7141a20a9760198f1e55b94ea6 */

    /* Started by AICoder, pid:k89e2uca109459714df2083d2074e61d89e754ba */

    @Test
    public void editTest() throws Exception {
        Mockito.when(productGroupRepository.selectProductGroupById(editDto.getId())).thenReturn(null);
        try {
            ProductGroupVo edit = productGroupServiceImpl.edit(editDto);
        } catch (BusinessException e) {
            Assert.assertEquals(ProductGroupStatusCode.NODE_NOT_EXIST.getCode(), e.getCode());
        }

        Mockito.when(productGroupRepository.selectProductGroupById(editDto.getId())).thenReturn(productGroupEntity);
        Mockito.when(productGroupRepository.selectByNameAndProductCategoryId(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Mockito.when(productGroupRepository.saveOrUpdateProductGroup(Mockito.any(),Mockito.anyBoolean())).thenReturn(null);
        Mockito.when(authService.getUserId()).thenReturn("123");
        Mockito.doNothing().when(permissionUtil).checkPermission(Mockito.anyString(), Mockito.any());
        /* Started by AICoder, pid:ua50bg0658943c2148200b4d80ad9e1cc7c5fa4e */
        try {
            productGroupServiceImpl.edit(editDto);
        } catch (BusinessException e) {
            Assert.assertEquals(StatusCode.DATABASE_OPERATION_EXCEPTION.getCode(), e.getCode());
        }

        Mockito.when(productGroupRepository.selectProductGroupById(editDto.getId())).thenReturn(productGroupEntity);
        Mockito.when(productGroupRepository.selectByNameAndProductCategoryId(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        Mockito.when(productGroupRepository.saveOrUpdateProductGroup(Mockito.any(), Mockito.anyBoolean())).thenReturn(productGroupEntity);
        Mockito.when(authService.getUserId()).thenReturn("123");
        List<ProductGroupEntity> entityList = new ArrayList<>();
        entityList.add(productGroupEntity);
        Mockito.when(productGroupRepository.selectCurentNodeAllChildNode(Mockito.anyString())).thenReturn(entityList);
        Mockito.doNothing().when(productGroupRepository).updateBatch(entityList);
        editDto.setName("Group1");
        /* Ended by AICoder, pid:ua50bg0658943c2148200b4d80ad9e1cc7c5fa4e */
        ProductGroupVo groupVo = productGroupServiceImpl.edit(editDto);
        Assert.assertEquals("1", groupVo.getId());
    }
    /* Ended by AICoder, pid:k89e2uca109459714df2083d2074e61d89e754ba */


    @Test
    public void deleteTest() throws Exception{
        String id = "1";
        List<ProductGroupEntity> list = new ArrayList<>();
        list.add(productGroupEntity);
        Mockito.when(productGroupRepository.selectProductGroupByParentId(id)).thenReturn(list);
        Mockito.doNothing().when(permissionUtil).checkPermission(Mockito.anyString(), Mockito.any());
        try{
            productGroupServiceImpl.delete(id);
        }catch (BusinessException e){
            Assert.assertEquals(ProductGroupStatusCode.NODE_NOT_EXIST.getCode(), e.getCode());
        }

        Mockito.when(productGroupRepository.selectProductGroupByParentId(id)).thenReturn(null);
        Mockito.when(productGroupRepository.selectProductGroupById(id)).thenReturn(null);
        try{
            productGroupServiceImpl.delete(id);
        }catch (BusinessException e){
            Assert.assertEquals(ProductGroupStatusCode.NODE_NOT_EXIST.getCode(), e.getCode());
        }

        Mockito.when(productGroupRepository.selectProductGroupByParentId(id)).thenReturn(null);
        Mockito.when(productGroupRepository.selectProductGroupById(id)).thenReturn(productGroupEntity);
        Mockito.when(productGroupRepository.selectProductGroupAndMaterialByGroupId(id)).thenReturn(list);
        try{
            productGroupServiceImpl.delete(id);
        }catch (BusinessException e){
            Assert.assertEquals(ProductGroupStatusCode.NODE_EXIST_MATERIAL.getCode(), e.getCode());
        }

        /* Started by AICoder, pid:ud4e9q3b22237b21408b0897606f940ab8d4deaa */
        Mockito.when(productGroupRepository.selectProductGroupByParentId(id)).thenReturn(null);
        Mockito.when(productGroupRepository.selectProductGroupById(id)).thenReturn(productGroupEntity);
        Mockito.when(productGroupRepository.selectProductGroupAndMaterialByGroupId(id)).thenReturn(null);
        Mockito.when(productGroupRepository.deleteProductGroupById(id)).thenReturn(0);
        /* Ended by AICoder, pid:ud4e9q3b22237b21408b0897606f940ab8d4deaa */
        try{
            productGroupServiceImpl.delete(id);
        }catch (BusinessException e){
            Assert.assertEquals(StatusCode.DATABASE_OPERATION_EXCEPTION.getCode(), e.getCode());
        }

        /* Started by AICoder, pid:hd4e9k3b22l37b21408b0897606f940ab8d4deaa */
        Mockito.when(productGroupRepository.selectProductGroupByParentId(id)).thenReturn(null);
        Mockito.when(productGroupRepository.selectProductGroupById(id)).thenReturn(productGroupEntity);
        Mockito.when(productGroupRepository.selectProductGroupAndMaterialByGroupId(id)).thenReturn(null);
        Mockito.when(productGroupRepository.deleteProductGroupById(id)).thenReturn(1);
        /* Ended by AICoder, pid:hd4e9k3b22l37b21408b0897606f940ab8d4deaa */
        int delete = productGroupServiceImpl.delete(id);
        Assert.assertEquals(1, delete);
    }

    @Test
    public void queryProductGroupsTest() throws Exception{
        ProductGroupQueryDto queryDto = new ProductGroupQueryDto();
        queryDto.setGroupName("test");
        List<ProductGroupEntity> groupEntities = new ArrayList<>();
        ProductGroupEntity rootEntity = new ProductGroupEntity();
        rootEntity.setId("1");
        rootEntity.setName("Root Group");
        rootEntity.setPdmModelSpec("Model A");
        rootEntity.setParentId(null);  // 根节点没有父节点
        rootEntity.setPathName("");
        rootEntity.setPathId("1");
        rootEntity.setCreateTime("2024-12-01 10:00:00");
        rootEntity.setUpdateTime("2024-12-01 10:00:00");
        rootEntity.setCreateBy("admin");
        rootEntity.setUpdateBy("admin");
        ProductGroupEntity child1Entity = new ProductGroupEntity();
        child1Entity.setId("2");
        child1Entity.setName("Child Group 1");
        child1Entity.setPdmModelSpec("Model B");
        child1Entity.setParentId("1");  // 父节点是根节点
        child1Entity.setPathName("Root Group");
        child1Entity.setPathId("1/2");
        child1Entity.setCreateTime("2024-12-02 11:00:00");
        child1Entity.setUpdateTime("2024-12-02 11:00:00");
        child1Entity.setCreateBy("user1");
        child1Entity.setUpdateBy("user1");
        groupEntities.add(rootEntity);
        groupEntities.add(child1Entity);
        Mockito.when(productGroupRepository.queryPorductGroups(queryDto)).thenReturn(null);
        List<ProductGroupVo> groupVos = productGroupServiceImpl.queryProductGroups(queryDto);
        Assert.assertEquals(0, groupVos.size());
        ProductCategoryEntity productCategoryEntity = new ProductCategoryEntity();
        productCategoryEntity.setPathId("1");
        productCategoryEntity.setPathName("1");
        Mockito.when(productGroupRepository.queryPorductGroups(queryDto)).thenReturn(groupEntities);
        Mockito.when(productCategoryRepository.queryById(Mockito.any())).thenReturn(productCategoryEntity);
        Mockito.when(productGroupRepository.selectByIds(Mockito.any())).thenReturn(groupEntities);
        List<ProductGroupVo> groupVos1 = productGroupServiceImpl.queryProductGroups(queryDto);
        Assert.assertEquals(1, groupVos1.size());

    }


    /* Started by AICoder, pid:ufae6p58can681f149320b14d0dbd607d459171d */
    @Test
    public void createParamTest() throws Exception {
        Method method = ProductGroupServiceImpl.class.getDeclaredMethod("createParam", ProductGroupEntity.class, boolean.class);
        method.setAccessible(true);
        Mockito.when(authService.getUserId()).thenReturn("123");
        ProductGroupEntity groupEntity = new ProductGroupEntity();
        method.invoke(productGroupServiceImpl, groupEntity, true);
        assertNotNull(groupEntity.getId());
    }
    /* Ended by AICoder, pid:ufae6p58can681f149320b14d0dbd607d459171d */

    /* Started by AICoder, pid:vbf9ak79ffgcefb14cd50a0f3081331597729307 */
    @Test
    public void queryProductGroupDetailTest() throws Exception {
        Mockito.when(productGroupRepository.queryProductGroupDetail(Mockito.anyString())).thenReturn(null);
        try {
            productGroupServiceImpl.queryProductGroupDetail("1");
        } catch (BusinessException e) {
            Assert.assertEquals(ProductGroupStatusCode.NODE_NOT_EXIST.getCode(), e.getCode());
        }
        Mockito.when(productGroupRepository.queryProductGroupDetail(Mockito.anyString())).thenReturn(productGroupEntity);
        ProductGroupDetailVo groupDetailVo = productGroupServiceImpl.queryProductGroupDetail("1");
        Assert.assertEquals("1", groupDetailVo.getId());
    }
    /* Ended by AICoder, pid:vbf9ak79ffgcefb14cd50a0f3081331597729307 */
}
