package com.zte.uedm.dcdigital.application.brand.executor.Impl;
/* Started by AICoder, pid:w09966783728f26141700802f13f5f14d0f57fec */
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DocumentCategoryRepository;
import com.zte.uedm.dcdigital.domain.common.valueobj.DocumentCategoryObj;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentCategoryAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentCategoryEditDto;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DocumentCategoryCommandServiceImplTest {

    @InjectMocks
    private DocumentCategoryCommandServiceImpl documentCategoryCommandService;

    @Mock
    private DocumentCategoryRepository documentCategoryRepository;

    @Mock
    private AuthService authService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // 测试添加文档分类方法（正常情况）
    @Test
    public void given_AddDTO_when_AddDocumentCategory_then_CategoryIsAdded() {
        // Given
        DocumentCategoryAddDto addDTO = new DocumentCategoryAddDto();
        addDTO.setName("testName");
        addDTO.setType(1);

        // When
        documentCategoryCommandService.addDocumentCategory(addDTO);

        // Then
        verify(documentCategoryRepository, times(1)).addDocumentCategory(any(DocumentCategoryObj.class));
        ArgumentCaptor<DocumentCategoryObj> captor = ArgumentCaptor.forClass(DocumentCategoryObj.class);
        verify(documentCategoryRepository).addDocumentCategory(captor.capture());
        DocumentCategoryObj entity = captor.getValue();
        assertNotNull(entity.getCreateTime());
        assertNotNull(entity.getUpdateTime());
    }

    @Test
    public void given_AddDTO_when_AddDocumentCategory_then_exception() {
        // Given
        DocumentCategoryAddDto addDTO = new DocumentCategoryAddDto();
        addDTO.setName("testName");
        addDTO.setType(0);
        // When
        assertThrows(BusinessException.class,()->documentCategoryCommandService.addDocumentCategory(addDTO));
        addDTO.setName("testNameXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX");
        assertThrows(BusinessException.class,()->documentCategoryCommandService.addDocumentCategory(addDTO));
    }

    // 测试更新文档分类方法（正常情况）
    @Test
    public void given_EditDTO_when_UpdateDocumentCategory_then_CategoryIsUpdated() throws BusinessException {
        // Given
        DocumentCategoryEditDto editDTO = new DocumentCategoryEditDto();
        editDTO.setId("1");
        editDTO.setName("updatedName");
        editDTO.setType(2);

        DocumentCategoryObj existingEntity = new DocumentCategoryObj();
        existingEntity.setId("1");

        when(documentCategoryRepository.queryDocumentCategory("1")).thenReturn(existingEntity);

        // When
        documentCategoryCommandService.updateDocumentCategory(editDTO);

        // Then
        verify(documentCategoryRepository, times(1)).updateDocumentCategory(any(DocumentCategoryObj.class));
        ArgumentCaptor<DocumentCategoryObj> captor = ArgumentCaptor.forClass(DocumentCategoryObj.class);
        verify(documentCategoryRepository).updateDocumentCategory(captor.capture());
        DocumentCategoryObj updatedEntity = captor.getValue();
        assertEquals("updatedName", updatedEntity.getName());
        assertEquals(2, updatedEntity.getType().intValue());
        assertNotNull(updatedEntity.getUpdateTime());
    }

    // 测试更新文档分类方法（文档分类不存在）
    @Test(expected = BusinessException.class)
    public void given_NonExistentId_when_UpdateDocumentCategory_then_ThrowBusinessException() throws BusinessException {
        // Given
        DocumentCategoryEditDto editDTO = new DocumentCategoryEditDto();
        editDTO.setId("nonexistent");

        when(documentCategoryRepository.queryDocumentCategory("nonexistent")).thenReturn(null);

        // When & Then
        documentCategoryCommandService.updateDocumentCategory(editDTO);
    }

    // 测试删除文档分类方法
    @Test
    public void given_Id_when_DeleteDocumentCategory_then_CategoryIsDeleted() {
        // Given
        String id = "1";

        // When
        documentCategoryCommandService.deleteDocumentCategory(id);

        // Then
        verify(documentCategoryRepository, times(1)).deleteDocumentCategory(id);
    }
}

/* Ended by AICoder, pid:w09966783728f26141700802f13f5f14d0f57fec */
